{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-59:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5e5330ea37f1d0a4ed46f4a86900feb4\\transformed\\material-1.11.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,415,493,591,680,780,899,982,1047,1140,1210,1269,1359,1423,1492,1550,1619,1679,1743,1855,1914,1973,2028,2103,2226,2306,2390,2523,2605,2686,2817,2904,2986,3044,3100,3166,3241,3321,3406,3485,3552,3627,3704,3768,3875,3969,4039,4128,4221,4295,4370,4460,4516,4595,4662,4746,4830,4892,4956,5019,5085,5185,5292,5386,5494,5556,5616", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,130,86,81,57,55,65,74,79,84,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79", "endOffsets": "254,332,410,488,586,675,775,894,977,1042,1135,1205,1264,1354,1418,1487,1545,1614,1674,1738,1850,1909,1968,2023,2098,2221,2301,2385,2518,2600,2681,2812,2899,2981,3039,3095,3161,3236,3316,3401,3480,3547,3622,3699,3763,3870,3964,4034,4123,4216,4290,4365,4455,4511,4590,4657,4741,4825,4887,4951,5014,5080,5180,5287,5381,5489,5551,5611,5691"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "770,3656,3734,3812,3890,3988,4798,4898,5017,5100,8897,8990,9060,9119,9209,9273,9342,9400,9469,9529,9593,9705,9764,9823,9878,9953,10076,10156,10240,10373,10455,10536,10667,10754,10836,10894,10950,11016,11091,11171,11256,11335,11402,11477,11554,11618,11725,11819,11889,11978,12071,12145,12220,12310,12366,12445,12512,12596,12680,12742,12806,12869,12935,13035,13142,13236,13344,13406,13693", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177", "endColumns": "12,77,77,77,97,88,99,118,82,64,92,69,58,89,63,68,57,68,59,63,111,58,58,54,74,122,79,83,132,81,80,130,86,81,57,55,65,74,79,84,78,66,74,76,63,106,93,69,88,92,73,74,89,55,78,66,83,83,61,63,62,65,99,106,93,107,61,59,79", "endOffsets": "924,3729,3807,3885,3983,4072,4893,5012,5095,5160,8985,9055,9114,9204,9268,9337,9395,9464,9524,9588,9700,9759,9818,9873,9948,10071,10151,10235,10368,10450,10531,10662,10749,10831,10889,10945,11011,11086,11166,11251,11330,11397,11472,11549,11613,11720,11814,11884,11973,12066,12140,12215,12305,12361,12440,12507,12591,12675,12737,12801,12864,12930,13030,13137,13231,13339,13401,13461,13768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16b8dac02a484144e9b877600396f6d3\\transformed\\exoplayer-ui-2.19.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,675,763,854,939,1034,1129,1197,1259,1348,1437,1507,1572,1634,1702,1812,1924,2033,2107,2188,2258,2326,2412,2501,2565,2628,2681,2739,2787,2848,2908,2977,3037,3100,3160,3223,3288,3351,3417,3470,3527,3598,3669", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "277,483,670,758,849,934,1029,1124,1192,1254,1343,1432,1502,1567,1629,1697,1807,1919,2028,2102,2183,2253,2321,2407,2496,2560,2623,2676,2734,2782,2843,2903,2972,3032,3095,3155,3218,3283,3346,3412,3465,3522,3593,3664,3718"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,377,583,5165,5253,5344,5429,5524,5619,5687,5749,5838,5927,5997,6062,6124,6192,6302,6414,6523,6597,6678,6748,6816,6902,6991,7055,7802,7855,7913,7961,8022,8082,8151,8211,8274,8334,8397,8462,8525,8591,8644,8701,8772,8843", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,62,65,52,56,70,70,53", "endOffsets": "372,578,765,5248,5339,5424,5519,5614,5682,5744,5833,5922,5992,6057,6119,6187,6297,6409,6518,6592,6673,6743,6811,6897,6986,7050,7113,7850,7908,7956,8017,8077,8146,8206,8269,8329,8392,8457,8520,8586,8639,8696,8767,8838,8892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\944d23d0a2a0a4de0e44ec3c7bd88e96\\transformed\\exoplayer-core-2.19.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,266,336,413,484,575,660", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "122,189,261,331,408,479,570,655,734"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7118,7190,7257,7329,7399,7476,7547,7638,7723", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "7185,7252,7324,7394,7471,7542,7633,7718,7797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3f319f1baa5473100673c9c69aa9ac79\\transformed\\core-1.16.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "55,56,57,58,59,60,61,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4077,4175,4277,4379,4483,4586,4684,13859", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "4170,4272,4374,4478,4581,4679,4793,13955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f743c2e5f2a61bc0a20ac96fdd74bd6f\\transformed\\navigation-ui-2.7.6\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,114", "endOffsets": "162,277"}, "to": {"startLines": "175,176", "startColumns": "4,4", "startOffsets": "13466,13578", "endColumns": "111,114", "endOffsets": "13573,13688"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46864e7f8e3972dd5b88a3c5321cb854\\transformed\\appcompat-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "929,1043,1149,1258,1344,1448,1568,1645,1720,1812,1906,2001,2095,2196,2290,2386,2480,2572,2664,2749,2857,2963,3065,3176,3277,3393,3558,13773", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "1038,1144,1253,1339,1443,1563,1640,1715,1807,1901,1996,2090,2191,2285,2381,2475,2567,2659,2744,2852,2958,3060,3171,3272,3388,3553,3651,13854"}}]}]}