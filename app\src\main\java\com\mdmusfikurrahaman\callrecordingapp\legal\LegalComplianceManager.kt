package com.mdmusfikurrahaman.callrecordingapp.legal

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository
import java.util.*

/**
 * Manager for legal compliance features
 * 
 * This class handles:
 * - Legal disclaimer acceptance tracking
 * - Consent dialog management
 * - Privacy policy compliance
 * - Recording legality warnings
 * - Audit trail for legal purposes
 */
@Singleton
class LegalComplianceManager @Inject constructor(
    private val context: Context,
    private val repository: CallRecordingRepository
) {
    
    companion object {
        private const val LEGAL_PREFS_NAME = "legal_compliance_prefs"
        private const val KEY_DISCLAIMER_ACCEPTED = "disclaimer_accepted"
        private const val KEY_DISCLAIMER_VERSION = "disclaimer_version"
        private const val KEY_DISCLAIMER_TIMESTAMP = "disclaimer_timestamp"
        private const val KEY_PRIVACY_POLICY_ACCEPTED = "privacy_policy_accepted"
        private const val KEY_PRIVACY_POLICY_VERSION = "privacy_policy_version"
        private const val KEY_CONSENT_DIALOG_ENABLED = "consent_dialog_enabled"
        private const val KEY_JURISDICTION_WARNING_SHOWN = "jurisdiction_warning_shown"
        
        private const val CURRENT_DISCLAIMER_VERSION = 1
        private const val CURRENT_PRIVACY_POLICY_VERSION = 1
    }
    
    private val encryptedPrefs: SharedPreferences by lazy {
        createEncryptedPreferences()
    }
    
    private val _complianceState = MutableStateFlow(ComplianceState())
    val complianceState: StateFlow<ComplianceState> = _complianceState.asStateFlow()
    
    init {
        loadComplianceState()
    }
    
    /**
     * Create encrypted shared preferences for sensitive legal data
     */
    private fun createEncryptedPreferences(): SharedPreferences {
        return try {
            val masterKey = MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build()
            
            EncryptedSharedPreferences.create(
                context,
                LEGAL_PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )
        } catch (e: Exception) {
            Timber.e(e, "Failed to create encrypted preferences, falling back to regular preferences")
            context.getSharedPreferences(LEGAL_PREFS_NAME, Context.MODE_PRIVATE)
        }
    }
    
    /**
     * Load current compliance state
     */
    private fun loadComplianceState() {
        try {
            val disclaimerAccepted = encryptedPrefs.getBoolean(KEY_DISCLAIMER_ACCEPTED, false)
            val disclaimerVersion = encryptedPrefs.getInt(KEY_DISCLAIMER_VERSION, 0)
            val disclaimerTimestamp = encryptedPrefs.getLong(KEY_DISCLAIMER_TIMESTAMP, 0L)
            val privacyPolicyAccepted = encryptedPrefs.getBoolean(KEY_PRIVACY_POLICY_ACCEPTED, false)
            val privacyPolicyVersion = encryptedPrefs.getInt(KEY_PRIVACY_POLICY_VERSION, 0)
            val consentDialogEnabled = encryptedPrefs.getBoolean(KEY_CONSENT_DIALOG_ENABLED, true)
            val jurisdictionWarningShown = encryptedPrefs.getBoolean(KEY_JURISDICTION_WARNING_SHOWN, false)
            
            _complianceState.value = ComplianceState(
                disclaimerAccepted = disclaimerAccepted && disclaimerVersion >= CURRENT_DISCLAIMER_VERSION,
                disclaimerVersion = disclaimerVersion,
                disclaimerTimestamp = disclaimerTimestamp,
                privacyPolicyAccepted = privacyPolicyAccepted && privacyPolicyVersion >= CURRENT_PRIVACY_POLICY_VERSION,
                privacyPolicyVersion = privacyPolicyVersion,
                consentDialogEnabled = consentDialogEnabled,
                jurisdictionWarningShown = jurisdictionWarningShown
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Error loading compliance state")
        }
    }
    
    /**
     * Accept legal disclaimer
     */
    suspend fun acceptLegalDisclaimer(): Boolean {
        return try {
            val timestamp = System.currentTimeMillis()
            
            encryptedPrefs.edit()
                .putBoolean(KEY_DISCLAIMER_ACCEPTED, true)
                .putInt(KEY_DISCLAIMER_VERSION, CURRENT_DISCLAIMER_VERSION)
                .putLong(KEY_DISCLAIMER_TIMESTAMP, timestamp)
                .apply()
            
            // Also update in repository
            repository.acceptLegalDisclaimer()
            
            // Update state
            _complianceState.value = _complianceState.value.copy(
                disclaimerAccepted = true,
                disclaimerVersion = CURRENT_DISCLAIMER_VERSION,
                disclaimerTimestamp = timestamp
            )
            
            // Log acceptance for audit trail
            logLegalEvent("DISCLAIMER_ACCEPTED", "version=$CURRENT_DISCLAIMER_VERSION")
            
            true
        } catch (e: Exception) {
            Timber.e(e, "Error accepting legal disclaimer")
            false
        }
    }
    
    /**
     * Accept privacy policy
     */
    suspend fun acceptPrivacyPolicy(): Boolean {
        return try {
            val timestamp = System.currentTimeMillis()
            
            encryptedPrefs.edit()
                .putBoolean(KEY_PRIVACY_POLICY_ACCEPTED, true)
                .putInt(KEY_PRIVACY_POLICY_VERSION, CURRENT_PRIVACY_POLICY_VERSION)
                .apply()
            
            // Also update in repository
            repository.acceptPrivacyPolicy()
            
            // Update state
            _complianceState.value = _complianceState.value.copy(
                privacyPolicyAccepted = true,
                privacyPolicyVersion = CURRENT_PRIVACY_POLICY_VERSION
            )
            
            // Log acceptance for audit trail
            logLegalEvent("PRIVACY_POLICY_ACCEPTED", "version=$CURRENT_PRIVACY_POLICY_VERSION")
            
            true
        } catch (e: Exception) {
            Timber.e(e, "Error accepting privacy policy")
            false
        }
    }
    
    /**
     * Set consent dialog preference
     */
    fun setConsentDialogEnabled(enabled: Boolean) {
        try {
            encryptedPrefs.edit()
                .putBoolean(KEY_CONSENT_DIALOG_ENABLED, enabled)
                .apply()
            
            _complianceState.value = _complianceState.value.copy(
                consentDialogEnabled = enabled
            )
            
            logLegalEvent("CONSENT_DIALOG_SETTING_CHANGED", "enabled=$enabled")
            
        } catch (e: Exception) {
            Timber.e(e, "Error setting consent dialog preference")
        }
    }
    
    /**
     * Mark jurisdiction warning as shown
     */
    fun markJurisdictionWarningShown() {
        try {
            encryptedPrefs.edit()
                .putBoolean(KEY_JURISDICTION_WARNING_SHOWN, true)
                .apply()
            
            _complianceState.value = _complianceState.value.copy(
                jurisdictionWarningShown = true
            )
            
            logLegalEvent("JURISDICTION_WARNING_SHOWN", "")
            
        } catch (e: Exception) {
            Timber.e(e, "Error marking jurisdiction warning as shown")
        }
    }
    
    /**
     * Check if app can legally record calls
     */
    fun canRecordCalls(): Boolean {
        val state = _complianceState.value
        return state.disclaimerAccepted && state.privacyPolicyAccepted
    }
    
    /**
     * Check if consent dialog should be shown before recording
     */
    fun shouldShowConsentDialog(): Boolean {
        return _complianceState.value.consentDialogEnabled
    }
    
    /**
     * Check if jurisdiction warning should be shown
     */
    fun shouldShowJurisdictionWarning(): Boolean {
        return !_complianceState.value.jurisdictionWarningShown
    }
    
    /**
     * Log consent for a specific call
     */
    suspend fun logCallConsent(phoneNumber: String, consentGiven: Boolean) {
        try {
            val details = "phoneNumber=${phoneNumber.takeLast(4)}, consent=$consentGiven"
            logLegalEvent("CALL_CONSENT_LOGGED", details)
            
            // You might want to store this in the database as well
            // for more permanent audit trail
            
        } catch (e: Exception) {
            Timber.e(e, "Error logging call consent")
        }
    }
    
    /**
     * Get legal compliance summary
     */
    fun getComplianceSummary(): ComplianceSummary {
        val state = _complianceState.value
        
        return ComplianceSummary(
            isFullyCompliant = canRecordCalls(),
            disclaimerStatus = if (state.disclaimerAccepted) {
                "Accepted on ${Date(state.disclaimerTimestamp)}"
            } else {
                "Not accepted"
            },
            privacyPolicyStatus = if (state.privacyPolicyAccepted) {
                "Accepted (v${state.privacyPolicyVersion})"
            } else {
                "Not accepted"
            },
            consentDialogEnabled = state.consentDialogEnabled,
            recommendations = getComplianceRecommendations(state)
        )
    }
    
    /**
     * Get compliance recommendations
     */
    private fun getComplianceRecommendations(state: ComplianceState): List<String> {
        val recommendations = mutableListOf<String>()
        
        if (!state.disclaimerAccepted) {
            recommendations.add("Accept the legal disclaimer to enable recording")
        }
        
        if (!state.privacyPolicyAccepted) {
            recommendations.add("Accept the privacy policy to comply with data protection laws")
        }
        
        if (!state.consentDialogEnabled) {
            recommendations.add("Consider enabling consent dialogs for better legal protection")
        }
        
        if (!state.jurisdictionWarningShown) {
            recommendations.add("Review jurisdiction-specific recording laws")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("All legal requirements are met")
        }
        
        return recommendations
    }
    
    /**
     * Log legal events for audit trail
     */
    private fun logLegalEvent(event: String, details: String) {
        try {
            val timestamp = System.currentTimeMillis()
            val logEntry = "[$timestamp] $event: $details"
            
            // Log to system log (in production, you might want to use a more secure logging mechanism)
            Timber.i("LEGAL_AUDIT: $logEntry")
            
            // You could also store these in a secure database table for audit purposes
            
        } catch (e: Exception) {
            Timber.e(e, "Error logging legal event")
        }
    }
    
    /**
     * Reset all compliance data (for testing or user request)
     */
    suspend fun resetComplianceData(): Boolean {
        return try {
            encryptedPrefs.edit().clear().apply()
            
            _complianceState.value = ComplianceState()
            
            logLegalEvent("COMPLIANCE_DATA_RESET", "user_requested")
            
            true
        } catch (e: Exception) {
            Timber.e(e, "Error resetting compliance data")
            false
        }
    }
}

/**
 * Data class representing the current compliance state
 */
data class ComplianceState(
    val disclaimerAccepted: Boolean = false,
    val disclaimerVersion: Int = 0,
    val disclaimerTimestamp: Long = 0L,
    val privacyPolicyAccepted: Boolean = false,
    val privacyPolicyVersion: Int = 0,
    val consentDialogEnabled: Boolean = true,
    val jurisdictionWarningShown: Boolean = false
)

/**
 * Data class representing a compliance summary
 */
data class ComplianceSummary(
    val isFullyCompliant: Boolean,
    val disclaimerStatus: String,
    val privacyPolicyStatus: String,
    val consentDialogEnabled: Boolean,
    val recommendations: List<String>
)
