<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.mdmusfikurrahaman.callrecordingapp" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="113" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="15" startOffset="8" endLine="21" endOffset="55"/></Target><Target id="@+id/searchEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="46" startOffset="16" endLine="50" endOffset="46"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="55" startOffset="12" endLine="60" endOffset="57"/></Target><Target id="@+id/emptyStateLayout" view="LinearLayout"><Expressions/><location startLine="63" startOffset="12" endLine="97" endOffset="26"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="104" startOffset="4" endLine="111" endOffset="45"/></Target></Targets></Layout>