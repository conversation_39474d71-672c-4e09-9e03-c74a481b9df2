<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2019 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<TextView
  xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/mtrl_picker_header_selection_text"
  style="?attr/materialCalendarHeaderSelection"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:gravity="start|bottom"
  app:firstBaselineToTopHeight="@dimen/mtrl_calendar_selection_text_baseline_to_top"
  app:lineHeight="@dimen/mtrl_calendar_header_selection_line_height"
  app:autoSizeTextType="uniform"
  android:paddingBottom="@dimen/mtrl_calendar_pre_l_text_clip_padding"/>
