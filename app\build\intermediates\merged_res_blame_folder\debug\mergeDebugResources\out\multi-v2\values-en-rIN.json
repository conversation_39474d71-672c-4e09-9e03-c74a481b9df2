{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-64:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,400,514,613,708,820,956,1072,1208,1292,1391,1482,1579,1698,1823,1927,2054,2177,2305,2466,2587,2703,2826,2951,3043,3141,3258,3382,3479,3581,3683,3813,3952,4058,4157,4235,4331,4425,4530,4617,4704,4806,4888,4972,5073,5174,5274,5373,5461,5567,5668,5772,5892,5974,6074", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "168,284,395,509,608,703,815,951,1067,1203,1287,1386,1477,1574,1693,1818,1922,2049,2172,2300,2461,2582,2698,2821,2946,3038,3136,3253,3377,3474,3576,3678,3808,3947,4053,4152,4230,4326,4420,4525,4612,4699,4801,4883,4967,5068,5169,5269,5368,5456,5562,5663,5767,5887,5969,6069,6164"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5714,5832,5948,6059,6173,6272,6367,6479,6615,6731,6867,6951,7050,7141,7238,7357,7482,7586,7713,7836,7964,8125,8246,8362,8485,8610,8702,8800,8917,9041,9138,9240,9342,9472,9611,9717,9816,9894,9990,10084,10189,10276,10363,10465,10547,10631,10732,10833,10933,11032,11120,11226,11327,11431,11551,11633,11733", "endColumns": "117,115,110,113,98,94,111,135,115,135,83,98,90,96,118,124,103,126,122,127,160,120,115,122,124,91,97,116,123,96,101,101,129,138,105,98,77,95,93,104,86,86,101,81,83,100,100,99,98,87,105,100,103,119,81,99,94", "endOffsets": "5827,5943,6054,6168,6267,6362,6474,6610,6726,6862,6946,7045,7136,7233,7352,7477,7581,7708,7831,7959,8120,8241,8357,8480,8605,8697,8795,8912,9036,9133,9235,9337,9467,9606,9712,9811,9889,9985,10079,10184,10271,10358,10460,10542,10626,10727,10828,10928,11027,11115,11221,11322,11426,11546,11628,11728,11823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3781,3851,3913,3978,4042,4119,4184,4274,4359", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "3846,3908,3973,4037,4114,4179,4269,4354,4423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,981,1063,1136,1212,1284,1354,1431,1497", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,976,1058,1131,1207,1279,1349,1426,1492,1613"}, "to": {"startLines": "26,27,28,29,30,82,83,141,142,143,144,145,146,147,148,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1425,1517,1599,1693,1792,5543,5625,11828,11917,12001,12079,12161,12234,12310,12382,12553,12630,12696", "endColumns": "91,81,93,98,86,81,88,88,83,77,81,72,75,71,69,76,65,120", "endOffsets": "1512,1594,1688,1787,1874,5620,5709,11912,11996,12074,12156,12229,12305,12377,12447,12625,12691,12812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3287,3353,3405,3467,3543,3619", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3282,3348,3400,3462,3538,3614,3668"}, "to": {"startLines": "2,11,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,529,1879,1961,2043,2121,2208,2293,2360,2423,2515,2607,2672,2735,2797,2868,2978,3089,3199,3266,3346,3417,3484,3569,3654,3717,4428,4481,4539,4587,4648,4713,4775,4840,4911,4969,5027,5093,5157,5223,5275,5337,5413,5489", "endLines": "10,14,18,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "330,524,701,1956,2038,2116,2203,2288,2355,2418,2510,2602,2667,2730,2792,2863,2973,3084,3194,3261,3341,3412,3479,3564,3649,3712,3776,4476,4534,4582,4643,4708,4770,4835,4906,4964,5022,5088,5152,5218,5270,5332,5408,5484,5538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "12817,12903", "endColumns": "85,84", "endOffsets": "12898,12983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "706,802,904,1003,1102,1206,1309,12452", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "797,899,998,1097,1201,1304,1420,12548"}}]}]}