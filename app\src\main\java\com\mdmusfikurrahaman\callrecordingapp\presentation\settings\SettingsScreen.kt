package com.mdmusfikurrahaman.callrecordingapp.presentation.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.mdmusfikurrahaman.callrecordingapp.R
import com.mdmusfikurrahaman.callrecordingapp.presentation.settings.components.*

/**
 * Settings screen for configuring app preferences
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateBack: () -> Unit,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    
    LaunchedEffect(Unit) {
        viewModel.loadSettings()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.settings)) },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // Recording Settings Section
            item {
                SettingsSection(title = "Recording Settings") {
                    // Auto Recording Toggle
                    SettingsToggleItem(
                        title = "Auto Record Calls",
                        description = "Automatically record all calls",
                        checked = uiState.preferences?.autoRecordEnabled ?: false,
                        onCheckedChange = viewModel::updateAutoRecordEnabled
                    )
                    
                    // Record Incoming Calls
                    SettingsToggleItem(
                        title = "Record Incoming Calls",
                        description = "Record calls you receive",
                        checked = uiState.preferences?.recordIncomingCalls ?: false,
                        onCheckedChange = viewModel::updateRecordIncomingCalls,
                        enabled = uiState.preferences?.autoRecordEnabled ?: false
                    )
                    
                    // Record Outgoing Calls
                    SettingsToggleItem(
                        title = "Record Outgoing Calls",
                        description = "Record calls you make",
                        checked = uiState.preferences?.recordOutgoingCalls ?: false,
                        onCheckedChange = viewModel::updateRecordOutgoingCalls,
                        enabled = uiState.preferences?.autoRecordEnabled ?: false
                    )
                    
                    // Recording Quality
                    SettingsDropdownItem(
                        title = "Recording Quality",
                        description = "Audio quality for recordings",
                        selectedValue = uiState.preferences?.recordingQuality?.displayName ?: "Medium Quality",
                        options = getRecordingQualityOptions(),
                        onValueSelected = { quality ->
                            viewModel.updateRecordingQuality(quality)
                        }
                    )
                }
            }
            
            // Storage Settings Section
            item {
                SettingsSection(title = "Storage Settings") {
                    // Auto Delete Toggle
                    SettingsToggleItem(
                        title = "Auto Delete Old Recordings",
                        description = "Automatically delete recordings after specified days",
                        checked = uiState.preferences?.autoDeleteEnabled ?: false,
                        onCheckedChange = viewModel::updateAutoDeleteEnabled
                    )
                    
                    // Auto Delete Days
                    SettingsSliderItem(
                        title = "Delete After (Days)",
                        description = "Number of days to keep recordings",
                        value = (uiState.preferences?.autoDeleteDays ?: 30).toFloat(),
                        valueRange = 1f..365f,
                        steps = 0,
                        onValueChange = { days ->
                            viewModel.updateAutoDeleteDays(days.toInt())
                        },
                        enabled = uiState.preferences?.autoDeleteEnabled ?: false,
                        valueFormatter = { "${it.toInt()} days" }
                    )
                    
                    // Storage Usage
                    SettingsInfoItem(
                        title = "Storage Used",
                        description = uiState.storageInfo?.let { 
                            "${it.recordingsSpaceMB} MB used"
                        } ?: "Calculating...",
                        onClick = { viewModel.refreshStorageInfo() }
                    )
                }
            }
            
            // Privacy Settings Section
            item {
                SettingsSection(title = "Privacy Settings") {
                    // Show Consent Dialog
                    SettingsToggleItem(
                        title = "Show Consent Dialog",
                        description = "Ask for consent before recording each call",
                        checked = uiState.preferences?.showConsentDialog ?: true,
                        onCheckedChange = viewModel::updateShowConsentDialog
                    )
                    
                    // Recording Indicator
                    SettingsToggleItem(
                        title = "Show Recording Indicator",
                        description = "Display visual indicator during recording",
                        checked = uiState.preferences?.showRecordingIndicator ?: true,
                        onCheckedChange = viewModel::updateShowRecordingIndicator
                    )
                    
                    // Vibrate on Recording Start
                    SettingsToggleItem(
                        title = "Vibrate on Recording Start",
                        description = "Vibrate when recording begins",
                        checked = uiState.preferences?.vibrateOnRecordingStart ?: true,
                        onCheckedChange = viewModel::updateVibrateOnRecordingStart
                    )
                }
            }
            
            // AI Features Section (if enabled)
            item {
                SettingsSection(title = "AI Features") {
                    // AI Transcription Toggle
                    SettingsToggleItem(
                        title = "AI Transcription",
                        description = "Generate transcripts using AI",
                        checked = uiState.preferences?.aiTranscriptionEnabled ?: false,
                        onCheckedChange = viewModel::updateAiTranscriptionEnabled
                    )
                    
                    // Auto Transcribe
                    SettingsToggleItem(
                        title = "Auto Transcribe",
                        description = "Automatically transcribe new recordings",
                        checked = uiState.preferences?.autoTranscribe ?: false,
                        onCheckedChange = viewModel::updateAutoTranscribe,
                        enabled = uiState.preferences?.aiTranscriptionEnabled ?: false
                    )
                }
            }
            
            // Permissions Section
            item {
                SettingsSection(title = "Permissions") {
                    PermissionStatusSection(
                        permissionStatus = uiState.permissionStatus,
                        onRequestPermissions = { viewModel.requestPermissions(context) },
                        onOpenSettings = { viewModel.openAppSettings(context) }
                    )
                }
            }
            
            // About Section
            item {
                SettingsSection(title = "About") {
                    SettingsClickableItem(
                        title = "Privacy Policy",
                        description = "View our privacy policy",
                        onClick = { viewModel.openPrivacyPolicy(context) }
                    )
                    
                    SettingsClickableItem(
                        title = "Legal Disclaimer",
                        description = "Important legal information",
                        onClick = { viewModel.showLegalDisclaimer() }
                    )
                    
                    SettingsInfoItem(
                        title = "App Version",
                        description = uiState.appVersion
                    )
                }
            }
        }
    }
    
    // Legal Disclaimer Dialog
    if (uiState.showLegalDisclaimer) {
        LegalDisclaimerDialog(
            onDismiss = { viewModel.dismissLegalDisclaimer() },
            onAccept = { viewModel.acceptLegalDisclaimer() }
        )
    }
}

private fun getRecordingQualityOptions(): List<String> {
    return listOf("Low Quality", "Medium Quality", "High Quality", "Very High Quality")
}
