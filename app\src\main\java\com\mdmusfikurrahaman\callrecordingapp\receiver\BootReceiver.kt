package com.mdmusfikurrahaman.callrecordingapp.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import timber.log.Timber
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

/**
 * BroadcastReceiver for handling device boot and app updates
 * 
 * This receiver ensures that the app's services and components are
 * properly initialized after device reboot or app updates.
 */
class BootReceiver : BroadcastReceiver() {
    
    private val receiverScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            when (intent.action) {
                Intent.ACTION_BOOT_COMPLETED -> {
                    handleBootCompleted(context)
                }
                Intent.ACTION_MY_PACKAGE_REPLACED,
                Intent.ACTION_PACKAGE_REPLACED -> {
                    if (intent.dataString?.contains(context.packageName) == true) {
                        handleAppUpdated(context)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error in BootReceiver")
        }
    }
    
    private fun handleBootCompleted(context: Context) {
        receiverScope.launch {
            try {
                Timber.d("Device boot completed - initializing app components")
                
                // Initialize app components if needed
                initializeAppComponents(context)
                
                // Clean up any temporary files
                cleanupTempFiles(context)
                
                Timber.d("Boot initialization completed")
                
            } catch (e: Exception) {
                Timber.e(e, "Error during boot initialization")
            }
        }
    }
    
    private fun handleAppUpdated(context: Context) {
        receiverScope.launch {
            try {
                Timber.d("App updated - reinitializing components")
                
                // Reinitialize app components after update
                initializeAppComponents(context)
                
                // Perform any migration tasks if needed
                performMigrationTasks(context)
                
                Timber.d("App update initialization completed")
                
            } catch (e: Exception) {
                Timber.e(e, "Error during app update initialization")
            }
        }
    }
    
    private suspend fun initializeAppComponents(context: Context) {
        try {
            // This would initialize any necessary app components
            // For example, checking permissions, updating preferences, etc.
            
            Timber.d("App components initialized")
            
        } catch (e: Exception) {
            Timber.e(e, "Error initializing app components")
        }
    }
    
    private suspend fun cleanupTempFiles(context: Context) {
        try {
            // Clean up any temporary recording files that might be left over
            // This would use the FileManager to clean up temp files
            
            Timber.d("Temporary files cleaned up")
            
        } catch (e: Exception) {
            Timber.e(e, "Error cleaning up temporary files")
        }
    }
    
    private suspend fun performMigrationTasks(context: Context) {
        try {
            // Perform any necessary migration tasks after app update
            // For example, database migrations, preference updates, etc.
            
            Timber.d("Migration tasks completed")
            
        } catch (e: Exception) {
            Timber.e(e, "Error performing migration tasks")
        }
    }
}
