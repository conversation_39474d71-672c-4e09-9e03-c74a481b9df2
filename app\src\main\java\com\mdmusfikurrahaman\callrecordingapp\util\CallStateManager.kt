package com.mdmusfikurrahaman.callrecordingapp.util

import timber.log.Timber
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType
import java.util.concurrent.ConcurrentHashMap

/**
 * Singleton manager for tracking call states
 * 
 * This class maintains the current call state and helps coordinate
 * between different components that need to track call information.
 */
object CallStateManager {
    
    private var currentCallInfo: CallInfo? = null
    private val incomingCalls = ConcurrentHashMap<String, Long>() // phoneNumber -> timestamp
    private val outgoingCalls = ConcurrentHashMap<String, Long>() // phoneNumber -> timestamp
    
    /**
     * Data class representing current call information
     */
    data class CallInfo(
        val phoneNumber: String,
        val callType: CallType,
        val startTime: Long,
        val isActive: Boolean = false
    )
    
    /**
     * Set an incoming call
     */
    fun setIncomingCall(phoneNumber: String) {
        val timestamp = System.currentTimeMillis()
        incomingCalls[phoneNumber] = timestamp
        
        Timber.d("Incoming call registered: $phoneNumber")
    }
    
    /**
     * Set an outgoing call
     */
    fun setOutgoingCall(phoneNumber: String) {
        val timestamp = System.currentTimeMillis()
        outgoingCalls[phoneNumber] = timestamp
        
        Timber.d("Outgoing call registered: $phoneNumber")
    }
    
    /**
     * Set a call as active
     */
    fun setCallActive(phoneNumber: String, callType: CallType) {
        currentCallInfo = CallInfo(
            phoneNumber = phoneNumber,
            callType = callType,
            startTime = System.currentTimeMillis(),
            isActive = true
        )
        
        Timber.d("Call set as active: $phoneNumber ($callType)")
    }
    
    /**
     * Clear the current call state
     */
    fun clearCallState() {
        val previousCall = currentCallInfo
        currentCallInfo = null
        
        // Clean up old entries (keep only recent ones)
        cleanupOldEntries()
        
        if (previousCall != null) {
            Timber.d("Call state cleared: ${previousCall.phoneNumber}")
        }
    }
    
    /**
     * Get current call information
     */
    fun getCurrentCallInfo(): CallInfo? = currentCallInfo
    
    /**
     * Check if there's an active call
     */
    fun hasActiveCall(): Boolean = currentCallInfo?.isActive == true
    
    /**
     * Check if a phone number corresponds to an incoming call
     */
    fun isIncomingCall(phoneNumber: String): Boolean {
        val incomingTime = incomingCalls[phoneNumber]
        val outgoingTime = outgoingCalls[phoneNumber]
        
        return when {
            incomingTime == null && outgoingTime == null -> false
            incomingTime != null && outgoingTime == null -> true
            incomingTime == null && outgoingTime != null -> false
            else -> {
                // Both exist, use the more recent one
                incomingTime!! > outgoingTime!!
            }
        }
    }
    
    /**
     * Check if a phone number corresponds to an outgoing call
     */
    fun isOutgoingCall(phoneNumber: String): Boolean {
        return !isIncomingCall(phoneNumber) && outgoingCalls.containsKey(phoneNumber)
    }
    
    /**
     * Get the call type for a phone number
     */
    fun getCallType(phoneNumber: String): CallType {
        return if (isIncomingCall(phoneNumber)) {
            CallType.INCOMING
        } else {
            CallType.OUTGOING
        }
    }
    
    /**
     * Get call duration if there's an active call
     */
    fun getCurrentCallDuration(): Long {
        val callInfo = currentCallInfo
        return if (callInfo != null && callInfo.isActive) {
            System.currentTimeMillis() - callInfo.startTime
        } else {
            0L
        }
    }
    
    /**
     * Clean up old call entries to prevent memory leaks
     */
    private fun cleanupOldEntries() {
        val cutoffTime = System.currentTimeMillis() - (5 * 60 * 1000L) // 5 minutes ago
        
        val incomingToRemove = incomingCalls.filter { it.value < cutoffTime }.keys
        val outgoingToRemove = outgoingCalls.filter { it.value < cutoffTime }.keys
        
        incomingToRemove.forEach { incomingCalls.remove(it) }
        outgoingToRemove.forEach { outgoingCalls.remove(it) }
        
        if (incomingToRemove.isNotEmpty() || outgoingToRemove.isNotEmpty()) {
            Timber.d("Cleaned up ${incomingToRemove.size + outgoingToRemove.size} old call entries")
        }
    }
    
    /**
     * Get debug information about current state
     */
    fun getDebugInfo(): String {
        return buildString {
            appendLine("CallStateManager Debug Info:")
            appendLine("Current Call: $currentCallInfo")
            appendLine("Incoming Calls: ${incomingCalls.size}")
            appendLine("Outgoing Calls: ${outgoingCalls.size}")
            
            if (incomingCalls.isNotEmpty()) {
                appendLine("Recent Incoming:")
                incomingCalls.forEach { (number, time) ->
                    appendLine("  $number -> ${System.currentTimeMillis() - time}ms ago")
                }
            }
            
            if (outgoingCalls.isNotEmpty()) {
                appendLine("Recent Outgoing:")
                outgoingCalls.forEach { (number, time) ->
                    appendLine("  $number -> ${System.currentTimeMillis() - time}ms ago")
                }
            }
        }
    }
}
