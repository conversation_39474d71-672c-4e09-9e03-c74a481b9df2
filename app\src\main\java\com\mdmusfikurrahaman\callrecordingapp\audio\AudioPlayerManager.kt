package com.mdmusfikurrahaman.callrecordingapp.audio

import android.content.Context
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.net.Uri
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for audio playback functionality
 * 
 * This class handles:
 * - Audio file playback with MediaPlayer
 * - Playback state management
 * - Position tracking and seeking
 * - Playback speed control
 * - Error handling and recovery
 */
@Singleton
class AudioPlayerManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private var mediaPlayer: MediaPlayer? = null
    private var positionUpdateJob: Job? = null
    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // State flows
    private val _isPlaying = MutableStateFlow(false)
    val isPlaying: StateFlow<Boolean> = _isPlaying.asStateFlow()
    
    private val _currentPosition = MutableStateFlow(0L)
    val currentPosition: StateFlow<Long> = _currentPosition.asStateFlow()
    
    private val _duration = MutableStateFlow(0L)
    val duration: StateFlow<Long> = _duration.asStateFlow()
    
    private val _playbackSpeed = MutableStateFlow(1.0f)
    val playbackSpeed: StateFlow<Float> = _playbackSpeed.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    /**
     * Prepare audio file for playback
     */
    suspend fun prepareAudio(filePath: String): Boolean = withContext(Dispatchers.IO) {
        try {
            _isLoading.value = true
            _error.value = null
            
            // Release any existing player
            release()
            
            val file = File(filePath)
            if (!file.exists()) {
                _error.value = "Audio file not found"
                return@withContext false
            }
            
            mediaPlayer = MediaPlayer().apply {
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .build()
                )
                
                setDataSource(context, Uri.fromFile(file))
                
                setOnPreparedListener { player ->
                    _duration.value = player.duration.toLong()
                    _isLoading.value = false
                    Timber.d("Audio prepared: duration=${player.duration}ms")
                }
                
                setOnCompletionListener {
                    _isPlaying.value = false
                    _currentPosition.value = 0L
                    stopPositionUpdates()
                    Timber.d("Audio playback completed")
                }
                
                setOnErrorListener { _, what, extra ->
                    val errorMsg = "MediaPlayer error: what=$what, extra=$extra"
                    Timber.e(errorMsg)
                    _error.value = errorMsg
                    _isPlaying.value = false
                    _isLoading.value = false
                    true
                }
                
                prepareAsync()
            }
            
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Error preparing audio")
            _error.value = "Error preparing audio: ${e.message}"
            _isLoading.value = false
            false
        }
    }
    
    /**
     * Start or resume playback
     */
    suspend fun play(): Boolean = withContext(Dispatchers.Main) {
        try {
            val player = mediaPlayer ?: return@withContext false
            
            if (!player.isPlaying) {
                player.start()
                _isPlaying.value = true
                startPositionUpdates()
                Timber.d("Audio playback started")
            }
            
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Error starting playback")
            _error.value = "Error starting playback: ${e.message}"
            false
        }
    }
    
    /**
     * Pause playback
     */
    suspend fun pause(): Boolean = withContext(Dispatchers.Main) {
        try {
            val player = mediaPlayer ?: return@withContext false
            
            if (player.isPlaying) {
                player.pause()
                _isPlaying.value = false
                stopPositionUpdates()
                Timber.d("Audio playback paused")
            }
            
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Error pausing playback")
            _error.value = "Error pausing playback: ${e.message}"
            false
        }
    }
    
    /**
     * Stop playback and reset position
     */
    suspend fun stop(): Boolean = withContext(Dispatchers.Main) {
        try {
            val player = mediaPlayer ?: return@withContext false
            
            if (player.isPlaying) {
                player.stop()
            }
            
            _isPlaying.value = false
            _currentPosition.value = 0L
            stopPositionUpdates()
            
            // Prepare again for next playback
            player.prepareAsync()
            
            Timber.d("Audio playback stopped")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Error stopping playback")
            _error.value = "Error stopping playback: ${e.message}"
            false
        }
    }
    
    /**
     * Seek to specific position
     */
    suspend fun seekTo(positionMs: Long): Boolean = withContext(Dispatchers.Main) {
        try {
            val player = mediaPlayer ?: return@withContext false
            
            val clampedPosition = positionMs.coerceIn(0L, _duration.value)
            player.seekTo(clampedPosition.toInt())
            _currentPosition.value = clampedPosition
            
            Timber.d("Seeked to position: ${clampedPosition}ms")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Error seeking")
            _error.value = "Error seeking: ${e.message}"
            false
        }
    }
    
    /**
     * Set playback speed (Android 6.0+)
     */
    suspend fun setPlaybackSpeed(speed: Float): Boolean = withContext(Dispatchers.Main) {
        try {
            val player = mediaPlayer ?: return@withContext false
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val playbackParams = player.playbackParams
                playbackParams.speed = speed.coerceIn(0.25f, 3.0f)
                player.playbackParams = playbackParams
                _playbackSpeed.value = speed
                
                Timber.d("Playback speed set to: ${speed}x")
                true
            } else {
                _error.value = "Playback speed control not supported on this device"
                false
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error setting playback speed")
            _error.value = "Error setting playback speed: ${e.message}"
            false
        }
    }
    
    /**
     * Release resources
     */
    suspend fun release() = withContext(Dispatchers.Main) {
        try {
            stopPositionUpdates()
            
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
            }
            mediaPlayer = null
            
            _isPlaying.value = false
            _currentPosition.value = 0L
            _duration.value = 0L
            _playbackSpeed.value = 1.0f
            _isLoading.value = false
            _error.value = null
            
            Timber.d("Audio player released")
            
        } catch (e: Exception) {
            Timber.e(e, "Error releasing audio player")
        }
    }
    
    /**
     * Start periodic position updates
     */
    private fun startPositionUpdates() {
        stopPositionUpdates()
        
        positionUpdateJob = coroutineScope.launch {
            while (isActive && _isPlaying.value) {
                try {
                    val player = mediaPlayer
                    if (player != null && player.isPlaying) {
                        _currentPosition.value = player.currentPosition.toLong()
                    }
                } catch (e: Exception) {
                    Timber.w(e, "Error updating position")
                }
                
                delay(100) // Update every 100ms
            }
        }
    }
    
    /**
     * Stop position updates
     */
    private fun stopPositionUpdates() {
        positionUpdateJob?.cancel()
        positionUpdateJob = null
    }
    
    /**
     * Get current playback state
     */
    fun getPlaybackState(): PlaybackState {
        return PlaybackState(
            isPlaying = _isPlaying.value,
            currentPosition = _currentPosition.value,
            duration = _duration.value,
            playbackSpeed = _playbackSpeed.value,
            isLoading = _isLoading.value,
            error = _error.value
        )
    }
    
    /**
     * Clear error state
     */
    fun clearError() {
        _error.value = null
    }
}

/**
 * Data class representing the current playback state
 */
data class PlaybackState(
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val duration: Long = 0L,
    val playbackSpeed: Float = 1.0f,
    val isLoading: Boolean = false,
    val error: String? = null
)
