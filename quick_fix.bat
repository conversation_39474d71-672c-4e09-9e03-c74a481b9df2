@echo off
echo ========================================
echo Android Studio Module Fix
echo ========================================
echo.

echo Step 1: Cleaning Gradle cache...
call gradlew clean --console=plain

echo.
echo Step 2: Refreshing dependencies...
call gradlew --refresh-dependencies

echo.
echo Step 3: Building project...
call gradlew build --console=plain

echo.
echo ========================================
echo Fix completed! Now try running in Android Studio.
echo ========================================
echo.
echo Instructions:
echo 1. Open Android Studio
echo 2. File → Sync Project with Gradle Files
echo 3. Run → Edit Configurations → Select 'app' module
echo 4. Click Run button
echo.
pause
