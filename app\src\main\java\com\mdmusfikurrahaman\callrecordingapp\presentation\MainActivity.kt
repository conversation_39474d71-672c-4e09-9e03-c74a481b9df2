package com.mdmusfikurrahaman.callrecordingapp.presentation

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import dagger.hilt.android.AndroidEntryPoint
import com.mdmusfikurrahaman.callrecordingapp.presentation.theme.CallRecordingAppTheme
import com.mdmusfikurrahaman.callrecordingapp.presentation.main.MainScreen
import com.mdmusfikurrahaman.callrecordingapp.permission.PermissionFlowHandler
import javax.inject.Inject

/**
 * Main activity for the Call Recording App
 * 
 * This activity serves as the entry point and hosts the main navigation
 * and permission management flows.
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var permissionFlowHandler: Permission<PERSON>lowHandler
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Initialize permission flow handler
        permissionFlowHandler.initialize(this)
        
        enableEdgeToEdge()
        
        setContent {
            CallRecordingAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen()
                }
            }
        }
    }
}
