C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\repository\CallRecordingRepository.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\settings\SettingsActivity.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\theme\Type.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\util\ContactUtils.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\di\LegalModule.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\CallRecordingApplication.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\entity\CallRecording.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\util\FileManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\error\ErrorHandler.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\MainActivity.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\detail\components\TranscriptCard.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\settings\SettingsScreen.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\main\components\PermissionRequestDialog.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\entity\CallTranscript.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\ui\theme\Color.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\util\DateTimeUtils.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\audio\AudioPlayerManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\util\CallStateManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\settings\components\SettingsComponents.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\di\ErrorHandlingModule.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\legal\ConsentDialogManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\detail\RecordingDetailViewModel.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\error\AppError.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\main\MainScreen.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\ui\theme\Theme.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\permission\PermissionFlowHandler.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\util\StorageManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\settings\SettingsViewModel.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\MaintenanceService.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\recording\LegacyAudioSourceRecordingMethod.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\MainActivity.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\CallRecordingService.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\detail\components\RecordingMetadataCard.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\settings\components\LegalDisclaimerDialog.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\receiver\PhoneStateReceiver.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\converter\DatabaseConverters.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\dao\CallRecordingDao.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\di\DatabaseModule.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\permission\PermissionManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\compatibility\DeviceCompatibilityChecker.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\di\ServiceModule.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\CallRecordingAccessibilityService.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\detail\RecordingDetailScreen.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\legal\LegalComplianceManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\settings\components\PermissionStatusSection.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\recording\RecordingMethod.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\recording\MediaProjectionRecordingMethod.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\main\components\RecordingItem.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\main\MainViewModel.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\dao\UserPreferencesDao.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\theme\Color.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\recording\RecordingMethodManager.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\receiver\BootReceiver.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\detail\components\AudioPlayerCard.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\dao\CallTranscriptDao.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\detail\RecordingDetailActivity.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\main\components\SearchBar.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\util\PermissionUtils.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\service\recording\AccessibilityRecordingMethod.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\ui\theme\Type.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\CallRecordingDatabase.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\main\components\EmptyRecordingsView.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\receiver\CallLogReceiver.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\data\database\entity\UserPreferences.kt
C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\java\com\mdmusfikurrahaman\callrecordingapp\presentation\theme\Theme.kt