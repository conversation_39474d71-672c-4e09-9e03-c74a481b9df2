package com.mdmusfikurrahaman.callrecordingapp.service

import android.accessibilityservice.AccessibilityService
import android.content.Intent
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import timber.log.Timber
import kotlinx.coroutines.*
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType

/**
 * AccessibilityService for detecting call states and triggering recordings
 * 
 * This service acts as a fallback method for call recording when other methods fail.
 * It monitors accessibility events from the phone app to detect when calls start and end.
 * 
 * Note: This service requires explicit user permission through the accessibility settings.
 * It should only be used as a fallback when MediaProjection or other methods are not available.
 */
class CallRecordingAccessibilityService : AccessibilityService() {
    
    companion object {
        private const val PHONE_PACKAGE = "com.android.server.telecom"
        private const val DIALER_PACKAGE = "com.android.dialer"
        private const val SAMSUNG_INCALL_PACKAGE = "com.samsung.android.incallui"
        private const val GOOGLE_DIALER_PACKAGE = "com.google.android.dialer"
        
        // Call state indicators
        private val CALL_ACTIVE_INDICATORS = listOf(
            "end call", "hang up", "disconnect", "end", "terminate",
            "ongoing call", "in call", "call in progress"
        )
        
        private val CALL_INCOMING_INDICATORS = listOf(
            "incoming call", "answer", "accept", "decline", "reject"
        )
        
        private val CALL_OUTGOING_INDICATORS = listOf(
            "calling", "dialing", "outgoing call"
        )
        
        @Volatile
        var isServiceRunning = false
            private set
    }
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private var isCallActive = false
    private var currentPhoneNumber: String? = null
    private var callStartTime = 0L
    
    override fun onServiceConnected() {
        super.onServiceConnected()
        isServiceRunning = true
        Timber.d("CallRecordingAccessibilityService connected")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false
        serviceScope.cancel()
        Timber.d("CallRecordingAccessibilityService destroyed")
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event ?: return
        
        try {
            // Only process events from phone/dialer apps
            if (!isPhoneRelatedPackage(event.packageName?.toString())) {
                return
            }
            
            when (event.eventType) {
                AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
                    handleWindowStateChanged(event)
                }
                AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
                    handleWindowContentChanged(event)
                }
                AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED -> {
                    handleNotificationStateChanged(event)
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error processing accessibility event")
        }
    }
    
    override fun onInterrupt() {
        Timber.d("CallRecordingAccessibilityService interrupted")
    }
    
    private fun handleWindowStateChanged(event: AccessibilityEvent) {
        val className = event.className?.toString() ?: return
        
        // Detect call-related activities
        if (className.contains("InCallActivity") || 
            className.contains("CallActivity") ||
            className.contains("DialerActivity")) {
            
            serviceScope.launch {
                delay(1000) // Wait for UI to stabilize
                analyzeCallState(event.source)
            }
        }
    }
    
    private fun handleWindowContentChanged(event: AccessibilityEvent) {
        if (isCallActive) {
            // Monitor for call end indicators
            serviceScope.launch {
                delay(500)
                checkForCallEnd(event.source)
            }
        }
    }
    
    private fun handleNotificationStateChanged(event: AccessibilityEvent) {
        val text = event.text?.joinToString(" ") ?: return
        
        // Check for incoming call notifications
        if (text.contains("incoming call", ignoreCase = true) ||
            text.contains("calling", ignoreCase = true)) {
            
            extractPhoneNumberFromText(text)?.let { phoneNumber ->
                currentPhoneNumber = phoneNumber
            }
        }
    }
    
    private suspend fun analyzeCallState(rootNode: AccessibilityNodeInfo?) {
        rootNode ?: return
        
        try {
            val allText = extractAllText(rootNode).lowercase()
            
            when {
                containsAny(allText, CALL_INCOMING_INDICATORS) && !isCallActive -> {
                    handleIncomingCall(allText)
                }
                containsAny(allText, CALL_OUTGOING_INDICATORS) && !isCallActive -> {
                    handleOutgoingCall(allText)
                }
                containsAny(allText, CALL_ACTIVE_INDICATORS) && !isCallActive -> {
                    handleCallStart(allText)
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error analyzing call state")
        } finally {
            rootNode.recycle()
        }
    }
    
    private suspend fun checkForCallEnd(rootNode: AccessibilityNodeInfo?) {
        rootNode ?: return
        
        try {
            val allText = extractAllText(rootNode).lowercase()
            
            // If we don't see call indicators anymore, the call might have ended
            if (!containsAny(allText, CALL_ACTIVE_INDICATORS) && 
                !containsAny(allText, CALL_INCOMING_INDICATORS) &&
                !containsAny(allText, CALL_OUTGOING_INDICATORS)) {
                
                handleCallEnd()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error checking for call end")
        } finally {
            rootNode.recycle()
        }
    }
    
    private fun handleIncomingCall(text: String) {
        if (isCallActive) return
        
        Timber.d("Detected incoming call")
        currentPhoneNumber = extractPhoneNumberFromText(text)
        
        // Don't start recording yet, wait for call to be answered
    }
    
    private fun handleOutgoingCall(text: String) {
        if (isCallActive) return
        
        Timber.d("Detected outgoing call")
        currentPhoneNumber = extractPhoneNumberFromText(text)
        
        // Start recording for outgoing calls
        startCallRecording(CallType.OUTGOING)
    }
    
    private fun handleCallStart(text: String) {
        if (isCallActive) return
        
        Timber.d("Detected call start")
        
        // Determine call type based on previous state
        val callType = if (currentPhoneNumber != null) {
            CallType.INCOMING // We detected an incoming call earlier
        } else {
            CallType.OUTGOING // Assume outgoing if no previous detection
        }
        
        startCallRecording(callType)
    }
    
    private fun handleCallEnd() {
        if (!isCallActive) return
        
        Timber.d("Detected call end")
        stopCallRecording()
    }
    
    private fun startCallRecording(callType: CallType) {
        if (isCallActive) return
        
        isCallActive = true
        callStartTime = System.currentTimeMillis()
        
        val phoneNumber = currentPhoneNumber ?: "Unknown"
        
        try {
            CallRecordingService.startRecording(
                context = this,
                phoneNumber = phoneNumber,
                contactName = null, // Could be enhanced to lookup contact name
                callType = callType
            )
            
            Timber.d("Started call recording via AccessibilityService")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start call recording")
            isCallActive = false
        }
    }
    
    private fun stopCallRecording() {
        if (!isCallActive) return
        
        isCallActive = false
        currentPhoneNumber = null
        callStartTime = 0L
        
        try {
            CallRecordingService.stopRecording(this)
            Timber.d("Stopped call recording via AccessibilityService")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to stop call recording")
        }
    }
    
    private fun extractAllText(node: AccessibilityNodeInfo): String {
        val textBuilder = StringBuilder()
        
        fun traverseNode(currentNode: AccessibilityNodeInfo) {
            currentNode.text?.let { text ->
                textBuilder.append(text).append(" ")
            }
            
            currentNode.contentDescription?.let { desc ->
                textBuilder.append(desc).append(" ")
            }
            
            for (i in 0 until currentNode.childCount) {
                currentNode.getChild(i)?.let { child ->
                    traverseNode(child)
                    child.recycle()
                }
            }
        }
        
        traverseNode(node)
        return textBuilder.toString()
    }
    
    private fun extractPhoneNumberFromText(text: String): String? {
        // Simple regex to extract phone numbers
        val phoneRegex = Regex("""[\+]?[1-9]?[\d\s\-\(\)]{7,15}""")
        return phoneRegex.find(text)?.value?.replace(Regex("""[\s\-\(\)]"""), "")
    }
    
    private fun containsAny(text: String, indicators: List<String>): Boolean {
        return indicators.any { indicator ->
            text.contains(indicator, ignoreCase = true)
        }
    }
    
    private fun isPhoneRelatedPackage(packageName: String?): Boolean {
        packageName ?: return false
        
        return packageName.contains("phone") ||
                packageName.contains("dialer") ||
                packageName.contains("call") ||
                packageName.contains("telecom") ||
                packageName == PHONE_PACKAGE ||
                packageName == DIALER_PACKAGE ||
                packageName == SAMSUNG_INCALL_PACKAGE ||
                packageName == GOOGLE_DIALER_PACKAGE
    }
}
