package com.mdmusfikurrahaman.callrecordingapp.service.recording

import android.content.Context
import android.os.Build
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import java.io.File

/**
 * Manager class that handles different recording methods and selects the best one
 * 
 * This class:
 * - Maintains a list of available recording methods
 * - Selects the best method based on device capabilities and permissions
 * - Provides fallback mechanisms when the primary method fails
 */
@Singleton
class RecordingMethodManager @Inject constructor(
    private val context: Context
) {
    
    private val availableMethods = mutableListOf<RecordingMethod>()
    private var currentMethod: RecordingMethod? = null
    
    init {
        initializeRecordingMethods()
    }
    
    /**
     * Initialize all available recording methods based on Android version
     */
    private fun initializeRecordingMethods() {
        availableMethods.clear()
        
        // Add methods in order of preference (highest priority first)
        
        // Note: Actual recording method implementations will be created in separate tasks
        // For now, we'll create placeholder methods to avoid compilation errors

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // MediaProjection method for Android 10+
            // availableMethods.add(MediaProjectionRecordingMethod())
        }

        // AccessibilityService fallback for all versions
        // availableMethods.add(AccessibilityRecordingMethod())

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
            // Legacy AudioSource method for API 25-28
            // availableMethods.add(LegacyAudioSourceRecordingMethod())
        }
        
        // Sort by priority (highest first)
        availableMethods.sortByDescending { it.getPriority() }
        
        Timber.d("Initialized ${availableMethods.size} recording methods")
    }
    
    /**
     * Get the best available recording method for the current device
     */
    suspend fun getBestRecordingMethod(): RecordingMethod? {
        for (method in availableMethods) {
            if (method.isSupported(context) && method.hasRequiredPermissions(context)) {
                if (method.initialize(context)) {
                    Timber.d("Selected recording method: ${method.getMethodName()}")
                    return method
                }
            }
        }
        
        Timber.w("No suitable recording method found")
        return null
    }
    
    /**
     * Start recording using the best available method
     */
    suspend fun startRecording(
        outputFile: File,
        quality: RecordingQuality,
        phoneNumber: String
    ): RecordingResult {
        try {
            // Get the best method if we don't have one
            if (currentMethod == null) {
                currentMethod = getBestRecordingMethod()
            }
            
            val method = currentMethod ?: return RecordingResult(
                success = false,
                method = "none",
                error = "No recording method available"
            )
            
            val success = method.startRecording(outputFile, quality, phoneNumber)
            
            return if (success) {
                Timber.d("Recording started successfully with ${method.getMethodName()}")
                RecordingResult(
                    success = true,
                    method = method.getMethodName(),
                    outputFile = outputFile
                )
            } else {
                Timber.w("Failed to start recording with ${method.getMethodName()}")
                // Try fallback methods
                tryFallbackMethods(outputFile, quality, phoneNumber)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error starting recording")
            return RecordingResult(
                success = false,
                method = currentMethod?.getMethodName() ?: "unknown",
                error = e.message
            )
        }
    }
    
    /**
     * Try fallback recording methods if the primary method fails
     */
    private suspend fun tryFallbackMethods(
        outputFile: File,
        quality: RecordingQuality,
        phoneNumber: String
    ): RecordingResult {
        val currentMethodName = currentMethod?.getMethodName()
        
        for (method in availableMethods) {
            // Skip the method that just failed
            if (method.getMethodName() == currentMethodName) continue
            
            if (method.isSupported(context) && method.hasRequiredPermissions(context)) {
                try {
                    if (method.initialize(context)) {
                        val success = method.startRecording(outputFile, quality, phoneNumber)
                        if (success) {
                            currentMethod = method
                            Timber.d("Fallback recording started with ${method.getMethodName()}")
                            return RecordingResult(
                                success = true,
                                method = method.getMethodName(),
                                outputFile = outputFile
                            )
                        }
                    }
                } catch (e: Exception) {
                    Timber.w(e, "Fallback method ${method.getMethodName()} failed")
                }
            }
        }
        
        return RecordingResult(
            success = false,
            method = "fallback_failed",
            error = "All recording methods failed"
        )
    }
    
    /**
     * Stop the current recording
     */
    suspend fun stopRecording(): RecordingResult {
        val method = currentMethod ?: return RecordingResult(
            success = false,
            method = "none",
            error = "No active recording method"
        )
        
        return try {
            val success = method.stopRecording()
            val duration = method.getRecordingDuration()
            
            if (success) {
                Timber.d("Recording stopped successfully")
                RecordingResult(
                    success = true,
                    method = method.getMethodName(),
                    duration = duration
                )
            } else {
                RecordingResult(
                    success = false,
                    method = method.getMethodName(),
                    error = "Failed to stop recording"
                )
            }
        } catch (e: Exception) {
            Timber.e(e, "Error stopping recording")
            RecordingResult(
                success = false,
                method = method.getMethodName(),
                error = e.message
            )
        }
    }
    
    /**
     * Check if currently recording
     */
    fun isRecording(): Boolean = currentMethod?.isRecording() == true
    
    /**
     * Get current recording duration
     */
    fun getRecordingDuration(): Long = currentMethod?.getRecordingDuration() ?: 0L
    
    /**
     * Get the name of the current recording method
     */
    fun getCurrentMethodName(): String? = currentMethod?.getMethodName()
    
    /**
     * Get all required permissions for available methods
     */
    fun getAllRequiredPermissions(): List<String> {
        return availableMethods.flatMap { it.getRequiredPermissions() }.distinct()
    }
    
    /**
     * Clean up all recording methods
     */
    suspend fun cleanup() {
        currentMethod?.cleanup()
        currentMethod = null
        
        availableMethods.forEach { method ->
            try {
                method.cleanup()
            } catch (e: Exception) {
                Timber.w(e, "Error cleaning up ${method.getMethodName()}")
            }
        }
    }
}
