<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res"><file name="ic_error_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_error_24.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_mic_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_mic_24.xml" qualifiers="" type="drawable"/><file name="ic_stop_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_stop_24.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Call Recorder</string><string name="settings">Settings</string><string name="recording_details">Recording Details</string><string name="accessibility_service_description">This service is used as a fallback method to record calls when the primary recording method is not available. It monitors call states to start and stop recording automatically.</string><string name="permission_record_audio_title">Microphone Permission</string><string name="permission_record_audio_description">Required to record call audio</string><string name="permission_phone_state_title">Phone State Permission</string><string name="permission_phone_state_description">Required to detect incoming and outgoing calls</string><string name="permission_call_log_title">Call Log Permission</string><string name="permission_call_log_description">Required to identify caller information</string><string name="permission_notification_title">Notification Permission</string><string name="permission_notification_description">Required to show recording notifications</string><string name="permission_contacts_title">Contacts Permission</string><string name="permission_contacts_description">Required to display caller names</string><string name="recording_notification_title">Recording Call</string><string name="recording_notification_text">Tap to stop recording</string><string name="recording_notification_channel_name">Call Recording</string><string name="recording_notification_channel_description">Notifications for active call recordings</string><string name="no_recordings">No recordings found</string><string name="search_recordings">Search recordings…</string><string name="incoming_call">Incoming</string><string name="outgoing_call">Outgoing</string><string name="unknown_caller">Unknown</string><string name="play">Play</string><string name="pause">Pause</string><string name="stop">Stop</string><string name="share">Share</string><string name="delete">Delete</string><string name="delete_recording">Delete Recording</string><string name="delete_confirmation">Are you sure you want to delete this recording?</string><string name="yes">Yes</string><string name="no">No</string><string name="cancel">Cancel</string><string name="recording_quality">Recording Quality</string><string name="auto_delete">Auto Delete</string><string name="privacy_policy">Privacy Policy</string><string name="permissions_status">Permissions Status</string><string name="about">About</string><string name="legal_disclaimer_title">Legal Disclaimer</string><string name="legal_disclaimer_text">Recording phone calls may be subject to legal restrictions in your jurisdiction. You are responsible for complying with all applicable laws. This app is provided for lawful use only.</string><string name="consent_dialog_title">Recording Consent</string><string name="consent_dialog_text">This call will be recorded. Please ensure you have consent from all parties before proceeding.</string><string name="i_understand">I Understand</string><string name="error_recording_failed">Recording failed</string><string name="error_permission_denied">Permission denied</string><string name="error_storage_full">Storage full</string><string name="error_device_not_supported">Device not supported</string><string name="error_playback_failed">Playback failed</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.CallRecordingApp" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="accessibility_service_config" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>