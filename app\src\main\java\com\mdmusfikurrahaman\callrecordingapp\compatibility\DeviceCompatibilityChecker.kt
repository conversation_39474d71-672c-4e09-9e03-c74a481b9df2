package com.mdmusfikurrahaman.callrecordingapp.compatibility

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Device compatibility checker for call recording features
 * 
 * This class checks:
 * - Android version compatibility
 * - Device manufacturer restrictions
 * - Hardware capabilities
 * - Known device issues
 * - Recording method availability
 */
@Singleton
class DeviceCompatibilityChecker @Inject constructor(
    private val context: Context
) {
    
    companion object {
        // Known problematic manufacturers
        private val RESTRICTED_MANUFACTURERS = setOf(
            "xiaomi", "huawei", "honor", "oppo", "vivo", "oneplus", "realme"
        )
        
        // Manufacturers with known call recording restrictions
        private val CALL_RECORDING_RESTRICTED = setOf(
            "samsung", "google", "sony"
        )
        
        // Known working devices (manufacturer + model combinations)
        private val KNOWN_WORKING_DEVICES = setOf(
            "samsung_sm-g973f", // Galaxy S10
            "google_pixel_3",
            "oneplus_gm1913" // OnePlus 7 Pro
        )
        
        // Known problematic devices
        private val KNOWN_PROBLEMATIC_DEVICES = setOf(
            "huawei_p30_pro",
            "xiaomi_mi_9",
            "samsung_sm-a505f" // Galaxy A50
        )
    }
    
    /**
     * Perform comprehensive compatibility check
     */
    fun checkCompatibility(): CompatibilityReport {
        val checks = mutableListOf<CompatibilityCheck>()
        
        // Android version checks
        checks.add(checkAndroidVersion())
        checks.add(checkMediaProjectionSupport())
        checks.add(checkAccessibilityServiceSupport())
        checks.add(checkLegacyAudioSourceSupport())
        
        // Hardware checks
        checks.add(checkMicrophoneAvailability())
        checks.add(checkAudioRecordingCapability())
        
        // Manufacturer checks
        checks.add(checkManufacturerRestrictions())
        checks.add(checkDeviceSpecificIssues())
        
        // Permission checks
        checks.add(checkRequiredPermissions())
        
        // Feature checks
        checks.add(checkCallRecordingCapability())
        
        val overallCompatibility = determineOverallCompatibility(checks)
        val recommendations = generateRecommendations(checks)
        
        return CompatibilityReport(
            overallCompatibility = overallCompatibility,
            checks = checks,
            recommendations = recommendations,
            deviceInfo = getDeviceInfo()
        )
    }
    
    /**
     * Check Android version compatibility
     */
    private fun checkAndroidVersion(): CompatibilityCheck {
        val currentVersion = Build.VERSION.SDK_INT
        
        return when {
            currentVersion >= Build.VERSION_CODES.Q -> {
                CompatibilityCheck(
                    feature = "Android Version",
                    status = CompatibilityStatus.FULLY_SUPPORTED,
                    message = "Android ${Build.VERSION.RELEASE} (API $currentVersion) - Full support",
                    details = "All recording methods available"
                )
            }
            
            currentVersion >= Build.VERSION_CODES.N -> {
                CompatibilityCheck(
                    feature = "Android Version",
                    status = CompatibilityStatus.PARTIALLY_SUPPORTED,
                    message = "Android ${Build.VERSION.RELEASE} (API $currentVersion) - Limited support",
                    details = "Legacy AudioSource method available, MediaProjection not supported"
                )
            }
            
            else -> {
                CompatibilityCheck(
                    feature = "Android Version",
                    status = CompatibilityStatus.NOT_SUPPORTED,
                    message = "Android ${Build.VERSION.RELEASE} (API $currentVersion) - Not supported",
                    details = "Minimum Android 7.0 (API 24) required"
                )
            }
        }
    }
    
    /**
     * Check MediaProjection API support
     */
    private fun checkMediaProjectionSupport(): CompatibilityCheck {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            CompatibilityCheck(
                feature = "MediaProjection Recording",
                status = CompatibilityStatus.FULLY_SUPPORTED,
                message = "MediaProjection API available",
                details = "Can record system audio on Android 10+"
            )
        } else {
            CompatibilityCheck(
                feature = "MediaProjection Recording",
                status = CompatibilityStatus.NOT_SUPPORTED,
                message = "MediaProjection requires Android 10+",
                details = "Current version: Android ${Build.VERSION.RELEASE}"
            )
        }
    }
    
    /**
     * Check AccessibilityService support
     */
    private fun checkAccessibilityServiceSupport(): CompatibilityCheck {
        val hasAccessibilityFeature = context.packageManager.hasSystemFeature(
            PackageManager.FEATURE_ACCESSIBILITY
        )
        
        return if (hasAccessibilityFeature) {
            CompatibilityCheck(
                feature = "Accessibility Service",
                status = CompatibilityStatus.PARTIALLY_SUPPORTED,
                message = "AccessibilityService available",
                details = "Can detect calls, limited recording capability"
            )
        } else {
            CompatibilityCheck(
                feature = "Accessibility Service",
                status = CompatibilityStatus.NOT_SUPPORTED,
                message = "AccessibilityService not available",
                details = "Device does not support accessibility features"
            )
        }
    }
    
    /**
     * Check legacy AudioSource support
     */
    private fun checkLegacyAudioSourceSupport(): CompatibilityCheck {
        return if (Build.VERSION.SDK_INT in Build.VERSION_CODES.N..Build.VERSION_CODES.P) {
            CompatibilityCheck(
                feature = "Legacy AudioSource",
                status = CompatibilityStatus.PARTIALLY_SUPPORTED,
                message = "Legacy VOICE_CALL source available",
                details = "May work on some devices, deprecated since Android 10"
            )
        } else {
            CompatibilityCheck(
                feature = "Legacy AudioSource",
                status = CompatibilityStatus.NOT_SUPPORTED,
                message = "Legacy AudioSource not available",
                details = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    "Deprecated since Android 10"
                } else {
                    "Not available on Android versions below 7.0"
                }
            )
        }
    }
    
    /**
     * Check microphone availability
     */
    private fun checkMicrophoneAvailability(): CompatibilityCheck {
        val hasMicrophone = context.packageManager.hasSystemFeature(
            PackageManager.FEATURE_MICROPHONE
        )
        
        return if (hasMicrophone) {
            CompatibilityCheck(
                feature = "Microphone",
                status = CompatibilityStatus.FULLY_SUPPORTED,
                message = "Microphone available",
                details = "Device has microphone hardware"
            )
        } else {
            CompatibilityCheck(
                feature = "Microphone",
                status = CompatibilityStatus.NOT_SUPPORTED,
                message = "No microphone detected",
                details = "Device does not have microphone hardware"
            )
        }
    }
    
    /**
     * Check audio recording capability
     */
    private fun checkAudioRecordingCapability(): CompatibilityCheck {
        return try {
            // Try to create an AudioRecord instance to test capability
            val audioRecord = android.media.AudioRecord(
                android.media.MediaRecorder.AudioSource.MIC,
                44100,
                android.media.AudioFormat.CHANNEL_IN_MONO,
                android.media.AudioFormat.ENCODING_PCM_16BIT,
                android.media.AudioRecord.getMinBufferSize(
                    44100,
                    android.media.AudioFormat.CHANNEL_IN_MONO,
                    android.media.AudioFormat.ENCODING_PCM_16BIT
                )
            )
            
            val isInitialized = audioRecord.state == android.media.AudioRecord.STATE_INITIALIZED
            audioRecord.release()
            
            if (isInitialized) {
                CompatibilityCheck(
                    feature = "Audio Recording",
                    status = CompatibilityStatus.FULLY_SUPPORTED,
                    message = "Audio recording capability confirmed",
                    details = "AudioRecord initialization successful"
                )
            } else {
                CompatibilityCheck(
                    feature = "Audio Recording",
                    status = CompatibilityStatus.NOT_SUPPORTED,
                    message = "Audio recording not available",
                    details = "AudioRecord initialization failed"
                )
            }
        } catch (e: Exception) {
            CompatibilityCheck(
                feature = "Audio Recording",
                status = CompatibilityStatus.NOT_SUPPORTED,
                message = "Audio recording error: ${e.message}",
                details = "Exception during AudioRecord test"
            )
        }
    }
    
    /**
     * Check manufacturer-specific restrictions
     */
    private fun checkManufacturerRestrictions(): CompatibilityCheck {
        val manufacturer = Build.MANUFACTURER.lowercase()
        
        return when {
            manufacturer in CALL_RECORDING_RESTRICTED -> {
                CompatibilityCheck(
                    feature = "Manufacturer Compatibility",
                    status = CompatibilityStatus.PARTIALLY_SUPPORTED,
                    message = "${Build.MANUFACTURER} devices have call recording restrictions",
                    details = "May require additional permissions or may not work reliably"
                )
            }
            
            manufacturer in RESTRICTED_MANUFACTURERS -> {
                CompatibilityCheck(
                    feature = "Manufacturer Compatibility",
                    status = CompatibilityStatus.LIMITED_SUPPORT,
                    message = "${Build.MANUFACTURER} devices may have limitations",
                    details = "Known to have aggressive power management or permission restrictions"
                )
            }
            
            else -> {
                CompatibilityCheck(
                    feature = "Manufacturer Compatibility",
                    status = CompatibilityStatus.FULLY_SUPPORTED,
                    message = "${Build.MANUFACTURER} devices generally supported",
                    details = "No known manufacturer-specific restrictions"
                )
            }
        }
    }
    
    /**
     * Check device-specific issues
     */
    private fun checkDeviceSpecificIssues(): CompatibilityCheck {
        val deviceKey = "${Build.MANUFACTURER}_${Build.MODEL}".lowercase()
        
        return when {
            deviceKey in KNOWN_WORKING_DEVICES -> {
                CompatibilityCheck(
                    feature = "Device Compatibility",
                    status = CompatibilityStatus.FULLY_SUPPORTED,
                    message = "Device confirmed working",
                    details = "This device model has been tested and works well"
                )
            }
            
            deviceKey in KNOWN_PROBLEMATIC_DEVICES -> {
                CompatibilityCheck(
                    feature = "Device Compatibility",
                    status = CompatibilityStatus.LIMITED_SUPPORT,
                    message = "Known issues with this device",
                    details = "This device model has reported compatibility issues"
                )
            }
            
            else -> {
                CompatibilityCheck(
                    feature = "Device Compatibility",
                    status = CompatibilityStatus.UNKNOWN,
                    message = "Device compatibility unknown",
                    details = "This device model has not been specifically tested"
                )
            }
        }
    }
    
    /**
     * Check required permissions
     */
    private fun checkRequiredPermissions(): CompatibilityCheck {
        val requiredPermissions = listOf(
            android.Manifest.permission.RECORD_AUDIO,
            android.Manifest.permission.READ_PHONE_STATE,
            android.Manifest.permission.READ_CALL_LOG
        )
        
        val missingPermissions = requiredPermissions.filter { permission ->
            context.checkSelfPermission(permission) != PackageManager.PERMISSION_GRANTED
        }
        
        return if (missingPermissions.isEmpty()) {
            CompatibilityCheck(
                feature = "Permissions",
                status = CompatibilityStatus.FULLY_SUPPORTED,
                message = "All required permissions granted",
                details = "App has all necessary permissions"
            )
        } else {
            CompatibilityCheck(
                feature = "Permissions",
                status = CompatibilityStatus.NOT_SUPPORTED,
                message = "Missing required permissions",
                details = "Missing: ${missingPermissions.joinToString(", ")}"
            )
        }
    }
    
    /**
     * Check overall call recording capability
     */
    private fun checkCallRecordingCapability(): CompatibilityCheck {
        // This is a summary check based on other factors
        val hasBasicRequirements = Build.VERSION.SDK_INT >= Build.VERSION_CODES.N &&
                context.packageManager.hasSystemFeature(PackageManager.FEATURE_MICROPHONE)
        
        return if (hasBasicRequirements) {
            CompatibilityCheck(
                feature = "Call Recording Capability",
                status = CompatibilityStatus.PARTIALLY_SUPPORTED,
                message = "Basic call recording requirements met",
                details = "Success depends on device manufacturer and Android version"
            )
        } else {
            CompatibilityCheck(
                feature = "Call Recording Capability",
                status = CompatibilityStatus.NOT_SUPPORTED,
                message = "Basic requirements not met",
                details = "Device lacks minimum requirements for call recording"
            )
        }
    }
    
    /**
     * Determine overall compatibility based on individual checks
     */
    private fun determineOverallCompatibility(checks: List<CompatibilityCheck>): CompatibilityStatus {
        val criticalChecks = checks.filter { 
            it.feature in listOf("Android Version", "Microphone", "Audio Recording")
        }
        
        return when {
            criticalChecks.any { it.status == CompatibilityStatus.NOT_SUPPORTED } -> 
                CompatibilityStatus.NOT_SUPPORTED
            
            checks.count { it.status == CompatibilityStatus.FULLY_SUPPORTED } >= checks.size / 2 -> 
                CompatibilityStatus.FULLY_SUPPORTED
            
            checks.any { it.status in listOf(CompatibilityStatus.FULLY_SUPPORTED, CompatibilityStatus.PARTIALLY_SUPPORTED) } -> 
                CompatibilityStatus.PARTIALLY_SUPPORTED
            
            else -> CompatibilityStatus.LIMITED_SUPPORT
        }
    }
    
    /**
     * Generate recommendations based on compatibility checks
     */
    private fun generateRecommendations(checks: List<CompatibilityCheck>): List<String> {
        val recommendations = mutableListOf<String>()
        
        checks.forEach { check ->
            when (check.status) {
                CompatibilityStatus.NOT_SUPPORTED -> {
                    when (check.feature) {
                        "Android Version" -> recommendations.add("Consider updating your Android version if possible")
                        "Microphone" -> recommendations.add("This device cannot record audio")
                        "Permissions" -> recommendations.add("Grant all required permissions in Settings")
                    }
                }
                CompatibilityStatus.LIMITED_SUPPORT -> {
                    recommendations.add("${check.feature}: ${check.message}")
                }
                CompatibilityStatus.PARTIALLY_SUPPORTED -> {
                    if (check.feature == "Manufacturer Compatibility") {
                        recommendations.add("Try different recording methods if one doesn't work")
                    }
                }
                else -> { /* No recommendations for fully supported features */ }
            }
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("Your device appears to be fully compatible with call recording")
        }
        
        return recommendations
    }
    
    /**
     * Get detailed device information
     */
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            sdkVersion = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            device = Build.DEVICE
        )
    }
}

/**
 * Enum representing compatibility status levels
 */
enum class CompatibilityStatus {
    FULLY_SUPPORTED,    // Feature works perfectly
    PARTIALLY_SUPPORTED, // Feature works with limitations
    LIMITED_SUPPORT,    // Feature may work but with significant issues
    NOT_SUPPORTED,      // Feature doesn't work
    UNKNOWN            // Compatibility status unknown
}

/**
 * Data class representing a single compatibility check
 */
data class CompatibilityCheck(
    val feature: String,
    val status: CompatibilityStatus,
    val message: String,
    val details: String
)

/**
 * Data class representing the overall compatibility report
 */
data class CompatibilityReport(
    val overallCompatibility: CompatibilityStatus,
    val checks: List<CompatibilityCheck>,
    val recommendations: List<String>,
    val deviceInfo: DeviceInfo
)
