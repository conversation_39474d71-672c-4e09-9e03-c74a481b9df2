package com.mdmusfikurrahaman.callrecordingapp.service.recording

import android.content.Context
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import java.io.File

/**
 * Interface defining the contract for different call recording methods
 * 
 * This allows the app to support multiple recording techniques:
 * - MediaProjection API (Android 10+)
 * - AccessibilityService fallback
 * - Legacy AudioSource.VOICE_CALL (API 25-28)
 */
interface RecordingMethod {
    
    /**
     * Check if this recording method is supported on the current device
     */
    fun isSupported(context: Context): Boolean
    
    /**
     * Check if all required permissions are granted for this method
     */
    fun hasRequiredPermissions(context: Context): <PERSON><PERSON><PERSON>
    
    /**
     * Get the list of permissions required for this method
     */
    fun getRequiredPermissions(): List<String>
    
    /**
     * Initialize the recording method
     * @return true if initialization was successful
     */
    suspend fun initialize(context: Context): Bo<PERSON>an
    
    /**
     * Start recording a call
     * @param outputFile The file where the recording should be saved
     * @param quality The desired recording quality
     * @param phoneNumber The phone number being called/calling
     * @return true if recording started successfully
     */
    suspend fun startRecording(
        outputFile: File,
        quality: RecordingQuality,
        phoneNumber: String
    ): Boolean
    
    /**
     * Stop the current recording
     * @return true if recording was stopped successfully
     */
    suspend fun stopRecording(): Boolean
    
    /**
     * Check if currently recording
     */
    fun isRecording(): Boolean
    
    /**
     * Get the current recording duration in milliseconds
     */
    fun getRecordingDuration(): Long
    
    /**
     * Clean up resources
     */
    suspend fun cleanup()
    
    /**
     * Get a human-readable name for this recording method
     */
    fun getMethodName(): String
    
    /**
     * Get the priority of this method (higher number = higher priority)
     * Used to determine which method to try first
     */
    fun getPriority(): Int
}

/**
 * Abstract base class for recording methods with common functionality
 */
abstract class BaseRecordingMethod : RecordingMethod {
    
    protected var isInitialized = false
    protected var isCurrentlyRecording = false
    protected var recordingStartTime = 0L
    protected var currentOutputFile: File? = null
    
    override fun isRecording(): Boolean = isCurrentlyRecording
    
    override fun getRecordingDuration(): Long {
        return if (isCurrentlyRecording && recordingStartTime > 0) {
            System.currentTimeMillis() - recordingStartTime
        } else {
            0L
        }
    }
    
    protected fun markRecordingStarted(outputFile: File) {
        isCurrentlyRecording = true
        recordingStartTime = System.currentTimeMillis()
        currentOutputFile = outputFile
    }
    
    protected fun markRecordingStopped() {
        isCurrentlyRecording = false
        recordingStartTime = 0L
        currentOutputFile = null
    }
    
    override suspend fun cleanup() {
        if (isRecording()) {
            stopRecording()
        }
        isInitialized = false
    }
}

/**
 * Exception thrown when a recording method fails
 */
class RecordingException(
    message: String,
    cause: Throwable? = null,
    val method: String? = null
) : Exception(message, cause)

/**
 * Data class representing the result of a recording operation
 */
data class RecordingResult(
    val success: Boolean,
    val method: String,
    val outputFile: File? = null,
    val duration: Long = 0L,
    val fileSize: Long = 0L,
    val error: String? = null
)
