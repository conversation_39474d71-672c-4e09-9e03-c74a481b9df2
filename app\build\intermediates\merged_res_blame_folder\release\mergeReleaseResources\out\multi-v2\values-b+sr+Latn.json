{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeReleaseResources-59:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\125614bd4c72f93cfeffaa0b5947d611\\transformed\\navigation-ui-2.7.6\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "178,179", "startColumns": "4,4", "startOffsets": "13797,13904", "endColumns": "106,122", "endOffsets": "13899,14022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\663fb4dbb7e440a36499eafaa6fbda5d\\transformed\\core-1.16.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "58,59,60,61,62,63,64,182", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4303,4401,4503,4600,4704,4808,4913,14194", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4396,4498,4595,4699,4803,4908,5024,14290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5e1e4d034409f719670a3d1d52262c91\\transformed\\appcompat-1.6.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1136,1243,1344,1450,1536,1640,1762,1847,1929,2020,2113,2208,2302,2402,2495,2590,2695,2786,2877,2963,3068,3174,3277,3384,3493,3600,3770,14107", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "1238,1339,1445,1531,1635,1757,1842,1924,2015,2108,2203,2297,2397,2490,2585,2690,2781,2872,2958,3063,3169,3272,3379,3488,3595,3765,3862,14189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81e0b3af703173a357d5c452b0d31980\\transformed\\exoplayer-core-2.19.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7382,7457,7518,7583,7655,7734,7807,7895,7979", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "7452,7513,7578,7650,7729,7802,7890,7974,8049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fab9a8fce592b1bb05a0549bf6b2f96e\\transformed\\exoplayer-ui-2.19.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1946,2060,2171,2247,2335,2409,2480,2572,2665,2732,2797,2850,2908,2956,3017,3083,3147,3210,3275,3339,3400,3466,3531,3597,3649,3711,3787,3863", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1941,2055,2166,2242,2330,2404,2475,2567,2660,2727,2792,2845,2903,2951,3012,3078,3142,3205,3270,3334,3395,3461,3526,3592,3644,3706,3782,3858,3914"}, "to": {"startLines": "2,11,16,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,651,5402,5484,5567,5649,5738,5829,5899,5966,6060,6155,6223,6287,6350,6422,6531,6645,6756,6832,6920,6994,7065,7157,7250,7317,8054,8107,8165,8213,8274,8340,8404,8467,8532,8596,8657,8723,8788,8854,8906,8968,9044,9120", "endLines": "10,15,20,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "377,646,907,5479,5562,5644,5733,5824,5894,5961,6055,6150,6218,6282,6345,6417,6526,6640,6751,6827,6915,6989,7060,7152,7245,7312,7377,8102,8160,8208,8269,8335,8399,8462,8527,8591,8652,8718,8783,8849,8901,8963,9039,9115,9171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7473b31c75d2d339c0da8a1a3f9146d6\\transformed\\material-1.11.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1133,1226,1294,1357,1460,1520,1586,1642,1713,1773,1827,1939,1996,2057,2111,2187,2312,2399,2482,2621,2703,2786,2917,3005,3083,3137,3193,3259,3333,3411,3500,3582,3658,3734,3809,3881,3988,4078,4151,4243,4339,4411,4487,4583,4636,4718,4785,4872,4959,5021,5085,5148,5217,5322,5432,5528,5636,5694,5754", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "319,396,473,553,661,755,849,981,1062,1128,1221,1289,1352,1455,1515,1581,1637,1708,1768,1822,1934,1991,2052,2106,2182,2307,2394,2477,2616,2698,2781,2912,3000,3078,3132,3188,3254,3328,3406,3495,3577,3653,3729,3804,3876,3983,4073,4146,4238,4334,4406,4482,4578,4631,4713,4780,4867,4954,5016,5080,5143,5212,5317,5427,5523,5631,5689,5749,5829"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,68,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,3867,3944,4021,4101,4209,5029,5123,5255,5336,9176,9269,9337,9400,9503,9563,9629,9685,9756,9816,9870,9982,10039,10100,10154,10230,10355,10442,10525,10664,10746,10829,10960,11048,11126,11180,11236,11302,11376,11454,11543,11625,11701,11777,11852,11924,12031,12121,12194,12286,12382,12454,12530,12626,12679,12761,12828,12915,13002,13064,13128,13191,13260,13365,13475,13571,13679,13737,14027", "endLines": "25,53,54,55,56,57,65,66,67,68,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,180", "endColumns": "12,76,76,79,107,93,93,131,80,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,82,138,81,82,130,87,77,53,55,65,73,77,88,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "1131,3939,4016,4096,4204,4298,5118,5250,5331,5397,9264,9332,9395,9498,9558,9624,9680,9751,9811,9865,9977,10034,10095,10149,10225,10350,10437,10520,10659,10741,10824,10955,11043,11121,11175,11231,11297,11371,11449,11538,11620,11696,11772,11847,11919,12026,12116,12189,12281,12377,12449,12525,12621,12674,12756,12823,12910,12997,13059,13123,13186,13255,13360,13470,13566,13674,13732,13792,14102"}}]}]}