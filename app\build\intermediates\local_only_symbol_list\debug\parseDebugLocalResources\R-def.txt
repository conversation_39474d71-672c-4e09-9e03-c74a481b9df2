R_DEF: Internal format may change without notice
local
color black
color md_theme_dark_background
color md_theme_dark_error
color md_theme_dark_errorContainer
color md_theme_dark_inverseOnSurface
color md_theme_dark_inverseSurface
color md_theme_dark_onBackground
color md_theme_dark_onError
color md_theme_dark_onErrorContainer
color md_theme_dark_onPrimary
color md_theme_dark_onPrimaryContainer
color md_theme_dark_onSecondary
color md_theme_dark_onSecondaryContainer
color md_theme_dark_onSurface
color md_theme_dark_onSurfaceVariant
color md_theme_dark_onTertiary
color md_theme_dark_onTertiaryContainer
color md_theme_dark_outline
color md_theme_dark_primary
color md_theme_dark_primaryContainer
color md_theme_dark_secondary
color md_theme_dark_secondaryContainer
color md_theme_dark_surface
color md_theme_dark_surfaceVariant
color md_theme_dark_tertiary
color md_theme_dark_tertiaryContainer
color md_theme_light_background
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_inverseOnSurface
color md_theme_light_inverseSurface
color md_theme_light_onBackground
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_surface
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
color white
drawable ic_error_24
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_mic_24
drawable ic_stop_24
mipmap ic_launcher
mipmap ic_launcher_round
string about
string accessibility_service_description
string app_name
string auto_delete
string cancel
string consent_dialog_text
string consent_dialog_title
string delete
string delete_confirmation
string delete_recording
string error_device_not_supported
string error_permission_denied
string error_playback_failed
string error_recording_failed
string error_storage_full
string i_understand
string incoming_call
string legal_disclaimer_text
string legal_disclaimer_title
string no
string no_recordings
string outgoing_call
string pause
string permission_call_log_description
string permission_call_log_title
string permission_contacts_description
string permission_contacts_title
string permission_notification_description
string permission_notification_title
string permission_phone_state_description
string permission_phone_state_title
string permission_record_audio_description
string permission_record_audio_title
string permissions_status
string play
string privacy_policy
string recording_details
string recording_notification_channel_description
string recording_notification_channel_name
string recording_notification_text
string recording_notification_title
string recording_quality
string search_recordings
string settings
string share
string stop
string unknown_caller
string yes
style Theme.CallRecordingApp
xml accessibility_service_config
xml backup_rules
xml data_extraction_rules
xml file_paths
