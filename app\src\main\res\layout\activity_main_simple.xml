<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- App Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Call Recording App"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <!-- Welcome Message -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="স্বাগতম! আপনার Call Recording App এ।"
        android:textSize="18sp"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Permission Status -->
    <TextView
        android:id="@+id/permissionStatusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Permission Status: Checking..."
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Recordings List -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Recordings:"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#F5F5F5"
        android:padding="8dp" />

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/requestPermissionsButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Request Permissions"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/startRecordingButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Start Recording"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
