<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res"><file name="ic_call_received_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_call_received_24.xml" qualifiers="" type="drawable"/><file name="ic_error_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_error_24.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_mic_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_mic_24.xml" qualifiers="" type="drawable"/><file name="ic_more_vert_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_more_vert_24.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_play_arrow_24.xml" qualifiers="" type="drawable"/><file name="ic_search_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_search_24.xml" qualifiers="" type="drawable"/><file name="ic_stop_24" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\drawable\ic_stop_24.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="item_recording" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\layout\item_recording.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\values\colors.xml" qualifiers=""><color name="md_theme_light_primary">#6750A4</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#EADDFF</color><color name="md_theme_light_onPrimaryContainer">#21005D</color><color name="md_theme_light_secondary">#625B71</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#E8DEF8</color><color name="md_theme_light_onSecondaryContainer">#1D192B</color><color name="md_theme_light_tertiary">#7D5260</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#FFD8E4</color><color name="md_theme_light_onTertiaryContainer">#31111D</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_outline">#79747E</color><color name="md_theme_light_background">#FFFBFE</color><color name="md_theme_light_onBackground">#1C1B1F</color><color name="md_theme_light_surface">#FFFBFE</color><color name="md_theme_light_onSurface">#1C1B1F</color><color name="md_theme_light_surfaceVariant">#E7E0EC</color><color name="md_theme_light_onSurfaceVariant">#49454F</color><color name="md_theme_light_inverseSurface">#313033</color><color name="md_theme_light_inverseOnSurface">#F4EFF4</color><color name="md_theme_dark_primary">#D0BCFF</color><color name="md_theme_dark_onPrimary">#381E72</color><color name="md_theme_dark_primaryContainer">#4F378B</color><color name="md_theme_dark_onPrimaryContainer">#EADDFF</color><color name="md_theme_dark_secondary">#CCC2DC</color><color name="md_theme_dark_onSecondary">#332D41</color><color name="md_theme_dark_secondaryContainer">#4A4458</color><color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color><color name="md_theme_dark_tertiary">#EFB8C8</color><color name="md_theme_dark_onTertiary">#492532</color><color name="md_theme_dark_tertiaryContainer">#633B48</color><color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color><color name="md_theme_dark_error">#FFB4AB</color><color name="md_theme_dark_onError">#690005</color><color name="md_theme_dark_errorContainer">#93000A</color><color name="md_theme_dark_onErrorContainer">#FFDAD6</color><color name="md_theme_dark_outline">#938F99</color><color name="md_theme_dark_background">#1C1B1F</color><color name="md_theme_dark_onBackground">#E6E1E5</color><color name="md_theme_dark_surface">#1C1B1F</color><color name="md_theme_dark_onSurface">#E6E1E5</color><color name="md_theme_dark_surfaceVariant">#49454F</color><color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color><color name="md_theme_dark_inverseSurface">#E6E1E5</color><color name="md_theme_dark_inverseOnSurface">#313033</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Call Recorder</string><string name="settings">Settings</string><string name="recording_details">Recording Details</string><string name="accessibility_service_description">This service is used as a fallback method to record calls when the primary recording method is not available. It monitors call states to start and stop recording automatically.</string><string name="permission_record_audio_title">Microphone Permission</string><string name="permission_record_audio_description">Required to record call audio</string><string name="permission_phone_state_title">Phone State Permission</string><string name="permission_phone_state_description">Required to detect incoming and outgoing calls</string><string name="permission_call_log_title">Call Log Permission</string><string name="permission_call_log_description">Required to identify caller information</string><string name="permission_notification_title">Notification Permission</string><string name="permission_notification_description">Required to show recording notifications</string><string name="permission_contacts_title">Contacts Permission</string><string name="permission_contacts_description">Required to display caller names</string><string name="recording_notification_title">Recording Call</string><string name="recording_notification_text">Tap to stop recording</string><string name="recording_notification_channel_name">Call Recording</string><string name="recording_notification_channel_description">Notifications for active call recordings</string><string name="no_recordings">No recordings found</string><string name="no_recordings_yet">কোন recording নেই</string><string name="search_recordings">Search recordings…</string><string name="incoming_call">Incoming</string><string name="outgoing_call">Outgoing</string><string name="unknown_caller">Unknown</string><string name="play">Play</string><string name="pause">Pause</string><string name="stop">Stop</string><string name="share">Share</string><string name="delete">Delete</string><string name="delete_recording">Delete Recording</string><string name="delete_confirmation">Are you sure you want to delete this recording?</string><string name="yes">Yes</string><string name="no">No</string><string name="cancel">Cancel</string><string name="recording_quality">Recording Quality</string><string name="auto_delete">Auto Delete</string><string name="privacy_policy">Privacy Policy</string><string name="permissions_status">Permissions Status</string><string name="about">About</string><string name="legal_disclaimer_title">Legal Disclaimer</string><string name="legal_disclaimer_text">Recording phone calls may be subject to legal restrictions in your jurisdiction. You are responsible for complying with all applicable laws. This app is provided for lawful use only.</string><string name="consent_dialog_title">Recording Consent</string><string name="consent_dialog_text">This call will be recorded. Please ensure you have consent from all parties before proceeding.</string><string name="i_understand">I Understand</string><string name="error_recording_failed">Recording failed</string><string name="error_permission_denied">Permission denied</string><string name="error_storage_full">Storage full</string><string name="error_device_not_supported">Device not supported</string><string name="error_playback_failed">Playback failed</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.CallRecordingApp" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.CallRecordingApp" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_dark_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_dark_inverseOnSurface</item>
    </style></file><file name="accessibility_service_config" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>