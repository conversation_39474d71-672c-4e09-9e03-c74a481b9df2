<resources>
    <string name="app_name">Call Recorder</string>

    <!-- Activity titles -->
    <string name="settings">Settings</string>
    <string name="recording_details">Recording Details</string>

    <!-- Accessibility service -->
    <string name="accessibility_service_description">This service is used as a fallback method to record calls when the primary recording method is not available. It monitors call states to start and stop recording automatically.</string>

    <!-- Permissions -->
    <string name="permission_record_audio_title">Microphone Permission</string>
    <string name="permission_record_audio_description">Required to record call audio</string>
    <string name="permission_phone_state_title">Phone State Permission</string>
    <string name="permission_phone_state_description">Required to detect incoming and outgoing calls</string>
    <string name="permission_call_log_title">Call Log Permission</string>
    <string name="permission_call_log_description">Required to identify caller information</string>
    <string name="permission_notification_title">Notification Permission</string>
    <string name="permission_notification_description">Required to show recording notifications</string>
    <string name="permission_contacts_title">Contacts Permission</string>
    <string name="permission_contacts_description">Required to display caller names</string>

    <!-- Recording service -->
    <string name="recording_notification_title">Recording Call</string>
    <string name="recording_notification_text">Tap to stop recording</string>
    <string name="recording_notification_channel_name">Call Recording</string>
    <string name="recording_notification_channel_description">Notifications for active call recordings</string>

    <!-- Main screen -->
    <string name="no_recordings">No recordings found</string>
    <string name="search_recordings">Search recordings…</string>
    <string name="incoming_call">Incoming</string>
    <string name="outgoing_call">Outgoing</string>
    <string name="unknown_caller">Unknown</string>

    <!-- Actions -->
    <string name="play">Play</string>
    <string name="pause">Pause</string>
    <string name="stop">Stop</string>
    <string name="share">Share</string>
    <string name="delete">Delete</string>
    <string name="delete_recording">Delete Recording</string>
    <string name="delete_confirmation">Are you sure you want to delete this recording?</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="cancel">Cancel</string>

    <!-- Settings -->
    <string name="recording_quality">Recording Quality</string>
    <string name="auto_delete">Auto Delete</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="permissions_status">Permissions Status</string>
    <string name="about">About</string>

    <!-- Legal -->
    <string name="legal_disclaimer_title">Legal Disclaimer</string>
    <string name="legal_disclaimer_text">Recording phone calls may be subject to legal restrictions in your jurisdiction. You are responsible for complying with all applicable laws. This app is provided for lawful use only.</string>
    <string name="consent_dialog_title">Recording Consent</string>
    <string name="consent_dialog_text">This call will be recorded. Please ensure you have consent from all parties before proceeding.</string>
    <string name="i_understand">I Understand</string>

    <!-- Errors -->
    <string name="error_recording_failed">Recording failed</string>
    <string name="error_permission_denied">Permission denied</string>
    <string name="error_storage_full">Storage full</string>
    <string name="error_device_not_supported">Device not supported</string>
    <string name="error_playback_failed">Playback failed</string>

</resources>