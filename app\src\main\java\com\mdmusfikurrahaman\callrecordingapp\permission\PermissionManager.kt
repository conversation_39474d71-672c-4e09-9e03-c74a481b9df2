package com.mdmusfikurrahaman.callrecordingapp.permission

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.util.PermissionUtils

/**
 * Comprehensive permission management system
 * 
 * This class handles all permission-related operations including:
 * - Runtime permission requests
 * - Permission status checking
 * - User-friendly permission explanations
 * - Graceful handling of permission denials
 * - Special permissions (accessibility, overlay, etc.)
 */
@Singleton
class PermissionManager @Inject constructor() {
    
    /**
     * Data class representing a permission with its metadata
     */
    data class PermissionInfo(
        val permission: String,
        val title: String,
        val description: String,
        val isRequired: <PERSON><PERSON>an,
        val minSdkVersion: Int = 1,
        val maxSdkVersion: Int = Int.MAX_VALUE
    )
    
    /**
     * Enum representing permission request results
     */
    enum class PermissionResult {
        GRANTED,
        DENIED,
        PERMANENTLY_DENIED,
        NOT_AVAILABLE
    }
    
    /**
     * Data class for permission request results
     */
    data class PermissionRequestResult(
        val permission: String,
        val result: PermissionResult,
        val shouldShowRationale: Boolean = false
    )
    
    companion object {
        /**
         * All permissions used by the app with their metadata
         */
        val APP_PERMISSIONS = listOf(
            PermissionInfo(
                permission = Manifest.permission.RECORD_AUDIO,
                title = "Microphone",
                description = "Required to record call audio",
                isRequired = true
            ),
            PermissionInfo(
                permission = Manifest.permission.READ_PHONE_STATE,
                title = "Phone State",
                description = "Required to detect incoming and outgoing calls",
                isRequired = true
            ),
            PermissionInfo(
                permission = Manifest.permission.READ_CALL_LOG,
                title = "Call Log",
                description = "Required to identify caller information",
                isRequired = true
            ),
            PermissionInfo(
                permission = Manifest.permission.READ_CONTACTS,
                title = "Contacts",
                description = "Required to display caller names",
                isRequired = false
            ),
            PermissionInfo(
                permission = Manifest.permission.POST_NOTIFICATIONS,
                title = "Notifications",
                description = "Required to show recording notifications",
                isRequired = true,
                minSdkVersion = Build.VERSION_CODES.TIRAMISU
            ),
            PermissionInfo(
                permission = Manifest.permission.FOREGROUND_SERVICE,
                title = "Foreground Service",
                description = "Required to run recording service in background",
                isRequired = true
            ),
            PermissionInfo(
                permission = Manifest.permission.FOREGROUND_SERVICE_MICROPHONE,
                title = "Foreground Service (Microphone)",
                description = "Required for microphone access in background",
                isRequired = true,
                minSdkVersion = Build.VERSION_CODES.UPSIDE_DOWN_CAKE
            ),
            PermissionInfo(
                permission = Manifest.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION,
                title = "Foreground Service (Media Projection)",
                description = "Required for screen recording in background",
                isRequired = false,
                minSdkVersion = Build.VERSION_CODES.UPSIDE_DOWN_CAKE
            )
        )
        
        /**
         * Get permissions that are applicable to the current Android version
         */
        fun getApplicablePermissions(): List<PermissionInfo> {
            return APP_PERMISSIONS.filter { permission ->
                Build.VERSION.SDK_INT >= permission.minSdkVersion &&
                Build.VERSION.SDK_INT <= permission.maxSdkVersion
            }
        }
        
        /**
         * Get required permissions for the current Android version
         */
        fun getRequiredPermissions(): List<PermissionInfo> {
            return getApplicablePermissions().filter { it.isRequired }
        }
        
        /**
         * Get optional permissions for the current Android version
         */
        fun getOptionalPermissions(): List<PermissionInfo> {
            return getApplicablePermissions().filter { !it.isRequired }
        }
    }
    
    /**
     * Check the status of all app permissions
     */
    fun checkAllPermissions(context: Context): Map<String, PermissionResult> {
        val results = mutableMapOf<String, PermissionResult>()
        
        getApplicablePermissions().forEach { permissionInfo ->
            results[permissionInfo.permission] = checkPermissionStatus(context, permissionInfo.permission)
        }
        
        return results
    }
    
    /**
     * Check the status of a specific permission
     */
    fun checkPermissionStatus(context: Context, permission: String): PermissionResult {
        return when {
            !PermissionUtils.isPermissionAvailable(permission) -> PermissionResult.NOT_AVAILABLE
            PermissionUtils.hasPermission(context, permission) -> PermissionResult.GRANTED
            else -> PermissionResult.DENIED
        }
    }
    
    /**
     * Check if all required permissions are granted
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        return getRequiredPermissions().all { permissionInfo ->
            checkPermissionStatus(context, permissionInfo.permission) == PermissionResult.GRANTED
        }
    }
    
    /**
     * Get missing required permissions
     */
    fun getMissingRequiredPermissions(context: Context): List<PermissionInfo> {
        return getRequiredPermissions().filter { permissionInfo ->
            checkPermissionStatus(context, permissionInfo.permission) != PermissionResult.GRANTED
        }
    }
    
    /**
     * Get missing optional permissions
     */
    fun getMissingOptionalPermissions(context: Context): List<PermissionInfo> {
        return getOptionalPermissions().filter { permissionInfo ->
            checkPermissionStatus(context, permissionInfo.permission) != PermissionResult.GRANTED
        }
    }
    
    /**
     * Create permission request launcher for an activity
     */
    fun createPermissionLauncher(
        activity: ComponentActivity,
        onResult: (Map<String, Boolean>) -> Unit
    ): ActivityResultLauncher<Array<String>> {
        return activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            onResult(permissions)
        }
    }
    
    /**
     * Request multiple permissions
     */
    fun requestPermissions(
        launcher: ActivityResultLauncher<Array<String>>,
        permissions: List<String>
    ) {
        try {
            launcher.launch(permissions.toTypedArray())
        } catch (e: Exception) {
            Timber.e(e, "Error requesting permissions")
        }
    }
    
    /**
     * Request all missing required permissions
     */
    fun requestMissingRequiredPermissions(
        context: Context,
        launcher: ActivityResultLauncher<Array<String>>
    ) {
        val missingPermissions = getMissingRequiredPermissions(context)
            .map { it.permission }
        
        if (missingPermissions.isNotEmpty()) {
            requestPermissions(launcher, missingPermissions)
        }
    }
    
    /**
     * Create intent to open app settings
     */
    fun createAppSettingsIntent(context: Context): Intent {
        return Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
    }
    
    /**
     * Create intent to open accessibility settings
     */
    fun createAccessibilitySettingsIntent(): Intent {
        return Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
    }
    
    /**
     * Create intent to open overlay permission settings
     */
    fun createOverlaySettingsIntent(context: Context): Intent {
        return Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
            data = Uri.parse("package:${context.packageName}")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
    }
    
    /**
     * Get user-friendly explanation for a permission
     */
    fun getPermissionExplanation(permission: String): String {
        val permissionInfo = APP_PERMISSIONS.find { it.permission == permission }
        return permissionInfo?.description ?: "This permission is required for the app to function properly."
    }
    
    /**
     * Get detailed permission information
     */
    fun getPermissionInfo(permission: String): PermissionInfo? {
        return APP_PERMISSIONS.find { it.permission == permission }
    }
    
    /**
     * Check if accessibility service is enabled
     */
    fun isAccessibilityServiceEnabled(context: Context): Boolean {
        return try {
            val accessibilityEnabled = Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
            
            if (accessibilityEnabled == 1) {
                val services = Settings.Secure.getString(
                    context.contentResolver,
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
                )
                
                services?.contains(context.packageName) == true
            } else {
                false
            }
        } catch (e: Exception) {
            Timber.e(e, "Error checking accessibility service status")
            false
        }
    }
    
    /**
     * Check if overlay permission is granted
     */
    fun hasOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Not required on older versions
        }
    }
    
    /**
     * Get comprehensive permission status report
     */
    fun getPermissionStatusReport(context: Context): PermissionStatusReport {
        val allPermissions = checkAllPermissions(context)
        val requiredPermissions = getRequiredPermissions()
        val optionalPermissions = getOptionalPermissions()
        
        val grantedRequired = requiredPermissions.count { 
            allPermissions[it.permission] == PermissionResult.GRANTED 
        }
        val grantedOptional = optionalPermissions.count { 
            allPermissions[it.permission] == PermissionResult.GRANTED 
        }
        
        return PermissionStatusReport(
            allPermissions = allPermissions,
            requiredPermissionsCount = requiredPermissions.size,
            optionalPermissionsCount = optionalPermissions.size,
            grantedRequiredCount = grantedRequired,
            grantedOptionalCount = grantedOptional,
            hasAllRequired = grantedRequired == requiredPermissions.size,
            accessibilityServiceEnabled = isAccessibilityServiceEnabled(context),
            overlayPermissionGranted = hasOverlayPermission(context)
        )
    }
    
    /**
     * Data class representing comprehensive permission status
     */
    data class PermissionStatusReport(
        val allPermissions: Map<String, PermissionResult>,
        val requiredPermissionsCount: Int,
        val optionalPermissionsCount: Int,
        val grantedRequiredCount: Int,
        val grantedOptionalCount: Int,
        val hasAllRequired: Boolean,
        val accessibilityServiceEnabled: Boolean,
        val overlayPermissionGranted: Boolean
    )
}
