package com.mdmusfikurrahaman.callrecordingapp.service.recording

import android.Manifest
import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Build
import androidx.annotation.RequiresApi
import timber.log.Timber
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import com.mdmusfikurrahaman.callrecordingapp.util.PermissionUtils

/**
 * Legacy AudioSource.VOICE_CALL recording method for Android 7.1-9 (API 25-28)
 * 
 * This method uses the deprecated AudioSource.VOICE_CALL to record both sides
 * of a phone call. It was the primary method for call recording before Android 10
 * but has been deprecated and may not work on newer devices.
 * 
 * Important Notes:
 * - AudioSource.VOICE_CALL is deprecated since Android 10
 * - May not work on all devices even in supported Android versions
 * - OEMs may have disabled this functionality
 * - Audio quality varies significantly between devices
 * - Some devices may only record one side of the call
 */
@RequiresApi(Build.VERSION_CODES.N)
@Suppress("DEPRECATION") // We know VOICE_CALL is deprecated
class LegacyAudioSourceRecordingMethod : BaseRecordingMethod() {
    
    companion object {
        private const val SAMPLE_RATE = 44100
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_STEREO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE_FACTOR = 2
        
        // Fallback configurations if stereo doesn't work
        private const val FALLBACK_CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val FALLBACK_SAMPLE_RATE = 22050
    }
    
    private var audioRecord: AudioRecord? = null
    private var recordingJob: Job? = null
    private var recordingConfig: RecordingConfig? = null
    
    override fun getMethodName(): String = "Legacy AudioSource.VOICE_CALL"
    
    override fun getPriority(): Int = 75 // High priority for supported Android versions
    
    override fun isSupported(context: Context): Boolean {
        // Only supported on Android 7.1 to 9 (API 25-28)
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && 
               Build.VERSION.SDK_INT <= Build.VERSION_CODES.P
    }
    
    override fun hasRequiredPermissions(context: Context): Boolean {
        return PermissionUtils.hasPermissions(context, getRequiredPermissions())
    }
    
    override fun getRequiredPermissions(): List<String> {
        val permissions = mutableListOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.FOREGROUND_SERVICE
        )
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            permissions.add(Manifest.permission.FOREGROUND_SERVICE_MICROPHONE)
        }
        
        return permissions
    }
    
    override suspend fun initialize(context: Context): Boolean {
        return try {
            // Test if VOICE_CALL audio source is available
            recordingConfig = findWorkingConfiguration()
            
            if (recordingConfig != null) {
                isInitialized = true
                Timber.d("Legacy AudioSource recording method initialized with config: $recordingConfig")
                true
            } else {
                Timber.w("No working configuration found for Legacy AudioSource method")
                false
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize Legacy AudioSource recording method")
            false
        }
    }
    
    override suspend fun startRecording(
        outputFile: File,
        quality: RecordingQuality,
        phoneNumber: String
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val config = recordingConfig ?: run {
                Timber.e("No recording configuration available")
                return@withContext false
            }
            
            val bufferSize = AudioRecord.getMinBufferSize(
                config.sampleRate,
                config.channelConfig,
                config.audioFormat
            ) * BUFFER_SIZE_FACTOR
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Timber.e("Invalid buffer size for AudioRecord")
                return@withContext false
            }
            
            // Create AudioRecord with VOICE_CALL source
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.VOICE_CALL,
                config.sampleRate,
                config.channelConfig,
                config.audioFormat,
                bufferSize
            )
            
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                Timber.e("AudioRecord initialization failed")
                cleanup()
                return@withContext false
            }
            
            // Start recording
            audioRecord?.startRecording()
            markRecordingStarted(outputFile)
            
            // Start recording loop in background
            recordingJob = CoroutineScope(Dispatchers.IO).launch {
                recordAudioToFile(outputFile, quality, config)
            }
            
            Timber.d("Legacy AudioSource recording started")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start Legacy AudioSource recording")
            cleanup()
            false
        }
    }
    
    override suspend fun stopRecording(): Boolean = withContext(Dispatchers.IO) {
        try {
            recordingJob?.cancel()
            recordingJob = null
            
            audioRecord?.let { record ->
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                record.release()
            }
            audioRecord = null
            
            markRecordingStopped()
            Timber.d("Legacy AudioSource recording stopped")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to stop Legacy AudioSource recording")
            false
        }
    }
    
    override suspend fun cleanup() {
        super.cleanup()
        
        recordingJob?.cancel()
        recordingJob = null
        
        audioRecord?.let { record ->
            try {
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                record.release()
            } catch (e: Exception) {
                Timber.w(e, "Error releasing AudioRecord")
            }
        }
        audioRecord = null
        
        Timber.d("Legacy AudioSource recording method cleaned up")
    }
    
    /**
     * Find a working recording configuration by testing different options
     */
    private suspend fun findWorkingConfiguration(): RecordingConfig? = withContext(Dispatchers.IO) {
        val configurations = listOf(
            // Primary configuration - stereo, high quality
            RecordingConfig(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT),
            
            // Fallback configurations
            RecordingConfig(FALLBACK_SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT),
            RecordingConfig(SAMPLE_RATE, FALLBACK_CHANNEL_CONFIG, AUDIO_FORMAT),
            RecordingConfig(FALLBACK_SAMPLE_RATE, FALLBACK_CHANNEL_CONFIG, AUDIO_FORMAT),
            
            // Lower quality fallbacks
            RecordingConfig(16000, FALLBACK_CHANNEL_CONFIG, AUDIO_FORMAT),
            RecordingConfig(8000, FALLBACK_CHANNEL_CONFIG, AUDIO_FORMAT)
        )
        
        for (config in configurations) {
            if (testConfiguration(config)) {
                return@withContext config
            }
        }
        
        return@withContext null
    }
    
    /**
     * Test if a recording configuration works
     */
    private fun testConfiguration(config: RecordingConfig): Boolean {
        return try {
            val bufferSize = AudioRecord.getMinBufferSize(
                config.sampleRate,
                config.channelConfig,
                config.audioFormat
            )
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                return false
            }
            
            val testRecord = AudioRecord(
                MediaRecorder.AudioSource.VOICE_CALL,
                config.sampleRate,
                config.channelConfig,
                config.audioFormat,
                bufferSize
            )
            
            val isInitialized = testRecord.state == AudioRecord.STATE_INITIALIZED
            testRecord.release()
            
            Timber.d("Configuration test - $config: ${if (isInitialized) "SUCCESS" else "FAILED"}")
            isInitialized
            
        } catch (e: Exception) {
            Timber.w(e, "Configuration test failed for $config")
            false
        }
    }
    
    /**
     * Record audio data to file
     */
    private suspend fun recordAudioToFile(
        outputFile: File, 
        quality: RecordingQuality,
        config: RecordingConfig
    ) {
        var fileOutputStream: FileOutputStream? = null
        
        try {
            fileOutputStream = FileOutputStream(outputFile)
            val buffer = ByteArray(1024)
            
            while (isCurrentlyRecording && !Thread.currentThread().isInterrupted) {
                val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (bytesRead > 0) {
                    fileOutputStream.write(buffer, 0, bytesRead)
                } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                    Timber.e("AudioRecord read error: Invalid operation")
                    break
                } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                    Timber.e("AudioRecord read error: Bad value")
                    break
                }
                
                // Small delay to prevent excessive CPU usage
                delay(10)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error recording audio to file")
        } finally {
            try {
                fileOutputStream?.close()
            } catch (e: Exception) {
                Timber.w(e, "Error closing file output stream")
            }
        }
    }
    
    /**
     * Data class representing a recording configuration
     */
    private data class RecordingConfig(
        val sampleRate: Int,
        val channelConfig: Int,
        val audioFormat: Int
    ) {
        override fun toString(): String {
            val channelStr = if (channelConfig == AudioFormat.CHANNEL_IN_STEREO) "STEREO" else "MONO"
            return "RecordingConfig(sampleRate=$sampleRate, channel=$channelStr, format=$audioFormat)"
        }
    }
    
    /**
     * Get device compatibility information
     */
    fun getCompatibilityInfo(): String {
        return """
            Legacy AudioSource.VOICE_CALL Method:
            
            Supported Android Versions: 7.1 - 9 (API 25-28)
            Current Device: Android ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})
            
            Known Limitations:
            • Deprecated since Android 10
            • May not work on all devices
            • OEM restrictions may apply
            • Audio quality varies by device
            • Some devices only record one side
            
            Device Info:
            • Manufacturer: ${Build.MANUFACTURER}
            • Model: ${Build.MODEL}
            • Brand: ${Build.BRAND}
        """.trimIndent()
    }
}
