{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-74:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e314a50eb315442fe0afa641180e00d\\transformed\\material-1.11.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1066,1157,1224,1283,1373,1436,1501,1565,1634,1696,1750,1865,1923,1984,2038,2111,2238,2324,2408,2541,2616,2692,2825,2911,2992,3046,3098,3164,3237,3317,3402,3482,3553,3629,3708,3777,3884,3980,4058,4153,4249,4323,4398,4497,4548,4630,4697,4784,4874,4936,5000,5063,5130,5232,5337,5434,5536,5594,5650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "263,337,409,491,597,695,794,914,998,1061,1152,1219,1278,1368,1431,1496,1560,1629,1691,1745,1860,1918,1979,2033,2106,2233,2319,2403,2536,2611,2687,2820,2906,2987,3041,3093,3159,3232,3312,3397,3477,3548,3624,3703,3772,3879,3975,4053,4148,4244,4318,4393,4492,4543,4625,4692,4779,4869,4931,4995,5058,5125,5227,5332,5429,5531,5589,5645,5723"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,3623,3697,3769,3851,3957,4777,4876,4996,5533,9353,9444,9698,15995,16085,16148,16213,16277,16346,16408,16462,16577,16635,16696,16750,16823,16950,17036,17120,17253,17328,17404,17537,17623,17704,17758,17810,17876,17949,18029,18114,18194,18265,18341,18420,18489,18596,18692,18770,18865,18961,19035,19110,19209,19260,19342,19409,19496,19586,19648,19712,19775,19842,19944,20049,20146,20248,20306,20535", "endLines": "22,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "endColumns": "12,73,71,81,105,97,98,119,83,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,83,132,74,75,132,85,80,53,51,65,72,79,84,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77", "endOffsets": "935,3692,3764,3846,3952,4050,4871,4991,5075,5591,9439,9506,9752,16080,16143,16208,16272,16341,16403,16457,16572,16630,16691,16745,16818,16945,17031,17115,17248,17323,17399,17532,17618,17699,17753,17805,17871,17944,18024,18109,18189,18260,18336,18415,18484,18591,18687,18765,18860,18956,19030,19105,19204,19255,19337,19404,19491,19581,19643,19707,19770,19837,19939,20044,20141,20243,20301,20357,20608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,489,677,759,839,922,1021,1123,1200,1262,1351,1439,1503,1567,1627,1694,1807,1921,2032,2105,2183,2252,2328,2410,2490,2553,2616,2669,2727,2775,2836,2898,2960,3025,3087,3154,3217,3283,3350,3417,3470,3532,3608,3684", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "281,484,672,754,834,917,1016,1118,1195,1257,1346,1434,1498,1562,1622,1689,1802,1916,2027,2100,2178,2247,2323,2405,2485,2548,2611,2664,2722,2770,2831,2893,2955,3020,3082,3149,3212,3278,3345,3412,3465,3527,3603,3679,3733"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,584,5596,5678,5758,5841,5940,6042,6119,6181,6270,6358,6422,6486,6546,6613,6726,6840,6951,7024,7102,7171,7247,7329,7409,7472,8231,8284,8342,8390,8451,8513,8575,8640,8702,8769,8832,8898,8965,9032,9085,9147,9223,9299", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,81,79,82,98,101,76,61,88,87,63,63,59,66,112,113,110,72,77,68,75,81,79,62,62,52,57,47,60,61,61,64,61,66,62,65,66,66,52,61,75,75,53", "endOffsets": "376,579,767,5673,5753,5836,5935,6037,6114,6176,6265,6353,6417,6481,6541,6608,6721,6835,6946,7019,7097,7166,7242,7324,7404,7467,7530,8279,8337,8385,8446,8508,8570,8635,8697,8764,8827,8893,8960,9027,9080,9142,9218,9294,9348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "253,254", "startColumns": "4,4", "startOffsets": "21519,21604", "endColumns": "84,84", "endOffsets": "21599,21684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,266,334,409,477,576,672", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "120,185,261,329,404,472,571,667,746"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7535,7605,7670,7746,7814,7889,7957,8056,8152", "endColumns": "69,64,75,67,74,67,98,95,78", "endOffsets": "7600,7665,7741,7809,7884,7952,8051,8147,8226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,406,518,613,712,828,967,1087,1229,1314,1418,1512,1612,1726,1854,1963,2098,2230,2360,2539,2665,2787,2913,3048,3143,3239,3366,3496,3597,3702,3809,3944,4085,4194,4296,4371,4468,4564,4671,4756,4843,4941,5021,5105,5205,5308,5406,5506,5593,5699,5798,5901,6019,6099,6199", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "164,276,401,513,608,707,823,962,1082,1224,1309,1413,1507,1607,1721,1849,1958,2093,2225,2355,2534,2660,2782,2908,3043,3138,3234,3361,3491,3592,3697,3804,3939,4080,4189,4291,4366,4463,4559,4666,4751,4838,4936,5016,5100,5200,5303,5401,5501,5588,5694,5793,5896,6014,6094,6194,6288"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9757,9871,9983,10108,10220,10315,10414,10530,10669,10789,10931,11016,11120,11214,11314,11428,11556,11665,11800,11932,12062,12241,12367,12489,12615,12750,12845,12941,13068,13198,13299,13404,13511,13646,13787,13896,13998,14073,14170,14266,14373,14458,14545,14643,14723,14807,14907,15010,15108,15208,15295,15401,15500,15603,15721,15801,15901", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "9866,9978,10103,10215,10310,10409,10525,10664,10784,10926,11011,11115,11209,11309,11423,11551,11660,11795,11927,12057,12236,12362,12484,12610,12745,12840,12936,13063,13193,13294,13399,13506,13641,13782,13891,13993,14068,14165,14261,14368,14453,14540,14638,14718,14802,14902,15005,15103,15203,15290,15396,15495,15598,15716,15796,15896,15990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,1001,1084,1159,1234,1309,1384,1460,1526", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,996,1079,1154,1229,1304,1379,1455,1521,1637"}, "to": {"startLines": "65,66,67,68,69,124,125,239,240,242,243,245,246,247,248,250,251,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5080,5172,5254,5347,5446,9511,9597,20362,20449,20613,20696,20860,20935,21010,21085,21261,21337,21403", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "5167,5249,5342,5441,5528,9592,9693,20444,20530,20691,20774,20930,21005,21080,21155,21332,21398,21514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87d78cd91b4d183bfa1262f9176c8808\\transformed\\appcompat-1.6.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "940,1047,1151,1258,1345,1445,1565,1643,1720,1811,1904,1999,2093,2193,2286,2381,2475,2566,2657,2737,2843,2944,3041,3150,3250,3360,3520,20779", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "1042,1146,1253,1340,1440,1560,1638,1715,1806,1899,1994,2088,2188,2281,2376,2470,2561,2652,2732,2838,2939,3036,3145,3245,3355,3515,3618,20855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,353,455,557,655,777", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "148,251,348,450,552,650,772,873"}, "to": {"startLines": "55,56,57,58,59,60,61,249", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4055,4153,4256,4353,4455,4557,4655,21160", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "4148,4251,4348,4450,4552,4650,4772,21256"}}]}]}