@echo off
echo ========================================
echo Call Recording App - Build and Install
echo ========================================
echo.

echo Cleaning previous build...
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)

echo.
echo Building debug APK...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Installing on device...
call gradlew installDebug
if %errorlevel% neq 0 (
    echo ERROR: Install failed! Make sure device is connected and USB debugging is enabled.
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS! App installed successfully!
echo ========================================
echo.
echo APK location: app\build\outputs\apk\debug\app-debug.apk
echo.
pause
