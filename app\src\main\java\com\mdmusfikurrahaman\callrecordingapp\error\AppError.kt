package com.mdmusfikurrahaman.callrecordingapp.error

/**
 * Data classes for representing different types of application errors
 */

/**
 * Main application error class
 */
data class AppError(
    val throwable: Throwable,
    val context: ErrorContext,
    val userMessage: String,
    val technicalMessage: String,
    val recoveryActions: List<RecoveryAction>,
    val timestamp: Long,
    val deviceInfo: DeviceInfo
)

/**
 * Device information for error reporting
 */
data class DeviceInfo(
    val manufacturer: String,
    val model: String,
    val androidVersion: String,
    val sdkVersion: Int,
    val brand: String,
    val device: String
)

/**
 * Permission-related error
 */
class PermissionError(
    val missingPermissions: List<String>,
    val isRequired: Boolean
) : Exception("Missing permissions: ${missingPermissions.joinToString(", ")}")

/**
 * Compatibility-related error
 */
class CompatibilityError(
    val feature: String,
    val minSdkVersion: Int,
    val currentSdkVersion: Int
) : Exception("$feature requires API $minSdkVersion, current: $currentSdkVersion")

/**
 * Recording-related error
 */
class RecordingError(
    val method: String,
    val reason: String,
    cause: Throwable? = null
) : Exception("Recording failed with $method: $reason", cause)

/**
 * Storage-related error
 */
class StorageError(
    val availableSpaceMB: Long,
    val requiredSpaceMB: Long,
    val operation: String
) : Exception("Storage error during $operation: available=${availableSpaceMB}MB, required=${requiredSpaceMB}MB")

/**
 * Audio playback error
 */
class PlaybackError(
    val filePath: String,
    val reason: String,
    cause: Throwable? = null
) : Exception("Playback failed for $filePath: $reason", cause)

/**
 * Legal compliance error
 */
class LegalComplianceError(
    val requirement: String,
    val action: String
) : Exception("Legal compliance error: $requirement not met for $action")

/**
 * Network-related error
 */
class NetworkError(
    val operation: String,
    val httpCode: Int? = null,
    cause: Throwable? = null
) : Exception("Network error during $operation${httpCode?.let { " (HTTP $it)" } ?: ""}", cause)

/**
 * Database-related error
 */
class DatabaseError(
    val operation: String,
    val table: String? = null,
    cause: Throwable? = null
) : Exception("Database error during $operation${table?.let { " on $it" } ?: ""}", cause)
