package com.mdmusfikurrahaman.callrecordingapp.util

import android.content.Context
import android.provider.ContactsContract
import android.telephony.PhoneNumberUtils
import timber.log.Timber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Utility class for contact-related operations
 */
object ContactUtils {
    
    /**
     * Get contact name for a phone number
     */
    suspend fun getContactName(context: Context, phoneNumber: String): String? = withContext(Dispatchers.IO) {
        try {
            if (!PermissionUtils.hasPermission(context, android.Manifest.permission.READ_CONTACTS)) {
                return@withContext null
            }
            
            val uri = ContactsContract.PhoneLookup.CONTENT_FILTER_URI.buildUpon()
                .appendPath(phoneNumber)
                .build()
            
            val projection = arrayOf(ContactsContract.PhoneLookup.DISPLAY_NAME)
            
            context.contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(ContactsContract.PhoneLookup.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        return@withContext cursor.getString(nameIndex)
                    }
                }
            }
            
            return@withContext null
            
        } catch (e: Exception) {
            Timber.e(e, "Error getting contact name for $phoneNumber")
            return@withContext null
        }
    }
    
    /**
     * Format phone number for display
     */
    fun formatPhoneNumber(phoneNumber: String): String {
        return try {
            PhoneNumberUtils.formatNumber(phoneNumber) ?: phoneNumber
        } catch (e: Exception) {
            phoneNumber
        }
    }
    
    /**
     * Check if two phone numbers are the same
     */
    fun arePhoneNumbersEqual(number1: String?, number2: String?): Boolean {
        if (number1.isNullOrBlank() || number2.isNullOrBlank()) {
            return false
        }
        
        return try {
            PhoneNumberUtils.compare(number1, number2)
        } catch (e: Exception) {
            // Fallback to simple string comparison
            val clean1 = number1.replace(Regex("[^\\d]"), "")
            val clean2 = number2.replace(Regex("[^\\d]"), "")
            clean1 == clean2
        }
    }
    
    /**
     * Get display name for a phone number (contact name or formatted number)
     */
    suspend fun getDisplayName(context: Context, phoneNumber: String): String {
        val contactName = getContactName(context, phoneNumber)
        return contactName ?: formatPhoneNumber(phoneNumber)
    }
    
    /**
     * Check if a phone number is valid
     */
    fun isValidPhoneNumber(phoneNumber: String?): Boolean {
        if (phoneNumber.isNullOrBlank()) return false
        
        return try {
            PhoneNumberUtils.isGlobalPhoneNumber(phoneNumber)
        } catch (e: Exception) {
            // Fallback validation
            val cleanNumber = phoneNumber.replace(Regex("[^\\d+]"), "")
            cleanNumber.length >= 7 && cleanNumber.length <= 15
        }
    }
}
