package com.mdmusfikurrahaman.callrecordingapp.presentation.main

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.mdmusfikurrahaman.callrecordingapp.R
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.presentation.main.components.RecordingItem
import com.mdmusfikurrahaman.callrecordingapp.presentation.main.components.SearchBar
import com.mdmusfikurrahaman.callrecordingapp.presentation.main.components.PermissionRequestDialog
import com.mdmusfikurrahaman.callrecordingapp.presentation.main.components.EmptyRecordingsView

/**
 * Main screen of the Call Recording App
 * 
 * This screen displays:
 * - List of call recordings
 * - Search functionality
 * - Permission management
 * - Navigation to settings
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    
    // Handle permission requests
    LaunchedEffect(Unit) {
        viewModel.checkPermissions(context)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.app_name)) },
                actions = {
                    IconButton(onClick = { viewModel.toggleSearch() }) {
                        Icon(Icons.Default.Search, contentDescription = "Search")
                    }
                    IconButton(onClick = { viewModel.navigateToSettings() }) {
                        Icon(Icons.Default.Settings, contentDescription = "Settings")
                    }
                }
            )
        },
        floatingActionButton = {
            if (uiState.hasRequiredPermissions) {
                FloatingActionButton(
                    onClick = { viewModel.startManualRecording() }
                ) {
                    Icon(Icons.Default.Add, contentDescription = "Start Recording")
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Search bar
            if (uiState.showSearch) {
                SearchBar(
                    query = uiState.searchQuery,
                    onQueryChange = viewModel::updateSearchQuery,
                    onSearch = viewModel::performSearch,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
            
            // Main content
            when {
                !uiState.hasRequiredPermissions -> {
                    PermissionRequestContent(
                        onRequestPermissions = { viewModel.requestPermissions(context) },
                        onOpenSettings = { viewModel.openAppSettings(context) }
                    )
                }
                
                uiState.isLoading -> {
                    LoadingContent()
                }
                
                uiState.recordings.isEmpty() -> {
                    EmptyRecordingsView(
                        hasSearchQuery = uiState.searchQuery.isNotBlank(),
                        onClearSearch = { viewModel.updateSearchQuery("") }
                    )
                }
                
                else -> {
                    RecordingsListContent(
                        recordings = uiState.recordings,
                        onPlayRecording = viewModel::playRecording,
                        onShareRecording = viewModel::shareRecording,
                        onDeleteRecording = viewModel::deleteRecording,
                        onToggleFavorite = viewModel::toggleFavorite
                    )
                }
            }
        }
    }
    
    // Permission request dialog
    if (uiState.showPermissionDialog) {
        PermissionRequestDialog(
            onDismiss = { viewModel.dismissPermissionDialog() },
            onRequestPermissions = { viewModel.requestPermissions(context) },
            onOpenSettings = { viewModel.openAppSettings(context) }
        )
    }
    
    // Error snackbar
    uiState.errorMessage?.let { message ->
        LaunchedEffect(message) {
            // Show snackbar for error
            // This would typically be handled by a SnackbarHost
        }
    }
}

@Composable
private fun PermissionRequestContent(
    onRequestPermissions: () -> Unit,
    onOpenSettings: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Settings,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Permissions Required",
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "This app needs several permissions to record calls and manage recordings.",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = onRequestPermissions,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Grant Permissions")
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        TextButton(onClick = onOpenSettings) {
            Text("Open Settings")
        }
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Composable
private fun RecordingsListContent(
    recordings: List<CallRecording>,
    onPlayRecording: (CallRecording) -> Unit,
    onShareRecording: (CallRecording) -> Unit,
    onDeleteRecording: (CallRecording) -> Unit,
    onToggleFavorite: (CallRecording) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(
            items = recordings,
            key = { it.id }
        ) { recording ->
            RecordingItem(
                recording = recording,
                onPlay = { onPlayRecording(recording) },
                onShare = { onShareRecording(recording) },
                onDelete = { onDeleteRecording(recording) },
                onToggleFavorite = { onToggleFavorite(recording) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            )
        }
    }
}
