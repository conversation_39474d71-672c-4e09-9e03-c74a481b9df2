package com.mdmusfikurrahaman.callrecordingapp.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo
import androidx.room.ForeignKey
import androidx.room.Index

/**
 * Entity representing a call transcript in the database
 * 
 * This entity stores AI-generated transcripts of call recordings
 * with speaker identification and confidence scores
 */
@Entity(
    tableName = "call_transcripts",
    foreignKeys = [
        ForeignKey(
            entity = CallRecording::class,
            parentColumns = ["id"],
            childColumns = ["recording_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["recording_id"]),
        Index(value = ["created_at"])
    ]
)
data class CallTranscript(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "recording_id")
    val recordingId: Long,
    
    @ColumnInfo(name = "transcript_text")
    val transcriptText: String,
    
    @ColumnInfo(name = "confidence_score")
    val confidenceScore: Float? = null, // AI confidence score (0.0 - 1.0)
    
    @ColumnInfo(name = "language")
    val language: String? = null, // Detected language code (e.g., "en-US")
    
    @ColumnInfo(name = "processing_time")
    val processingTime: Long? = null, // Time taken to generate transcript in ms
    
    @ColumnInfo(name = "ai_model")
    val aiModel: String? = null, // AI model used (e.g., "whisper-1")
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * Entity for storing detailed transcript segments with timestamps
 * Useful for displaying transcript with playback synchronization
 */
@Entity(
    tableName = "transcript_segments",
    foreignKeys = [
        ForeignKey(
            entity = CallTranscript::class,
            parentColumns = ["id"],
            childColumns = ["transcript_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["transcript_id"]),
        Index(value = ["start_time"])
    ]
)
data class TranscriptSegment(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "transcript_id")
    val transcriptId: Long,
    
    @ColumnInfo(name = "text")
    val text: String,
    
    @ColumnInfo(name = "start_time")
    val startTime: Long, // Start time in milliseconds from beginning of call
    
    @ColumnInfo(name = "end_time")
    val endTime: Long, // End time in milliseconds from beginning of call
    
    @ColumnInfo(name = "speaker")
    val speaker: Speaker, // Who was speaking
    
    @ColumnInfo(name = "confidence")
    val confidence: Float? = null // Confidence for this segment
)

/**
 * Enum representing the speaker in a call
 */
enum class Speaker {
    USER,      // The device owner
    CALLER,    // The other party
    UNKNOWN    // Could not determine speaker
}
