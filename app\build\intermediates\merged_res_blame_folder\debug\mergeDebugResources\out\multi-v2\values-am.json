{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-74:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7187,7249,7307,7369,7429,7501,7564,7653,7734", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "7244,7302,7364,7424,7496,7559,7648,7729,7795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,85", "endOffsets": "137,223"}, "to": {"startLines": "253,254", "startColumns": "4,4", "startOffsets": "20327,20414", "endColumns": "86,85", "endOffsets": "20409,20495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87d78cd91b4d183bfa1262f9176c8808\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "920,1018,1116,1222,1308,1411,1528,1606,1682,1773,1866,1958,2052,2152,2245,2340,2433,2524,2615,2695,2795,2895,2991,3093,3193,3292,3442,19597", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "1013,1111,1217,1303,1406,1523,1601,1677,1768,1861,1953,2047,2147,2240,2335,2428,2519,2610,2690,2790,2890,2986,3088,3188,3287,3437,3533,19672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e314a50eb315442fe0afa641180e00d\\transformed\\material-1.11.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,970,1055,1117,1175,1260,1323,1385,1443,1509,1571,1626,1722,1779,1838,1894,1961,2066,2146,2227,2356,2429,2500,2614,2696,2772,2823,2874,2940,3006,3079,3160,3235,3303,3376,3447,3514,3612,3697,3764,3851,3939,4013,4081,4166,4217,4295,4359,4439,4521,4583,4647,4710,4776,4871,4966,5051,5142,5197,5252", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,113,81,75,50,50,65,65,72,80,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75", "endOffsets": "256,327,395,470,552,633,722,824,901,965,1050,1112,1170,1255,1318,1380,1438,1504,1566,1621,1717,1774,1833,1889,1956,2061,2141,2222,2351,2424,2495,2609,2691,2767,2818,2869,2935,3001,3074,3155,3230,3298,3371,3442,3509,3607,3692,3759,3846,3934,4008,4076,4161,4212,4290,4354,4434,4516,4578,4642,4705,4771,4866,4961,5046,5137,5192,5247,5323"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "759,3538,3609,3677,3752,3834,4602,4691,4793,5300,8876,8961,9184,15125,15210,15273,15335,15393,15459,15521,15576,15672,15729,15788,15844,15911,16016,16096,16177,16306,16379,16450,16564,16646,16722,16773,16824,16890,16956,17029,17110,17185,17253,17326,17397,17464,17562,17647,17714,17801,17889,17963,18031,18116,18167,18245,18309,18389,18471,18533,18597,18660,18726,18821,18916,19001,19092,19147,19362", "endLines": "22,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,113,81,75,50,50,65,65,72,80,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75", "endOffsets": "915,3604,3672,3747,3829,3910,4686,4788,4865,5359,8956,9018,9237,15205,15268,15330,15388,15454,15516,15571,15667,15724,15783,15839,15906,16011,16091,16172,16301,16374,16445,16559,16641,16717,16768,16819,16885,16951,17024,17105,17180,17248,17321,17392,17459,17557,17642,17709,17796,17884,17958,18026,18111,18162,18240,18304,18384,18466,18528,18592,18655,18721,18816,18911,18996,19087,19142,19197,19433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1719,1821,1922,1991,2067,2135,2201,2280,2360,2422,2487,2540,2597,2643,2704,2762,2837,2896,2958,3017,3074,3138,3201,3265,3315,3371,3441,3511", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1714,1816,1917,1986,2062,2130,2196,2275,2355,2417,2482,2535,2592,2638,2699,2757,2832,2891,2953,3012,3069,3133,3196,3260,3310,3366,3436,3506,3558"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,378,577,5364,5444,5525,5601,5686,5767,5833,5895,5979,6062,6129,6192,6253,6319,6419,6521,6622,6691,6767,6835,6901,6980,7060,7122,7800,7853,7910,7956,8017,8075,8150,8209,8271,8330,8387,8451,8514,8578,8628,8684,8754,8824", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "373,572,754,5439,5520,5596,5681,5762,5828,5890,5974,6057,6124,6187,6248,6314,6414,6516,6617,6686,6762,6830,6896,6975,7055,7117,7182,7848,7905,7951,8012,8070,8145,8204,8266,8325,8382,8446,8509,8573,8623,8679,8749,8819,8871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "55,56,57,58,59,60,61,249", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3915,4008,4108,4205,4304,4400,4502,19965", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "4003,4103,4200,4299,4395,4497,4597,20061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,934,1015,1085,1158,1231,1303,1383,1448", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,929,1010,1080,1153,1226,1298,1378,1443,1559"}, "to": {"startLines": "65,66,67,68,69,124,125,239,240,242,243,245,246,247,248,250,251,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4953,5030,5122,5218,9023,9101,19202,19284,19438,19516,19677,19747,19820,19893,20066,20146,20211", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "4948,5025,5117,5213,5295,9096,9179,19279,19357,19511,19592,19742,19815,19888,19960,20141,20206,20322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4334,4418,4503,4605,4686,4769,4869,4966,5061,5156,5241,5343,5442,5541,5659,5740,5841", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4329,4413,4498,4600,4681,4764,4864,4961,5056,5151,5236,5338,5437,5536,5654,5735,5836,5933"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9242,9353,9463,9571,9678,9772,9862,9969,10097,10207,10336,10418,10516,10603,10696,10806,10925,11028,11151,11276,11400,11548,11664,11777,11891,12006,12094,12189,12299,12418,12513,12615,12717,12837,12963,13067,13163,13237,13330,13422,13521,13605,13690,13792,13873,13956,14056,14153,14248,14343,14428,14530,14629,14728,14846,14927,15028", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "9348,9458,9566,9673,9767,9857,9964,10092,10202,10331,10413,10511,10598,10691,10801,10920,11023,11146,11271,11395,11543,11659,11772,11886,12001,12089,12184,12294,12413,12508,12610,12712,12832,12958,13062,13158,13232,13325,13417,13516,13600,13685,13787,13868,13951,14051,14148,14243,14338,14423,14525,14624,14723,14841,14922,15023,15120"}}]}]}