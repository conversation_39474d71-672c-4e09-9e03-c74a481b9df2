plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
}

android {
    namespace = "com.mdmusfikurrahaman.callrecordingapp"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.mdmusfikurrahaman.callrecordingapp"
        minSdk = 24
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        debug {
            isDebuggable = true
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
        dataBinding = true
    }
}

// Allow references to generated code - Temporarily disabled
// kapt {
//     correctErrorTypes = true
//     useBuildCache = true
//
//     arguments {
//         arg("dagger.hilt.android.internal.disableAndroidSuperclassValidation", "true")
//     }
// }

dependencies {
    // Material Design
    implementation("com.google.android.material:material:1.11.0")

    // Core Android
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    implementation("androidx.cardview:cardview:1.0.0")
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")
    implementation("androidx.coordinatorlayout:coordinatorlayout:1.2.0")

    // MVVM and Architecture
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
    implementation("androidx.navigation:navigation-fragment-ktx:2.7.6")
    implementation("androidx.navigation:navigation-ui-ktx:2.7.6")
    implementation("androidx.fragment:fragment-ktx:1.6.2")

    // Hilt Dependency Injection - Temporarily disabled for build fix
    // implementation(libs.hilt.android)
    // kapt(libs.hilt.android.compiler)

    // Room Database - Temporarily disabled for build fix
    // implementation(libs.androidx.room.runtime)
    // implementation(libs.androidx.room.ktx)
    // kapt(libs.androidx.room.compiler)

    // Media and Recording - Temporarily disabled
    // implementation(libs.exoplayer.core)
    // implementation(libs.exoplayer.ui)

    // Permissions - Using built-in Android permission system

    // Coroutines - Temporarily disabled
    // implementation(libs.kotlinx.coroutines.android)

    // Networking (for AI integration) - Temporarily disabled
    // implementation(libs.retrofit)
    // implementation(libs.retrofit.converter.moshi)
    // implementation(libs.okhttp)
    // implementation(libs.okhttp.logging.interceptor)
    // implementation(libs.moshi)
    // implementation(libs.moshi.kotlin)

    // Utilities - Temporarily disabled
    // implementation(libs.timber)

    // Testing
    testImplementation(libs.junit)
    testImplementation(libs.mockk)
    testImplementation(libs.turbine)
    testImplementation(libs.kotlinx.coroutines.test)

    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}