package com.mdmusfikurrahaman.callrecordingapp.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo

/**
 * Entity representing user preferences and settings
 * 
 * This entity stores user configuration for the app including:
 * - Recording settings
 * - Privacy preferences
 * - UI preferences
 * - Legal consent status
 */
@Entity(tableName = "user_preferences")
data class UserPreferences(
    @PrimaryKey
    val id: Int = 1, // Single row table
    
    // Recording Settings
    @ColumnInfo(name = "recording_quality")
    val recordingQuality: RecordingQuality = RecordingQuality.MEDIUM,
    
    @ColumnInfo(name = "auto_record_enabled")
    val autoRecordEnabled: Boolean = true,
    
    @ColumnInfo(name = "record_incoming_calls")
    val recordIncomingCalls: Boolean = true,
    
    @ColumnInfo(name = "record_outgoing_calls")
    val recordOutgoingCalls: Boolean = true,
    
    // Storage Settings
    @ColumnInfo(name = "auto_delete_enabled")
    val autoDeleteEnabled: Boolean = false,
    
    @ColumnInfo(name = "auto_delete_days")
    val autoDeleteDays: Int = 30, // Delete recordings older than X days
    
    @ColumnInfo(name = "max_storage_mb")
    val maxStorageMb: Int = 1000, // Maximum storage in MB
    
    // Privacy Settings
    @ColumnInfo(name = "show_consent_dialog")
    val showConsentDialog: Boolean = true,
    
    @ColumnInfo(name = "legal_disclaimer_accepted")
    val legalDisclaimerAccepted: Boolean = false,
    
    @ColumnInfo(name = "privacy_policy_accepted")
    val privacyPolicyAccepted: Boolean = false,
    
    @ColumnInfo(name = "consent_timestamp")
    val consentTimestamp: Long? = null,
    
    // AI Features
    @ColumnInfo(name = "ai_transcription_enabled")
    val aiTranscriptionEnabled: Boolean = false,
    
    @ColumnInfo(name = "openai_api_key")
    val openaiApiKey: String? = null, // Encrypted API key
    
    @ColumnInfo(name = "auto_transcribe")
    val autoTranscribe: Boolean = false,
    
    // UI Preferences
    @ColumnInfo(name = "theme_mode")
    val themeMode: ThemeMode = ThemeMode.SYSTEM,
    
    @ColumnInfo(name = "show_recording_indicator")
    val showRecordingIndicator: Boolean = true,
    
    @ColumnInfo(name = "vibrate_on_recording_start")
    val vibrateOnRecordingStart: Boolean = true,
    
    // Notification Settings
    @ColumnInfo(name = "show_recording_notifications")
    val showRecordingNotifications: Boolean = true,
    
    @ColumnInfo(name = "notification_sound_enabled")
    val notificationSoundEnabled: Boolean = false,
    
    // Backup Settings
    @ColumnInfo(name = "backup_enabled")
    val backupEnabled: Boolean = false,
    
    @ColumnInfo(name = "backup_wifi_only")
    val backupWifiOnly: Boolean = true,
    
    // Timestamps
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * Enum representing theme modes
 */
enum class ThemeMode {
    LIGHT,
    DARK,
    SYSTEM
}
