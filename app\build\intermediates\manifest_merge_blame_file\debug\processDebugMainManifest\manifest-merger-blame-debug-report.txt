1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdmusfikurrahaman.callrecordingapp.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="35" />
10
11    <!-- Core recording permissions -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:5-75
13-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_CALL_LOG" />
14-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:5-72
14-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:22-69
15
16    <!-- Service permissions -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
18-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:5-94
18-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:22-91
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
19-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:5-88
19-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:22-85
20
21    <!-- Storage permissions -->
22    <uses-permission
22-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:5-17:38
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:22-78
24        android:maxSdkVersion="28" />
24-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:17:9-35
25    <uses-permission
25-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:5-19:38
26        android:name="android.permission.READ_EXTERNAL_STORAGE"
26-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:22-77
27        android:maxSdkVersion="32" />
27-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:19:9-35
28
29    <!-- Notification permission for Android 13+ -->
30    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
30-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:5-77
30-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:22-74
31
32    <!-- MediaProjection for screen/audio capture on Android 10+ -->
33    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
33-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:5-78
33-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:22-75
34
35    <!-- Accessibility service permission -->
36    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
36-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:5-29:47
36-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:22-82
37
38    <!-- Phone state and call management -->
39    <uses-permission android:name="android.permission.READ_CONTACTS" />
39-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:5-72
39-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:22-69
40    <uses-permission
40-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:5-34:38
41        android:name="android.permission.PROCESS_OUTGOING_CALLS"
41-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:22-78
42        android:maxSdkVersion="28" />
42-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:34:9-35
43
44    <!-- Wake lock for background recording -->
45    <uses-permission android:name="android.permission.WAKE_LOCK" />
45-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:5-68
45-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:22-65
46
47    <!-- Internet for AI transcription (optional) -->
48    <uses-permission android:name="android.permission.INTERNET" />
48-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:5-67
48-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:22-64
49    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
49-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:5-79
49-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:22-76
50
51    <permission
51-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
52        android:name="com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
56
57    <application
57-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
58        android:name="com.mdmusfikurrahaman.callrecordingapp.CallRecordingApplication"
58-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:44:9-49
59        android:allowBackup="true"
59-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:45:9-35
60        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
60-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
61        android:dataExtractionRules="@xml/data_extraction_rules"
61-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:46:9-65
62        android:debuggable="true"
63        android:extractNativeLibs="false"
64        android:fullBackupContent="@xml/backup_rules"
64-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:47:9-54
65        android:icon="@mipmap/ic_launcher"
65-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:48:9-43
66        android:label="@string/app_name"
66-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:49:9-41
67        android:requestLegacyExternalStorage="true"
67-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:53:9-52
68        android:roundIcon="@mipmap/ic_launcher_round"
68-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:50:9-54
69        android:supportsRtl="true"
69-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:51:9-35
70        android:testOnly="true"
71        android:theme="@style/Theme.CallRecordingApp" >
71-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:52:9-54
72
73        <!-- Main Activity -->
74        <activity
74-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:56:9-66:20
75            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity"
75-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:57:13-54
76            android:exported="true"
76-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:58:13-36
77            android:label="@string/app_name"
77-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:59:13-45
78            android:launchMode="singleTop"
78-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:61:13-43
79            android:theme="@style/Theme.CallRecordingApp" >
79-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:60:13-58
80            <intent-filter>
80-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:62:13-65:29
81                <action android:name="android.intent.action.MAIN" />
81-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:17-69
81-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:25-66
82
83                <category android:name="android.intent.category.LAUNCHER" />
83-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:17-77
83-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:27-74
84            </intent-filter>
85        </activity>
86
87        <!-- Settings Activity -->
88        <activity
88-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:69:9-73:71
89            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.settings.SettingsActivity"
89-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:70:13-67
90            android:exported="false"
90-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:71:13-37
91            android:label="@string/settings"
91-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:72:13-45
92            android:parentActivityName="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity" />
92-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:73:13-68
93
94        <!-- Recording Detail Activity -->
95        <activity
95-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:76:9-80:71
96            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.detail.RecordingDetailActivity"
96-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:77:13-72
97            android:exported="false"
97-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:78:13-37
98            android:label="@string/recording_details"
98-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:79:13-54
99            android:parentActivityName="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity" />
99-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:80:13-68
100
101        <!-- Call Recording Service -->
102        <service
102-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:83:9-87:74
103            android:name="com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService"
103-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:84:13-57
104            android:enabled="true"
104-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:85:13-35
105            android:exported="false"
105-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:86:13-37
106            android:foregroundServiceType="mediaProjection|microphone" />
106-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:87:13-71
107
108        <!-- Accessibility Service for fallback recording -->
109        <service
109-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:90:9-101:19
110            android:name="com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingAccessibilityService"
110-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:91:13-70
111            android:enabled="true"
111-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:92:13-35
112            android:exported="false"
112-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:93:13-37
113            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
113-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:94:13-79
114            <intent-filter>
114-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:95:13-97:29
115                <action android:name="android.accessibilityservice.AccessibilityService" />
115-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:17-92
115-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:25-89
116            </intent-filter>
117
118            <meta-data
118-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:98:13-100:72
119                android:name="android.accessibilityservice"
119-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:99:17-60
120                android:resource="@xml/accessibility_service_config" />
120-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:100:17-69
121        </service>
122
123        <!-- Phone State Receiver -->
124        <receiver
124-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:104:9-111:20
125            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.PhoneStateReceiver"
125-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:105:13-56
126            android:enabled="true"
126-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:106:13-35
127            android:exported="false" >
127-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:107:13-37
128            <intent-filter android:priority="1000" >
128-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:13-110:29
128-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:28-51
129                <action android:name="android.intent.action.PHONE_STATE" />
129-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:17-76
129-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:25-73
130            </intent-filter>
131        </receiver>
132
133        <!-- Call Log Receiver -->
134        <receiver
134-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:114:9-121:20
135            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.CallLogReceiver"
135-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:115:13-53
136            android:enabled="true"
136-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:116:13-35
137            android:exported="false" >
137-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:117:13-37
138            <intent-filter>
138-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:118:13-120:29
139                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
139-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:17-82
139-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:25-79
140            </intent-filter>
141        </receiver>
142
143        <!-- Boot Receiver to restart service after reboot -->
144        <receiver
144-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:124:9-134:20
145            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.BootReceiver"
145-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:125:13-50
146            android:enabled="true"
146-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:126:13-35
147            android:exported="false" >
147-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:127:13-37
148            <intent-filter android:priority="1000" >
148-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:13-133:29
148-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:28-51
149                <action android:name="android.intent.action.BOOT_COMPLETED" />
149-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:17-79
149-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:25-76
150                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
150-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:17-84
150-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:25-81
151                <action android:name="android.intent.action.PACKAGE_REPLACED" />
151-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:17-81
151-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:25-78
152
153                <data android:scheme="package" />
153-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:17-50
153-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:23-47
154            </intent-filter>
155        </receiver>
156
157        <!-- File Provider for sharing recordings -->
158        <provider
159            android:name="androidx.core.content.FileProvider"
159-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:138:13-62
160            android:authorities="com.mdmusfikurrahaman.callrecordingapp.debug.fileprovider"
160-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:139:13-64
161            android:exported="false"
161-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:140:13-37
162            android:grantUriPermissions="true" >
162-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:141:13-47
163            <meta-data
163-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:142:13-144:54
164                android:name="android.support.FILE_PROVIDER_PATHS"
164-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:143:17-67
165                android:resource="@xml/file_paths" />
165-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:144:17-51
166        </provider>
167
168        <activity
168-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
169            android:name="androidx.compose.ui.tooling.PreviewActivity"
169-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
170            android:exported="true" />
170-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
171        <activity
171-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
172            android:name="androidx.activity.ComponentActivity"
172-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
173            android:exported="true" />
173-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
174
175        <provider
175-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
176            android:name="androidx.startup.InitializationProvider"
176-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
177            android:authorities="com.mdmusfikurrahaman.callrecordingapp.debug.androidx-startup"
177-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
178            android:exported="false" >
178-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
179            <meta-data
179-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.emoji2.text.EmojiCompatInitializer"
180-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
181                android:value="androidx.startup" />
181-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
182            <meta-data
182-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
183                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
183-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
184                android:value="androidx.startup" />
184-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
185            <meta-data
185-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
186                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
186-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
187                android:value="androidx.startup" />
187-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
188        </provider>
189
190        <service
190-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
191            android:name="androidx.room.MultiInstanceInvalidationService"
191-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
192            android:directBootAware="true"
192-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
193            android:exported="false" />
193-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
194
195        <receiver
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
196            android:name="androidx.profileinstaller.ProfileInstallReceiver"
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
197            android:directBootAware="false"
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
198            android:enabled="true"
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
199            android:exported="true"
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
200            android:permission="android.permission.DUMP" >
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
202                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
205                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
206            </intent-filter>
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
208                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
209            </intent-filter>
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
211                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
211-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
212            </intent-filter>
213        </receiver>
214    </application>
215
216</manifest>
