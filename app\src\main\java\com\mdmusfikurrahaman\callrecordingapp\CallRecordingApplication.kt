package com.mdmusfikurrahaman.callrecordingapp

import android.app.Application
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository

/**
 * Application class for the Call Recording App
 * 
 * This class is responsible for:
 * - Initializing Hilt dependency injection
 * - Setting up Timber logging
 * - Initializing default user preferences
 * - Setting up background cleanup tasks
 */
@HiltAndroidApp
class CallRecordingApplication : Application() {
    
    @Inject
    lateinit var repository: CallRecordingRepository
    
    // Application-level coroutine scope
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize Timber logging
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        } else {
            // In production, you might want to use a custom tree that logs to Crashlytics
            // Timber.plant(CrashlyticsTree())
        }
        
        Timber.d("CallRecordingApplication created")
        
        // Initialize app data in background
        applicationScope.launch {
            initializeAppData()
        }
    }
    
    /**
     * Initialize application data and preferences
     */
    private suspend fun initializeAppData() {
        try {
            // Initialize default user preferences if they don't exist
            repository.initializeDefaultPreferences()
            
            // Perform cleanup of old recordings based on user preferences
            repository.cleanupOldRecordings()
            
            Timber.d("App data initialization completed")
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize app data")
        }
    }
}
