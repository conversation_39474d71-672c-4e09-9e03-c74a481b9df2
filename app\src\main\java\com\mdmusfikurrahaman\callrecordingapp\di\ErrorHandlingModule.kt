package com.mdmusfikurrahaman.callrecordingapp.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.error.ErrorHandler
import com.mdmusfikurrahaman.callrecordingapp.compatibility.DeviceCompatibilityChecker

/**
 * Hilt module for providing error handling and compatibility dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object ErrorHandlingModule {
    
    @Provides
    @Singleton
    fun provideErrorHandler(
        @ApplicationContext context: Context
    ): ErrorHandler {
        return ErrorHandler(context)
    }
    
    @Provides
    @Singleton
    fun provideDeviceCompatibilityChecker(
        @ApplicationContext context: Context
    ): DeviceCompatibilityChecker {
        return DeviceCompatibilityChecker(context)
    }
}
