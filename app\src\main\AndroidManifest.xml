<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Basic permissions for call recording app -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />

    <!-- Notification permission for Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Internet permission -->
    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:name=".CallRecordingApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.CallRecordingApp"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="34">
        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.CallRecordingApp"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- TODO: Add other activities when implemented -->
        <!-- Settings Activity -->
        <!-- Recording Detail Activity -->

        <!-- TODO: Add services and receivers when implemented -->
        <!-- Call Recording Service -->
        <!-- Accessibility Service -->
        <!-- Phone State Receiver -->
        <!-- Call Log Receiver -->
        <!-- Boot Receiver -->

        <!-- TODO: Add File Provider when file sharing is implemented -->
        <!-- File Provider for sharing recordings -->

    </application>

</manifest>