{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-64:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,516,702,791,881,962,1052,1143,1220,1285,1388,1493,1558,1622,1685,1757,1875,1991,2106,2183,2272,2343,2422,2512,2603,2667,2735,2788,2846,2894,2955,3021,3088,3151,3221,3285,3343,3409,3474,3540,3592,3657,3736,3815", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,89,80,89,90,76,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "306,511,697,786,876,957,1047,1138,1215,1280,1383,1488,1553,1617,1680,1752,1870,1986,2101,2178,2267,2338,2417,2507,2598,2662,2730,2783,2841,2889,2950,3016,3083,3146,3216,3280,3338,3404,3469,3535,3587,3652,3731,3810,3866"}, "to": {"startLines": "2,11,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,361,566,1949,2038,2128,2209,2299,2390,2467,2532,2635,2740,2805,2869,2932,3004,3122,3238,3353,3430,3519,3590,3669,3759,3850,3914,4647,4700,4758,4806,4867,4933,5000,5063,5133,5197,5255,5321,5386,5452,5504,5569,5648,5727", "endLines": "10,14,18,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,88,89,80,89,90,76,64,102,104,64,63,62,71,117,115,114,76,88,70,78,89,90,63,67,52,57,47,60,65,66,62,69,63,57,65,64,65,51,64,78,78,55", "endOffsets": "356,561,747,2033,2123,2204,2294,2385,2462,2527,2630,2735,2800,2864,2927,2999,3117,3233,3348,3425,3514,3585,3664,3754,3845,3909,3977,4695,4753,4801,4862,4928,4995,5058,5128,5192,5250,5316,5381,5447,5499,5564,5643,5722,5778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "19,20,21,22,23,24,25,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "752,850,952,1049,1147,1252,1355,12948", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "845,947,1044,1142,1247,1350,1466,13044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,149", "endColumns": "93,95", "endOffsets": "144,240"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "13322,13416", "endColumns": "93,95", "endOffsets": "13411,13507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,250,318,395,468,557,642", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "120,181,245,313,390,463,552,637,715"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3982,4052,4113,4177,4245,4322,4395,4484,4569", "endColumns": "69,60,63,67,76,72,88,84,77", "endOffsets": "4047,4108,4172,4240,4317,4390,4479,4564,4642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,1010,1099,1171,1248,1326,1402,1483,1554", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,1005,1094,1166,1243,1321,1397,1478,1549,1670"}, "to": {"startLines": "26,27,28,29,30,82,83,141,142,143,144,145,146,147,148,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1471,1575,1665,1761,1864,5783,5860,12296,12388,12472,12556,12645,12717,12794,12872,13049,13130,13201", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,76,77,75,80,70,120", "endOffsets": "1570,1660,1756,1859,1944,5855,5945,12383,12467,12551,12640,12712,12789,12867,12943,13125,13196,13317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,292,408,526,623,718,830,963,1084,1232,1317,1416,1510,1606,1721,1845,1949,2094,2238,2380,2554,2685,2806,2933,3058,3153,3251,3377,3512,3612,3714,3827,3968,4117,4233,4335,4412,4506,4601,4720,4812,4898,5012,5095,5178,5278,5380,5477,5574,5662,5769,5869,5971,6104,6187,6298", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "169,287,403,521,618,713,825,958,1079,1227,1312,1411,1505,1601,1716,1840,1944,2089,2233,2375,2549,2680,2801,2928,3053,3148,3246,3372,3507,3607,3709,3822,3963,4112,4228,4330,4407,4501,4596,4715,4807,4893,5007,5090,5173,5273,5375,5472,5569,5657,5764,5864,5966,6099,6182,6293,6396"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5950,6069,6187,6303,6421,6518,6613,6725,6858,6979,7127,7212,7311,7405,7501,7616,7740,7844,7989,8133,8275,8449,8580,8701,8828,8953,9048,9146,9272,9407,9507,9609,9722,9863,10012,10128,10230,10307,10401,10496,10615,10707,10793,10907,10990,11073,11173,11275,11372,11469,11557,11664,11764,11866,11999,12082,12193", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "6064,6182,6298,6416,6513,6608,6720,6853,6974,7122,7207,7306,7400,7496,7611,7735,7839,7984,8128,8270,8444,8575,8696,8823,8948,9043,9141,9267,9402,9502,9604,9717,9858,10007,10123,10225,10302,10396,10491,10610,10702,10788,10902,10985,11068,11168,11270,11367,11464,11552,11659,11759,11861,11994,12077,12188,12291"}}]}]}