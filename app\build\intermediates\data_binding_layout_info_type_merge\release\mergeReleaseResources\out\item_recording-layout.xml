<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recording" modulePackage="com.mdmusfikurrahaman.callrecordingapp" filePath="app\src\main\res\layout\item_recording.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_recording_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="114" endOffset="51"/></Target><Target id="@+id/callTypeIcon" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="26" endOffset="55"/></Target><Target id="@+id/contactNameText" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="43" endOffset="39"/></Target><Target id="@+id/phoneNumberText" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="54" endOffset="44"/></Target><Target id="@+id/dateText" view="TextView"><Expressions/><location startLine="63" startOffset="16" endLine="70" endOffset="47"/></Target><Target id="@+id/durationText" view="TextView"><Expressions/><location startLine="72" startOffset="16" endLine="78" endOffset="39"/></Target><Target id="@+id/playButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="93" startOffset="12" endLine="99" endOffset="51"/></Target><Target id="@+id/moreButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="102" startOffset="12" endLine="108" endOffset="60"/></Target></Targets></Layout>