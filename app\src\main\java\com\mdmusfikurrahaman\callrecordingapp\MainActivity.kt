package com.mdmusfikurrahaman.callrecordingapp

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.widget.Button
import android.widget.TextView

/**
 * Main Activity for Call Recording App
 *
 * এই Activity হল আমাদের app এর main screen যেখানে:
 * - সব call recordings এর list দেখানো হয়
 * - Recording start/stop করার option আছে
 * - Permission handle করা হয়
 */
class MainActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var permissionStatusText: TextView
    private lateinit var requestPermissionsButton: Button
    private lateinit var startRecordingButton: Button

    // Required permissions for call recording
    private val requiredPermissions = arrayOf(
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.READ_CALL_LOG,
        Manifest.permission.READ_CONTACTS
    )

    // Permission launcher
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            Toast.makeText(this, "সব permission দেওয়া হয়েছে!", Toast.LENGTH_SHORT).show()
            setupRecordingFeatures()
        } else {
            Toast.makeText(this, "Permission প্রয়োজন app চালানোর জন্য", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main_simple)

        initViews()
        checkPermissions()
    }

    /**
     * Initialize all views
     */
    private fun initViews() {
        recyclerView = findViewById(R.id.recyclerView)
        permissionStatusText = findViewById(R.id.permissionStatusText)
        requestPermissionsButton = findViewById(R.id.requestPermissionsButton)
        startRecordingButton = findViewById(R.id.startRecordingButton)

        // Setup RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(this)

        // Setup button click listeners
        requestPermissionsButton.setOnClickListener {
            checkPermissions()
        }

        startRecordingButton.setOnClickListener {
            Toast.makeText(this, "Recording feature আসছে শীঘ্রই!", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Check and request permissions
     */
    private fun checkPermissions() {
        val missingPermissions = requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isNotEmpty()) {
            permissionStatusText.text = "Permission Status: Missing ${missingPermissions.size} permissions"
            permissionLauncher.launch(missingPermissions.toTypedArray())
        } else {
            permissionStatusText.text = "Permission Status: All permissions granted ✓"
            setupRecordingFeatures()
        }
    }

    /**
     * Setup recording features after permissions are granted
     */
    private fun setupRecordingFeatures() {
        // TODO: Initialize recording service
        // TODO: Setup call detection
        // TODO: Load existing recordings

        Toast.makeText(this, "App ready to record calls!", Toast.LENGTH_SHORT).show()
    }
}