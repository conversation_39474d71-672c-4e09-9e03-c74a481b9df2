<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2017 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->

<selector xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

  <!-- Pressed state -->
  <item
      android:state_enabled="true"
      android:state_pressed="true">
    <set>
      <objectAnimator
          android:duration="@integer/mtrl_btn_anim_duration_ms"
          android:propertyName="translationZ"
          android:valueTo="@dimen/mtrl_btn_pressed_z"
          android:valueType="floatType"/>
      <objectAnimator
          android:duration="0"
          android:propertyName="elevation"
          android:valueTo="@dimen/mtrl_btn_elevation"
          android:valueType="floatType"/>
    </set>
  </item>

  <!-- Hover state. This is triggered via mouse. -->
  <item
      android:state_enabled="true"
      android:state_hovered="true">
    <set>
      <objectAnimator
          android:duration="@integer/mtrl_btn_anim_duration_ms"
          android:propertyName="translationZ"
          android:valueTo="@dimen/mtrl_btn_hovered_z"
          android:valueType="floatType"/>
      <objectAnimator
          android:duration="0"
          android:propertyName="elevation"
          android:valueTo="@dimen/mtrl_btn_elevation"
          android:valueType="floatType"/>
    </set>
  </item>

  <!-- Focused state. This is triggered via keyboard. -->
  <item
      android:state_enabled="true"
      android:state_focused="true">
    <set>
      <objectAnimator
          android:duration="@integer/mtrl_btn_anim_duration_ms"
          android:propertyName="translationZ"
          android:valueTo="@dimen/mtrl_btn_focused_z"
          android:valueType="floatType"/>
      <objectAnimator
          android:duration="0"
          android:propertyName="elevation"
          android:valueTo="@dimen/mtrl_btn_elevation"
          android:valueType="floatType"/>
    </set>
  </item>

  <!-- Base state (enabled, not pressed) -->
  <item android:state_enabled="true">
    <set>
      <objectAnimator
          android:duration="@integer/mtrl_btn_anim_duration_ms"
          android:propertyName="translationZ"
          android:startDelay="@integer/mtrl_btn_anim_delay_ms"
          android:valueTo="@dimen/mtrl_btn_z"
          android:valueType="floatType"
          tools:ignore="UnusedAttribute"/>
      <objectAnimator
          android:duration="0"
          android:propertyName="elevation"
          android:valueTo="@dimen/mtrl_btn_elevation"
          android:valueType="floatType"/>
    </set>
  </item>

  <!-- Disabled state -->
  <item>
    <set>
      <objectAnimator
          android:duration="0"
          android:propertyName="translationZ"
          android:valueTo="@dimen/mtrl_btn_disabled_z"
          android:valueType="floatType"/>
      <objectAnimator
          android:duration="0"
          android:propertyName="elevation"
          android:valueTo="@dimen/mtrl_btn_disabled_elevation"
          android:valueType="floatType"/>
    </set>
  </item>

</selector>
