package com.mdmusfikurrahaman.callrecordingapp.data.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.UserPreferences
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.ThemeMode

/**
 * Data Access Object for UserPreferences entity
 * 
 * Provides methods to manage user preferences and settings
 */
@Dao
interface UserPreferencesDao {
    
    /**
     * Get user preferences as Flow for reactive updates
     */
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    fun getUserPreferences(): Flow<UserPreferences?>
    
    /**
     * Get user preferences synchronously
     */
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    suspend fun getUserPreferencesSync(): UserPreferences?
    
    /**
     * Insert or update user preferences
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdatePreferences(preferences: UserPreferences)
    
    /**
     * Update recording quality
     */
    @Query("UPDATE user_preferences SET recording_quality = :quality, updated_at = :updatedAt WHERE id = 1")
    suspend fun updateRecordingQuality(quality: RecordingQuality, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update auto record settings
     */
    @Query("""
        UPDATE user_preferences 
        SET auto_record_enabled = :enabled, 
            record_incoming_calls = :recordIncoming,
            record_outgoing_calls = :recordOutgoing,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun updateAutoRecordSettings(
        enabled: Boolean,
        recordIncoming: Boolean,
        recordOutgoing: Boolean,
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Update auto delete settings
     */
    @Query("""
        UPDATE user_preferences 
        SET auto_delete_enabled = :enabled,
            auto_delete_days = :days,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun updateAutoDeleteSettings(
        enabled: Boolean,
        days: Int,
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Update privacy settings
     */
    @Query("""
        UPDATE user_preferences 
        SET show_consent_dialog = :showConsent,
            legal_disclaimer_accepted = :disclaimerAccepted,
            privacy_policy_accepted = :privacyAccepted,
            consent_timestamp = :consentTimestamp,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun updatePrivacySettings(
        showConsent: Boolean,
        disclaimerAccepted: Boolean,
        privacyAccepted: Boolean,
        consentTimestamp: Long?,
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Update AI settings
     */
    @Query("""
        UPDATE user_preferences 
        SET ai_transcription_enabled = :enabled,
            openai_api_key = :apiKey,
            auto_transcribe = :autoTranscribe,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun updateAiSettings(
        enabled: Boolean,
        apiKey: String?,
        autoTranscribe: Boolean,
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Update theme mode
     */
    @Query("UPDATE user_preferences SET theme_mode = :themeMode, updated_at = :updatedAt WHERE id = 1")
    suspend fun updateThemeMode(themeMode: ThemeMode, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update notification settings
     */
    @Query("""
        UPDATE user_preferences 
        SET show_recording_notifications = :showNotifications,
            notification_sound_enabled = :soundEnabled,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun updateNotificationSettings(
        showNotifications: Boolean,
        soundEnabled: Boolean,
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Update UI preferences
     */
    @Query("""
        UPDATE user_preferences 
        SET show_recording_indicator = :showIndicator,
            vibrate_on_recording_start = :vibrateOnStart,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun updateUiPreferences(
        showIndicator: Boolean,
        vibrateOnStart: Boolean,
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Accept legal disclaimer
     */
    @Query("""
        UPDATE user_preferences 
        SET legal_disclaimer_accepted = 1,
            consent_timestamp = :timestamp,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun acceptLegalDisclaimer(
        timestamp: Long = System.currentTimeMillis(),
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * Accept privacy policy
     */
    @Query("""
        UPDATE user_preferences 
        SET privacy_policy_accepted = 1,
            updated_at = :updatedAt 
        WHERE id = 1
    """)
    suspend fun acceptPrivacyPolicy(updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Reset all preferences to default
     */
    @Query("DELETE FROM user_preferences WHERE id = 1")
    suspend fun resetPreferences()
}
