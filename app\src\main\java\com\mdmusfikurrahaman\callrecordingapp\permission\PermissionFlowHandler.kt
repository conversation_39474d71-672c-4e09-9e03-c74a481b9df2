package com.mdmusfikurrahaman.callrecordingapp.permission

import android.content.Context
import android.content.Intent
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Handler for managing permission request flows
 * 
 * This class provides a structured way to handle permission requests
 * with proper user guidance and fallback options.
 */
class PermissionFlowHandler @Inject constructor(
    private val permissionManager: PermissionManager
) {
    
    /**
     * Enum representing the current state of permission flow
     */
    enum class PermissionFlowState {
        IDLE,
        CHECKING_PERMISSIONS,
        REQUESTING_PERMISSIONS,
        SHOWING_RATIONALE,
        PERMISSIONS_GRANTED,
        PERMISSIONS_DENIED,
        PERMANENTLY_DENIED,
        ACCESSIBILITY_REQUIRED,
        OVERLAY_REQUIRED
    }
    
    /**
     * Data class representing permission flow result
     */
    data class PermissionFlowResult(
        val success: <PERSON><PERSON><PERSON>,
        val grantedPermissions: List<String> = emptyList(),
        val deniedPermissions: List<String> = emptyList(),
        val permanentlyDeniedPermissions: List<String> = emptyList(),
        val message: String? = null
    )
    
    private val _flowState = MutableStateFlow(PermissionFlowState.IDLE)
    val flowState: StateFlow<PermissionFlowState> = _flowState.asStateFlow()
    
    private val _flowResult = MutableStateFlow<PermissionFlowResult?>(null)
    val flowResult: StateFlow<PermissionFlowResult?> = _flowResult.asStateFlow()
    
    private var permissionLauncher: ActivityResultLauncher<Array<String>>? = null
    private var currentActivity: ComponentActivity? = null
    
    /**
     * Initialize the permission flow handler with an activity
     */
    fun initialize(activity: ComponentActivity) {
        currentActivity = activity
        
        permissionLauncher = permissionManager.createPermissionLauncher(activity) { permissions ->
            handlePermissionResults(permissions)
        }
    }
    
    /**
     * Start the permission request flow
     */
    fun startPermissionFlow(
        context: Context,
        requestOptionalPermissions: Boolean = false
    ) {
        currentActivity?.lifecycleScope?.launch {
            try {
                _flowState.value = PermissionFlowState.CHECKING_PERMISSIONS
                
                val statusReport = permissionManager.getPermissionStatusReport(context)
                
                when {
                    statusReport.hasAllRequired && !requestOptionalPermissions -> {
                        // All required permissions granted
                        handleFlowSuccess(context, "All required permissions granted")
                    }
                    
                    statusReport.hasAllRequired && requestOptionalPermissions -> {
                        // Request optional permissions
                        requestOptionalPermissions(context)
                    }
                    
                    else -> {
                        // Request missing required permissions
                        requestRequiredPermissions(context)
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error starting permission flow")
                handleFlowError("Error starting permission flow: ${e.message}")
            }
        }
    }
    
    /**
     * Request required permissions
     */
    private fun requestRequiredPermissions(context: Context) {
        val missingPermissions = permissionManager.getMissingRequiredPermissions(context)
        
        if (missingPermissions.isEmpty()) {
            handleFlowSuccess(context, "All required permissions already granted")
            return
        }
        
        _flowState.value = PermissionFlowState.REQUESTING_PERMISSIONS
        
        val permissionNames = missingPermissions.map { it.permission }
        permissionLauncher?.launch(permissionNames.toTypedArray())
            ?: handleFlowError("Permission launcher not initialized")
    }
    
    /**
     * Request optional permissions
     */
    private fun requestOptionalPermissions(context: Context) {
        val missingPermissions = permissionManager.getMissingOptionalPermissions(context)
        
        if (missingPermissions.isEmpty()) {
            handleFlowSuccess(context, "All permissions already granted")
            return
        }
        
        _flowState.value = PermissionFlowState.REQUESTING_PERMISSIONS
        
        val permissionNames = missingPermissions.map { it.permission }
        permissionLauncher?.launch(permissionNames.toTypedArray())
            ?: handleFlowError("Permission launcher not initialized")
    }
    
    /**
     * Handle permission request results
     */
    private fun handlePermissionResults(permissions: Map<String, Boolean>) {
        val context = currentActivity ?: return
        
        val grantedPermissions = permissions.filter { it.value }.keys.toList()
        val deniedPermissions = permissions.filter { !it.value }.keys.toList()
        
        // Check for permanently denied permissions
        val permanentlyDeniedPermissions = deniedPermissions.filter { permission ->
            !shouldShowRequestPermissionRationale(permission)
        }
        
        val temporarilyDeniedPermissions = deniedPermissions - permanentlyDeniedPermissions.toSet()
        
        when {
            deniedPermissions.isEmpty() -> {
                // All permissions granted
                handleFlowSuccess(context, "All permissions granted")
            }
            
            permanentlyDeniedPermissions.isNotEmpty() -> {
                // Some permissions permanently denied
                handlePermanentlyDenied(permanentlyDeniedPermissions)
            }
            
            temporarilyDeniedPermissions.isNotEmpty() -> {
                // Some permissions temporarily denied, show rationale
                handleTemporarilyDenied(temporarilyDeniedPermissions)
            }
        }
    }
    
    /**
     * Handle successfully granted permissions
     */
    private fun handleFlowSuccess(context: Context, message: String) {
        _flowState.value = PermissionFlowState.PERMISSIONS_GRANTED
        
        val statusReport = permissionManager.getPermissionStatusReport(context)
        
        _flowResult.value = PermissionFlowResult(
            success = true,
            grantedPermissions = statusReport.allPermissions
                .filter { it.value == PermissionManager.PermissionResult.GRANTED }
                .keys.toList(),
            message = message
        )
    }
    
    /**
     * Handle permanently denied permissions
     */
    private fun handlePermanentlyDenied(permanentlyDeniedPermissions: List<String>) {
        _flowState.value = PermissionFlowState.PERMANENTLY_DENIED
        
        val message = buildString {
            appendLine("Some permissions were permanently denied:")
            permanentlyDeniedPermissions.forEach { permission ->
                val info = permissionManager.getPermissionInfo(permission)
                appendLine("• ${info?.title ?: permission}: ${info?.description ?: ""}")
            }
            appendLine("\nPlease enable them in Settings to use all features.")
        }
        
        _flowResult.value = PermissionFlowResult(
            success = false,
            permanentlyDeniedPermissions = permanentlyDeniedPermissions,
            message = message
        )
    }
    
    /**
     * Handle temporarily denied permissions
     */
    private fun handleTemporarilyDenied(temporarilyDeniedPermissions: List<String>) {
        _flowState.value = PermissionFlowState.SHOWING_RATIONALE
        
        val message = buildString {
            appendLine("The following permissions are needed:")
            temporarilyDeniedPermissions.forEach { permission ->
                val info = permissionManager.getPermissionInfo(permission)
                appendLine("• ${info?.title ?: permission}: ${info?.description ?: ""}")
            }
        }
        
        _flowResult.value = PermissionFlowResult(
            success = false,
            deniedPermissions = temporarilyDeniedPermissions,
            message = message
        )
    }
    
    /**
     * Handle flow error
     */
    private fun handleFlowError(message: String) {
        _flowState.value = PermissionFlowState.PERMISSIONS_DENIED
        
        _flowResult.value = PermissionFlowResult(
            success = false,
            message = message
        )
    }
    
    /**
     * Retry permission request
     */
    fun retryPermissionRequest(context: Context) {
        startPermissionFlow(context)
    }
    
    /**
     * Open app settings
     */
    fun openAppSettings(context: Context) {
        try {
            val intent = permissionManager.createAppSettingsIntent(context)
            context.startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e, "Error opening app settings")
        }
    }
    
    /**
     * Open accessibility settings
     */
    fun openAccessibilitySettings(context: Context) {
        try {
            val intent = permissionManager.createAccessibilitySettingsIntent()
            context.startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e, "Error opening accessibility settings")
        }
    }
    
    /**
     * Reset flow state
     */
    fun resetFlow() {
        _flowState.value = PermissionFlowState.IDLE
        _flowResult.value = null
    }
    
    /**
     * Check if should show rationale for a permission
     */
    private fun shouldShowRequestPermissionRationale(permission: String): Boolean {
        return currentActivity?.shouldShowRequestPermissionRationale(permission) ?: false
    }
    
    /**
     * Get user-friendly message for current flow state
     */
    fun getStateMessage(): String {
        return when (_flowState.value) {
            PermissionFlowState.IDLE -> "Ready to request permissions"
            PermissionFlowState.CHECKING_PERMISSIONS -> "Checking permission status..."
            PermissionFlowState.REQUESTING_PERMISSIONS -> "Requesting permissions..."
            PermissionFlowState.SHOWING_RATIONALE -> "Please grant the required permissions"
            PermissionFlowState.PERMISSIONS_GRANTED -> "All permissions granted!"
            PermissionFlowState.PERMISSIONS_DENIED -> "Some permissions were denied"
            PermissionFlowState.PERMANENTLY_DENIED -> "Please enable permissions in Settings"
            PermissionFlowState.ACCESSIBILITY_REQUIRED -> "Please enable Accessibility Service"
            PermissionFlowState.OVERLAY_REQUIRED -> "Please enable overlay permission"
        }
    }
}
