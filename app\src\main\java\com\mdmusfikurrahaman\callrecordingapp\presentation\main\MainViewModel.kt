package com.mdmusfikurrahaman.callrecordingapp.presentation.main

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.permission.PermissionManager
import com.mdmusfikurrahaman.callrecordingapp.permission.PermissionFlowHandler

/**
 * ViewModel for the main screen
 * 
 * Manages the state of the recordings list, search functionality,
 * and permission handling.
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val repository: CallRecordingRepository,
    private val permissionManager: PermissionManager,
    private val permissionFlowHandler: PermissionFlowHandler
) : ViewModel() {
    
    private val _searchQuery = MutableStateFlow("")
    private val _showSearch = MutableStateFlow(false)
    private val _isLoading = MutableStateFlow(true)
    private val _hasRequiredPermissions = MutableStateFlow(false)
    private val _showPermissionDialog = MutableStateFlow(false)
    private val _errorMessage = MutableStateFlow<String?>(null)
    
    // Get all recordings from repository
    private val allRecordings = repository.getAllRecordings()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    // Filter recordings based on search query
    private val filteredRecordings = combine(
        allRecordings,
        _searchQuery
    ) { recordings, query ->
        if (query.isBlank()) {
            recordings
        } else {
            recordings.filter { recording ->
                recording.phoneNumber.contains(query, ignoreCase = true) ||
                recording.contactName?.contains(query, ignoreCase = true) == true
            }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    // UI State
    val uiState = combine(
        filteredRecordings,
        _searchQuery,
        _showSearch,
        _isLoading,
        _hasRequiredPermissions,
        _showPermissionDialog,
        _errorMessage
    ) { recordings, searchQuery, showSearch, isLoading, hasPermissions, showDialog, errorMessage ->
        MainUiState(
            recordings = recordings,
            searchQuery = searchQuery,
            showSearch = showSearch,
            isLoading = isLoading,
            hasRequiredPermissions = hasPermissions,
            showPermissionDialog = showDialog,
            errorMessage = errorMessage
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = MainUiState()
    )
    
    init {
        // Stop loading when recordings are loaded
        viewModelScope.launch {
            allRecordings.collect {
                _isLoading.value = false
            }
        }
        
        // Observe permission flow results
        viewModelScope.launch {
            permissionFlowHandler.flowResult.collect { result ->
                result?.let {
                    if (it.success) {
                        _hasRequiredPermissions.value = true
                        _showPermissionDialog.value = false
                    } else {
                        _errorMessage.value = it.message
                    }
                }
            }
        }
    }
    
    /**
     * Check if required permissions are granted
     */
    fun checkPermissions(context: Context) {
        viewModelScope.launch {
            try {
                val hasPermissions = permissionManager.hasAllRequiredPermissions(context)
                _hasRequiredPermissions.value = hasPermissions
                
                if (!hasPermissions) {
                    _showPermissionDialog.value = true
                }
            } catch (e: Exception) {
                Timber.e(e, "Error checking permissions")
                _errorMessage.value = "Error checking permissions"
            }
        }
    }
    
    /**
     * Request required permissions
     */
    fun requestPermissions(context: Context) {
        permissionFlowHandler.startPermissionFlow(context)
    }
    
    /**
     * Open app settings
     */
    fun openAppSettings(context: Context) {
        permissionFlowHandler.openAppSettings(context)
    }
    
    /**
     * Dismiss permission dialog
     */
    fun dismissPermissionDialog() {
        _showPermissionDialog.value = false
    }
    
    /**
     * Toggle search visibility
     */
    fun toggleSearch() {
        _showSearch.value = !_showSearch.value
        if (!_showSearch.value) {
            _searchQuery.value = ""
        }
    }
    
    /**
     * Update search query
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * Perform search (currently just updates the query)
     */
    fun performSearch(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * Play a recording
     */
    fun playRecording(recording: CallRecording) {
        viewModelScope.launch {
            try {
                // TODO: Implement audio playback
                Timber.d("Playing recording: ${recording.filePath}")
            } catch (e: Exception) {
                Timber.e(e, "Error playing recording")
                _errorMessage.value = "Error playing recording"
            }
        }
    }
    
    /**
     * Share a recording
     */
    fun shareRecording(recording: CallRecording) {
        viewModelScope.launch {
            try {
                // TODO: Implement sharing functionality
                Timber.d("Sharing recording: ${recording.filePath}")
            } catch (e: Exception) {
                Timber.e(e, "Error sharing recording")
                _errorMessage.value = "Error sharing recording"
            }
        }
    }
    
    /**
     * Delete a recording
     */
    fun deleteRecording(recording: CallRecording) {
        viewModelScope.launch {
            try {
                repository.deleteRecording(recording)
                Timber.d("Deleted recording: ${recording.filePath}")
            } catch (e: Exception) {
                Timber.e(e, "Error deleting recording")
                _errorMessage.value = "Error deleting recording"
            }
        }
    }
    
    /**
     * Toggle favorite status of a recording
     */
    fun toggleFavorite(recording: CallRecording) {
        viewModelScope.launch {
            try {
                repository.updateFavoriteStatus(recording.id, !recording.isFavorite)
                Timber.d("Toggled favorite for recording: ${recording.id}")
            } catch (e: Exception) {
                Timber.e(e, "Error toggling favorite")
                _errorMessage.value = "Error updating favorite status"
            }
        }
    }
    
    /**
     * Start manual recording
     */
    fun startManualRecording() {
        viewModelScope.launch {
            try {
                // TODO: Implement manual recording start
                Timber.d("Starting manual recording")
            } catch (e: Exception) {
                Timber.e(e, "Error starting manual recording")
                _errorMessage.value = "Error starting recording"
            }
        }
    }
    
    /**
     * Navigate to settings
     */
    fun navigateToSettings() {
        // TODO: Implement navigation to settings
        Timber.d("Navigating to settings")
    }
    
    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}

/**
 * UI state for the main screen
 */
data class MainUiState(
    val recordings: List<CallRecording> = emptyList(),
    val searchQuery: String = "",
    val showSearch: Boolean = false,
    val isLoading: Boolean = true,
    val hasRequiredPermissions: Boolean = false,
    val showPermissionDialog: Boolean = false,
    val errorMessage: String? = null
)
