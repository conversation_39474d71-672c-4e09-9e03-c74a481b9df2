package com.mdmusfikurrahaman.callrecordingapp.presentation.detail

import android.content.Context
import android.content.Intent
import androidx.core.content.FileProvider
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.TranscriptWithSegments
import com.mdmusfikurrahaman.callrecordingapp.audio.AudioPlayerManager

/**
 * ViewModel for the recording detail screen
 */
@HiltViewModel
class RecordingDetailViewModel @Inject constructor(
    private val repository: CallRecordingRepository,
    private val audioPlayerManager: AudioPlayerManager
) : ViewModel() {
    
    private val _recording = MutableStateFlow<CallRecording?>(null)
    private val _transcript = MutableStateFlow<TranscriptWithSegments?>(null)
    private val _isLoading = MutableStateFlow(false)
    private val _isPlaying = MutableStateFlow(false)
    private val _currentPosition = MutableStateFlow(0L)
    private val _playbackSpeed = MutableStateFlow(1.0f)
    private val _showDeleteConfirmation = MutableStateFlow(false)
    private val _errorMessage = MutableStateFlow<String?>(null)
    
    val uiState = combine(
        _recording,
        _transcript,
        _isLoading,
        _isPlaying,
        _currentPosition,
        _playbackSpeed,
        _showDeleteConfirmation,
        _errorMessage
    ) { recording, transcript, isLoading, isPlaying, currentPosition, playbackSpeed, showDeleteConfirmation, errorMessage ->
        RecordingDetailUiState(
            recording = recording,
            transcript = transcript,
            isLoading = isLoading,
            isPlaying = isPlaying,
            currentPosition = currentPosition,
            playbackSpeed = playbackSpeed,
            showDeleteConfirmation = showDeleteConfirmation,
            errorMessage = errorMessage
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = RecordingDetailUiState()
    )
    
    init {
        // Observe audio player state
        viewModelScope.launch {
            audioPlayerManager.isPlaying.collect { isPlaying ->
                _isPlaying.value = isPlaying
            }
        }
        
        viewModelScope.launch {
            audioPlayerManager.currentPosition.collect { position ->
                _currentPosition.value = position
            }
        }
        
        viewModelScope.launch {
            audioPlayerManager.playbackSpeed.collect { speed ->
                _playbackSpeed.value = speed
            }
        }
    }
    
    /**
     * Load recording details
     */
    fun loadRecording(recordingId: Long) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val recording = repository.getRecordingById(recordingId)
                if (recording != null) {
                    _recording.value = recording
                    
                    // Load transcript if available
                    if (recording.hasTranscript) {
                        val transcript = repository.getTranscriptWithSegments(recordingId)
                        _transcript.value = transcript
                    }
                    
                    // Prepare audio player
                    audioPlayerManager.prepareAudio(recording.filePath)
                } else {
                    _errorMessage.value = "Recording not found"
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error loading recording")
                _errorMessage.value = "Error loading recording"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Toggle playback (play/pause)
     */
    fun togglePlayback() {
        viewModelScope.launch {
            try {
                if (_isPlaying.value) {
                    audioPlayerManager.pause()
                } else {
                    audioPlayerManager.play()
                }
            } catch (e: Exception) {
                Timber.e(e, "Error toggling playback")
                _errorMessage.value = "Error controlling playback"
            }
        }
    }
    
    /**
     * Seek to position (in milliseconds)
     */
    fun seekTo(positionMs: Float) {
        viewModelScope.launch {
            try {
                audioPlayerManager.seekTo(positionMs.toLong())
            } catch (e: Exception) {
                Timber.e(e, "Error seeking")
                _errorMessage.value = "Error seeking"
            }
        }
    }
    
    /**
     * Change playback speed
     */
    fun changePlaybackSpeed(speed: Float) {
        viewModelScope.launch {
            try {
                audioPlayerManager.setPlaybackSpeed(speed)
            } catch (e: Exception) {
                Timber.e(e, "Error changing playback speed")
                _errorMessage.value = "Error changing playback speed"
            }
        }
    }
    
    /**
     * Toggle favorite status
     */
    fun toggleFavorite() {
        viewModelScope.launch {
            try {
                val recording = _recording.value ?: return@launch
                repository.updateFavoriteStatus(recording.id, !recording.isFavorite)
                _recording.value = recording.copy(isFavorite = !recording.isFavorite)
            } catch (e: Exception) {
                Timber.e(e, "Error toggling favorite")
                _errorMessage.value = "Error updating favorite status"
            }
        }
    }
    
    /**
     * Share recording
     */
    fun shareRecording(context: Context) {
        viewModelScope.launch {
            try {
                val recording = _recording.value ?: return@launch
                val file = File(recording.filePath)
                
                if (!file.exists()) {
                    _errorMessage.value = "Recording file not found"
                    return@launch
                }
                
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )
                
                val shareIntent = Intent(Intent.ACTION_SEND).apply {
                    type = "audio/*"
                    putExtra(Intent.EXTRA_STREAM, uri)
                    putExtra(Intent.EXTRA_SUBJECT, "Call Recording")
                    putExtra(
                        Intent.EXTRA_TEXT,
                        "Call recording with ${recording.contactName ?: recording.phoneNumber}"
                    )
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                
                val chooserIntent = Intent.createChooser(shareIntent, "Share Recording")
                chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(chooserIntent)
                
            } catch (e: Exception) {
                Timber.e(e, "Error sharing recording")
                _errorMessage.value = "Error sharing recording"
            }
        }
    }
    
    /**
     * Show delete confirmation dialog
     */
    fun showDeleteConfirmation() {
        _showDeleteConfirmation.value = true
    }
    
    /**
     * Hide delete confirmation dialog
     */
    fun hideDeleteConfirmation() {
        _showDeleteConfirmation.value = false
    }
    
    /**
     * Delete recording
     */
    fun deleteRecording() {
        viewModelScope.launch {
            try {
                val recording = _recording.value ?: return@launch
                
                // Stop playback if active
                audioPlayerManager.stop()
                
                // Delete from repository (this will also delete the file)
                repository.deleteRecording(recording)
                
                _showDeleteConfirmation.value = false
                
            } catch (e: Exception) {
                Timber.e(e, "Error deleting recording")
                _errorMessage.value = "Error deleting recording"
            }
        }
    }
    
    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    override fun onCleared() {
        super.onCleared()
        // Clean up audio player
        viewModelScope.launch {
            audioPlayerManager.release()
        }
    }
}

/**
 * UI state for the recording detail screen
 */
data class RecordingDetailUiState(
    val recording: CallRecording? = null,
    val transcript: TranscriptWithSegments? = null,
    val isLoading: Boolean = false,
    val isPlaying: Boolean = false,
    val currentPosition: Long = 0L,
    val playbackSpeed: Float = 1.0f,
    val showDeleteConfirmation: Boolean = false,
    val errorMessage: String? = null
)
