{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeReleaseResources-59:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81e0b3af703173a357d5c452b0d31980\\transformed\\exoplayer-core-2.19.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7583,7660,7722,7786,7857,7934,8008,8092,8174", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "7655,7717,7781,7852,7929,8003,8087,8169,8249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7473b31c75d2d339c0da8a1a3f9146d6\\transformed\\material-1.11.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1147,1246,1322,1387,1477,1541,1607,1661,1730,1790,1844,1961,2021,2083,2137,2209,2339,2426,2518,2657,2726,2804,2935,3023,3103,3157,3208,3274,3346,3423,3506,3588,3660,3737,3810,3881,3986,4074,4146,4238,4334,4408,4482,4578,4630,4712,4779,4866,4953,5015,5079,5142,5210,5316,5423,5521,5638,5696,5751", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "373,448,523,601,693,776,868,996,1077,1142,1241,1317,1382,1472,1536,1602,1656,1725,1785,1839,1956,2016,2078,2132,2204,2334,2421,2513,2652,2721,2799,2930,3018,3098,3152,3203,3269,3341,3418,3501,3583,3655,3732,3805,3876,3981,4069,4141,4233,4329,4403,4477,4573,4625,4707,4774,4861,4948,5010,5074,5137,5205,5311,5418,5516,5633,5691,5746,5825"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,71,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1033,4023,4098,4173,4251,4343,5163,5255,5383,5464,9402,9501,9577,9642,9732,9796,9862,9916,9985,10045,10099,10216,10276,10338,10392,10464,10594,10681,10773,10912,10981,11059,11190,11278,11358,11412,11463,11529,11601,11678,11761,11843,11915,11992,12065,12136,12241,12329,12401,12493,12589,12663,12737,12833,12885,12967,13034,13121,13208,13270,13334,13397,13465,13571,13678,13776,13893,13951,14239", "endLines": "28,56,57,58,59,60,68,69,70,71,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183", "endColumns": "12,74,74,77,91,82,91,127,80,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,91,138,68,77,130,87,79,53,50,65,71,76,82,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78", "endOffsets": "1306,4093,4168,4246,4338,4421,5250,5378,5459,5524,9496,9572,9637,9727,9791,9857,9911,9980,10040,10094,10211,10271,10333,10387,10459,10589,10676,10768,10907,10976,11054,11185,11273,11353,11407,11458,11524,11596,11673,11756,11838,11910,11987,12060,12131,12236,12324,12396,12488,12584,12658,12732,12828,12880,12962,13029,13116,13203,13265,13329,13392,13460,13566,13673,13771,13888,13946,14001,14313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fab9a8fce592b1bb05a0549bf6b2f96e\\transformed\\exoplayer-ui-2.19.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3748,3814,3866,3930,4008,4086", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3743,3809,3861,3925,4003,4081,4135"}, "to": {"startLines": "2,11,17,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,719,5529,5610,5690,5772,5875,5974,6053,6118,6209,6303,6373,6439,6504,6581,6703,6820,6941,7015,7097,7170,7252,7352,7451,7518,8254,8307,8365,8413,8474,8546,8620,8683,8756,8821,8881,8946,9010,9076,9128,9192,9270,9348", "endLines": "10,16,22,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "383,714,1028,5605,5685,5767,5870,5969,6048,6113,6204,6298,6368,6434,6499,6576,6698,6815,6936,7010,7092,7165,7247,7347,7446,7513,7578,8302,8360,8408,8469,8541,8615,8678,8751,8816,8876,8941,9005,9071,9123,9187,9265,9343,9397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\663fb4dbb7e440a36499eafaa6fbda5d\\transformed\\core-1.16.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "61,62,63,64,65,66,67,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4426,4522,4624,4725,4823,4933,5041,14401", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "4517,4619,4720,4818,4928,5036,5158,14497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5e1e4d034409f719670a3d1d52262c91\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1311,1418,1519,1630,1716,1824,1942,2021,2098,2189,2282,2380,2474,2574,2667,2762,2860,2951,3042,3126,3231,3339,3438,3544,3656,3759,3925,14318", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1413,1514,1625,1711,1819,1937,2016,2093,2184,2277,2375,2469,2569,2662,2757,2855,2946,3037,3121,3226,3334,3433,3539,3651,3754,3920,4018,14396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\125614bd4c72f93cfeffaa0b5947d611\\transformed\\navigation-ui-2.7.6\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,126", "endOffsets": "156,283"}, "to": {"startLines": "181,182", "startColumns": "4,4", "startOffsets": "14006,14112", "endColumns": "105,126", "endOffsets": "14107,14234"}}]}]}