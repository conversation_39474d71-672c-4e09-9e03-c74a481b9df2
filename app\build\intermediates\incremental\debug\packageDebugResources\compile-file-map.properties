#Sun Jul 20 15:14:30 BDT 2025
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_call_received_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_call_received_24.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_error_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_error_24.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_launcher_background.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_mic_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_mic_24.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_more_vert_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_more_vert_24.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_play_arrow_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play_arrow_24.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_search_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search_24.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/drawable/ic_stop_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_stop_24.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.mdmusfikurrahaman.callrecordingapp-main-5\:/xml/accessibility_service_config.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\accessibility_service_config.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/xml/backup_rules.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/xml/data_extraction_rules.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.mdmusfikurrahaman.callrecordingapp-main-5\:/xml/file_paths.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\file_paths.xml
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-2\:/layout/activity_main.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-2\:/layout/item_recording.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_recording.xml
