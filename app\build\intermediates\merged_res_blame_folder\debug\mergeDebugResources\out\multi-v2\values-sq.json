{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-59:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5e5330ea37f1d0a4ed46f4a86900feb4\\transformed\\material-1.11.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1177,1247,1306,1404,1466,1530,1589,1661,1724,1778,1895,1952,2014,2068,2140,2275,2358,2436,2577,2661,2743,2891,2981,3059,3112,3171,3237,3308,3387,3475,3558,3634,3712,3784,3857,3961,4050,4122,4216,4315,4389,4461,4562,4612,4697,4763,4853,4942,5004,5068,5131,5198,5314,5427,5536,5641,5698,5761", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,147,89,77,52,58,65,70,78,87,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1172,1242,1301,1399,1461,1525,1584,1656,1719,1773,1890,1947,2009,2063,2135,2270,2353,2431,2572,2656,2738,2886,2976,3054,3107,3166,3232,3303,3382,3470,3553,3629,3707,3779,3852,3956,4045,4117,4211,4310,4384,4456,4557,4607,4692,4758,4848,4937,4999,5063,5126,5193,5309,5422,5531,5636,5693,5756,5839"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,3629,3708,3786,3872,3972,4801,4902,5028,5111,9067,9167,9237,9296,9394,9456,9520,9579,9651,9714,9768,9885,9942,10004,10058,10130,10265,10348,10426,10567,10651,10733,10881,10971,11049,11102,11161,11227,11298,11377,11465,11548,11624,11702,11774,11847,11951,12040,12112,12206,12305,12379,12451,12552,12602,12687,12753,12843,12932,12994,13058,13121,13188,13304,13417,13526,13631,13688,13976", "endLines": "22,50,51,52,53,54,62,63,64,65,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,177", "endColumns": "12,78,77,85,99,91,100,125,82,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,77,140,83,81,147,89,77,52,58,65,70,78,87,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82", "endOffsets": "928,3703,3781,3867,3967,4059,4897,5023,5106,5171,9162,9232,9291,9389,9451,9515,9574,9646,9709,9763,9880,9937,9999,10053,10125,10260,10343,10421,10562,10646,10728,10876,10966,11044,11097,11156,11222,11293,11372,11460,11543,11619,11697,11769,11842,11946,12035,12107,12201,12300,12374,12446,12547,12597,12682,12748,12838,12927,12989,13053,13116,13183,13299,13412,13521,13626,13683,13746,14054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16b8dac02a484144e9b877600396f6d3\\transformed\\exoplayer-ui-2.19.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,485,671,752,833,919,1023,1115,1188,1251,1341,1431,1496,1559,1626,1694,1843,1992,2135,2202,2284,2356,2429,2528,2627,2691,2761,2814,2872,2920,2981,3046,3112,3174,3242,3306,3365,3431,3496,3562,3614,3679,3757,3835", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "281,480,666,747,828,914,1018,1110,1183,1246,1336,1426,1491,1554,1621,1689,1838,1987,2130,2197,2279,2351,2424,2523,2622,2686,2756,2809,2867,2915,2976,3041,3107,3169,3237,3301,3360,3426,3491,3557,3609,3674,3752,3830,3887"}, "to": {"startLines": "2,11,15,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,580,5176,5257,5338,5424,5528,5620,5693,5756,5846,5936,6001,6064,6131,6199,6348,6497,6640,6707,6789,6861,6934,7033,7132,7196,7936,7989,8047,8095,8156,8221,8287,8349,8417,8481,8540,8606,8671,8737,8789,8854,8932,9010", "endLines": "10,14,18,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "17,12,12,80,80,85,103,91,72,62,89,89,64,62,66,67,148,148,142,66,81,71,72,98,98,63,69,52,57,47,60,64,65,61,67,63,58,65,64,65,51,64,77,77,56", "endOffsets": "376,575,761,5252,5333,5419,5523,5615,5688,5751,5841,5931,5996,6059,6126,6194,6343,6492,6635,6702,6784,6856,6929,7028,7127,7191,7261,7984,8042,8090,8151,8216,8282,8344,8412,8476,8535,8601,8666,8732,8784,8849,8927,9005,9062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46864e7f8e3972dd5b88a3c5321cb854\\transformed\\appcompat-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "933,1047,1147,1259,1345,1451,1574,1656,1734,1825,1918,2013,2107,2208,2301,2396,2493,2584,2677,2758,2864,2968,3066,3172,3276,3378,3532,14059", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "1042,1142,1254,1340,1446,1569,1651,1729,1820,1913,2008,2102,2203,2296,2391,2488,2579,2672,2753,2859,2963,3061,3167,3271,3373,3527,3624,14136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3f319f1baa5473100673c9c69aa9ac79\\transformed\\core-1.16.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "55,56,57,58,59,60,61,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4064,4163,4265,4363,4460,4568,4679,14141", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "4158,4260,4358,4455,4563,4674,4796,14237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f743c2e5f2a61bc0a20ac96fdd74bd6f\\transformed\\navigation-ui-2.7.6\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,118", "endOffsets": "156,275"}, "to": {"startLines": "175,176", "startColumns": "4,4", "startOffsets": "13751,13857", "endColumns": "105,118", "endOffsets": "13852,13971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\944d23d0a2a0a4de0e44ec3c7bd88e96\\transformed\\exoplayer-core-2.19.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,254,320,398,477,569,655", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "121,182,249,315,393,472,564,650,720"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7266,7337,7398,7465,7531,7609,7688,7780,7866", "endColumns": "70,60,66,65,77,78,91,85,69", "endOffsets": "7332,7393,7460,7526,7604,7683,7775,7861,7931"}}]}]}