package com.mdmusfikurrahaman.callrecordingapp.presentation.settings.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.mdmusfikurrahaman.callrecordingapp.permission.PermissionManager

/**
 * Section showing permission status with actions
 */
@Composable
fun PermissionStatusSection(
    permissionStatus: PermissionManager.PermissionStatusReport?,
    onRequestPermissions: () -> Unit,
    onOpenSettings: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        if (permissionStatus != null) {
            // Permission summary
            PermissionSummaryCard(
                permissionStatus = permissionStatus,
                onRequestPermissions = onRequestPermissions,
                onOpenSettings = onOpenSettings
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Individual permission status
            LazyColumn(
                modifier = Modifier.heightIn(max = 300.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(permissionStatus.allPermissions.toList()) { (permission, result) ->
                    PermissionStatusItem(
                        permission = permission,
                        result = result
                    )
                }
            }
            
            // Special permissions
            if (!permissionStatus.accessibilityServiceEnabled) {
                Spacer(modifier = Modifier.height(8.dp))
                SpecialPermissionItem(
                    title = "Accessibility Service",
                    description = "Required for fallback recording method",
                    isGranted = false,
                    onClick = onOpenSettings
                )
            }
            
        } else {
            // Loading state
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(100.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }
    }
}

@Composable
private fun PermissionSummaryCard(
    permissionStatus: PermissionManager.PermissionStatusReport,
    onRequestPermissions: () -> Unit,
    onOpenSettings: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (permissionStatus.hasAllRequired) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.errorContainer
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (permissionStatus.hasAllRequired) {
                        Icons.Default.CheckCircle
                    } else {
                        Icons.Default.Warning
                    },
                    contentDescription = null,
                    tint = if (permissionStatus.hasAllRequired) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    }
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = if (permissionStatus.hasAllRequired) {
                        "All Required Permissions Granted"
                    } else {
                        "Missing Required Permissions"
                    },
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (permissionStatus.hasAllRequired) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "${permissionStatus.grantedRequiredCount}/${permissionStatus.requiredPermissionsCount} required permissions granted",
                style = MaterialTheme.typography.bodyMedium,
                color = if (permissionStatus.hasAllRequired) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onErrorContainer
                }
            )
            
            if (!permissionStatus.hasAllRequired) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = onRequestPermissions,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Grant Permissions")
                    }
                    
                    OutlinedButton(
                        onClick = onOpenSettings,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Open Settings")
                    }
                }
            }
        }
    }
}

@Composable
private fun PermissionStatusItem(
    permission: String,
    result: PermissionManager.PermissionResult
) {
    val permissionInfo = PermissionManager.getPermissionInfo(permission)
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = when (result) {
                PermissionManager.PermissionResult.GRANTED -> Icons.Default.CheckCircle
                PermissionManager.PermissionResult.DENIED -> Icons.Default.Cancel
                PermissionManager.PermissionResult.PERMANENTLY_DENIED -> Icons.Default.Block
                PermissionManager.PermissionResult.NOT_AVAILABLE -> Icons.Default.NotInterested
            },
            contentDescription = null,
            tint = when (result) {
                PermissionManager.PermissionResult.GRANTED -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.error
            },
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = permissionInfo?.title ?: permission,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = when (result) {
                    PermissionManager.PermissionResult.GRANTED -> "Granted"
                    PermissionManager.PermissionResult.DENIED -> "Denied"
                    PermissionManager.PermissionResult.PERMANENTLY_DENIED -> "Permanently Denied"
                    PermissionManager.PermissionResult.NOT_AVAILABLE -> "Not Available"
                },
                style = MaterialTheme.typography.bodySmall,
                color = when (result) {
                    PermissionManager.PermissionResult.GRANTED -> MaterialTheme.colorScheme.primary
                    else -> MaterialTheme.colorScheme.error
                }
            )
        }
    }
}

@Composable
private fun SpecialPermissionItem(
    title: String,
    description: String,
    isGranted: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isGranted) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.errorContainer
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = if (isGranted) Icons.Default.CheckCircle else Icons.Default.Warning,
                contentDescription = null,
                tint = if (isGranted) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onErrorContainer
                }
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = if (isGranted) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    }
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = if (isGranted) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onErrorContainer
                    }
                )
            }
            
            if (!isGranted) {
                TextButton(onClick = onClick) {
                    Text(
                        "Enable",
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}
