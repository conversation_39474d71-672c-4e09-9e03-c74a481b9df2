package com.mdmusfikurrahaman.callrecordingapp.error

import android.content.Context
import android.os.Build
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Centralized error handling system
 * 
 * This class provides:
 * - Consistent error handling across the app
 * - User-friendly error messages
 * - Error logging and reporting
 * - Recovery suggestions
 * - Compatibility issue detection
 */
@Singleton
class ErrorHandler @Inject constructor(
    private val context: Context
) {
    
    private val _currentError = MutableStateFlow<AppError?>(null)
    val currentError: StateFlow<AppError?> = _currentError.asStateFlow()
    
    /**
     * Handle an error with context and recovery options
     */
    fun handleError(
        throwable: Throwable,
        context: ErrorContext,
        userMessage: String? = null,
        recoveryActions: List<RecoveryAction> = emptyList()
    ) {
        val appError = createAppError(throwable, context, userMessage, recoveryActions)
        
        // Log the error
        logError(appError)
        
        // Update current error state
        _currentError.value = appError
        
        // Report to crash analytics if configured
        reportError(appError)
    }
    
    /**
     * Handle specific recording errors
     */
    fun handleRecordingError(
        throwable: Throwable,
        recordingMethod: String,
        phoneNumber: String? = null
    ) {
        val context = ErrorContext.RECORDING
        val userMessage = getRecordingErrorMessage(throwable, recordingMethod)
        val recoveryActions = getRecordingRecoveryActions(throwable, recordingMethod)
        
        handleError(throwable, context, userMessage, recoveryActions)
    }
    
    /**
     * Handle permission errors
     */
    fun handlePermissionError(
        missingPermissions: List<String>,
        isRequired: Boolean = true
    ) {
        val error = PermissionError(missingPermissions, isRequired)
        val context = ErrorContext.PERMISSIONS
        val userMessage = getPermissionErrorMessage(missingPermissions, isRequired)
        val recoveryActions = getPermissionRecoveryActions(missingPermissions)
        
        handleError(error, context, userMessage, recoveryActions)
    }
    
    /**
     * Handle storage errors
     */
    fun handleStorageError(
        throwable: Throwable,
        availableSpaceMB: Long,
        requiredSpaceMB: Long
    ) {
        val context = ErrorContext.STORAGE
        val userMessage = getStorageErrorMessage(throwable, availableSpaceMB, requiredSpaceMB)
        val recoveryActions = getStorageRecoveryActions(availableSpaceMB, requiredSpaceMB)
        
        handleError(throwable, context, userMessage, recoveryActions)
    }
    
    /**
     * Handle compatibility errors
     */
    fun handleCompatibilityError(
        feature: String,
        minSdkVersion: Int,
        currentSdkVersion: Int = Build.VERSION.SDK_INT
    ) {
        val error = CompatibilityError(feature, minSdkVersion, currentSdkVersion)
        val context = ErrorContext.COMPATIBILITY
        val userMessage = getCompatibilityErrorMessage(feature, minSdkVersion, currentSdkVersion)
        val recoveryActions = getCompatibilityRecoveryActions(feature)
        
        handleError(error, context, userMessage, recoveryActions)
    }
    
    /**
     * Clear current error
     */
    fun clearError() {
        _currentError.value = null
    }
    
    /**
     * Create AppError from throwable and context
     */
    private fun createAppError(
        throwable: Throwable,
        context: ErrorContext,
        userMessage: String?,
        recoveryActions: List<RecoveryAction>
    ): AppError {
        return AppError(
            throwable = throwable,
            context = context,
            userMessage = userMessage ?: getDefaultErrorMessage(throwable, context),
            technicalMessage = throwable.message ?: throwable.javaClass.simpleName,
            recoveryActions = recoveryActions,
            timestamp = System.currentTimeMillis(),
            deviceInfo = getDeviceInfo()
        )
    }
    
    /**
     * Get user-friendly error message for recording errors
     */
    private fun getRecordingErrorMessage(throwable: Throwable, recordingMethod: String): String {
        return when {
            throwable is SecurityException -> 
                "Recording permission denied. Please grant microphone permission and try again."
            
            throwable.message?.contains("AudioRecord") == true ->
                "Unable to access microphone for recording. This may be due to device restrictions or another app using the microphone."
            
            throwable.message?.contains("MediaProjection") == true ->
                "Screen recording permission denied or not supported on this device."
            
            recordingMethod == "MediaProjection" && Build.VERSION.SDK_INT < Build.VERSION_CODES.Q ->
                "MediaProjection recording requires Android 10 or higher. Please use a different recording method."
            
            recordingMethod == "AccessibilityService" ->
                "Accessibility service is not enabled. Please enable it in Settings > Accessibility."
            
            else -> "Recording failed using $recordingMethod method. Please try a different recording method or check your device settings."
        }
    }
    
    /**
     * Get recovery actions for recording errors
     */
    private fun getRecordingRecoveryActions(throwable: Throwable, recordingMethod: String): List<RecoveryAction> {
        val actions = mutableListOf<RecoveryAction>()
        
        when {
            throwable is SecurityException -> {
                actions.add(RecoveryAction.REQUEST_PERMISSIONS)
                actions.add(RecoveryAction.OPEN_SETTINGS)
            }
            
            recordingMethod == "MediaProjection" -> {
                actions.add(RecoveryAction.TRY_DIFFERENT_METHOD)
                actions.add(RecoveryAction.CHECK_DEVICE_COMPATIBILITY)
            }
            
            recordingMethod == "AccessibilityService" -> {
                actions.add(RecoveryAction.ENABLE_ACCESSIBILITY_SERVICE)
                actions.add(RecoveryAction.TRY_DIFFERENT_METHOD)
            }
            
            else -> {
                actions.add(RecoveryAction.TRY_DIFFERENT_METHOD)
                actions.add(RecoveryAction.RESTART_APP)
                actions.add(RecoveryAction.CHECK_DEVICE_COMPATIBILITY)
            }
        }
        
        return actions
    }
    
    /**
     * Get user-friendly message for permission errors
     */
    private fun getPermissionErrorMessage(permissions: List<String>, isRequired: Boolean): String {
        val permissionNames = permissions.map { getPermissionDisplayName(it) }
        
        return if (isRequired) {
            "Required permissions are missing: ${permissionNames.joinToString(", ")}. " +
            "Please grant these permissions to use the app."
        } else {
            "Optional permissions are missing: ${permissionNames.joinToString(", ")}. " +
            "Some features may not work properly."
        }
    }
    
    /**
     * Get recovery actions for permission errors
     */
    private fun getPermissionRecoveryActions(permissions: List<String>): List<RecoveryAction> {
        return listOf(
            RecoveryAction.REQUEST_PERMISSIONS,
            RecoveryAction.OPEN_SETTINGS,
            RecoveryAction.LEARN_MORE
        )
    }
    
    /**
     * Get user-friendly message for storage errors
     */
    private fun getStorageErrorMessage(
        throwable: Throwable,
        availableSpaceMB: Long,
        requiredSpaceMB: Long
    ): String {
        return when {
            availableSpaceMB < requiredSpaceMB ->
                "Insufficient storage space. Available: ${availableSpaceMB}MB, Required: ${requiredSpaceMB}MB. " +
                "Please free up space or change storage settings."
            
            throwable.message?.contains("permission", ignoreCase = true) == true ->
                "Storage permission denied. Please grant storage permission to save recordings."
            
            else -> "Storage error occurred: ${throwable.message}. Please check your storage settings."
        }
    }
    
    /**
     * Get recovery actions for storage errors
     */
    private fun getStorageRecoveryActions(availableSpaceMB: Long, requiredSpaceMB: Long): List<RecoveryAction> {
        val actions = mutableListOf<RecoveryAction>()
        
        if (availableSpaceMB < requiredSpaceMB) {
            actions.add(RecoveryAction.FREE_UP_SPACE)
            actions.add(RecoveryAction.CHANGE_STORAGE_SETTINGS)
        }
        
        actions.add(RecoveryAction.REQUEST_PERMISSIONS)
        actions.add(RecoveryAction.OPEN_SETTINGS)
        
        return actions
    }
    
    /**
     * Get user-friendly message for compatibility errors
     */
    private fun getCompatibilityErrorMessage(
        feature: String,
        minSdkVersion: Int,
        currentSdkVersion: Int
    ): String {
        val minVersionName = getAndroidVersionName(minSdkVersion)
        val currentVersionName = getAndroidVersionName(currentSdkVersion)
        
        return "$feature requires Android $minVersionName (API $minSdkVersion) or higher. " +
               "Your device is running Android $currentVersionName (API $currentSdkVersion). " +
               "Please use an alternative method or update your device if possible."
    }
    
    /**
     * Get recovery actions for compatibility errors
     */
    private fun getCompatibilityRecoveryActions(feature: String): List<RecoveryAction> {
        return listOf(
            RecoveryAction.TRY_DIFFERENT_METHOD,
            RecoveryAction.CHECK_DEVICE_COMPATIBILITY,
            RecoveryAction.LEARN_MORE
        )
    }
    
    /**
     * Get default error message
     */
    private fun getDefaultErrorMessage(throwable: Throwable, context: ErrorContext): String {
        return "An error occurred in ${context.name.lowercase()}. Please try again or contact support if the problem persists."
    }
    
    /**
     * Get display name for permission
     */
    private fun getPermissionDisplayName(permission: String): String {
        return when (permission) {
            android.Manifest.permission.RECORD_AUDIO -> "Microphone"
            android.Manifest.permission.READ_PHONE_STATE -> "Phone State"
            android.Manifest.permission.READ_CALL_LOG -> "Call Log"
            android.Manifest.permission.READ_CONTACTS -> "Contacts"
            android.Manifest.permission.POST_NOTIFICATIONS -> "Notifications"
            else -> permission.substringAfterLast(".")
        }
    }
    
    /**
     * Get Android version name from SDK version
     */
    private fun getAndroidVersionName(sdkVersion: Int): String {
        return when (sdkVersion) {
            Build.VERSION_CODES.UPSIDE_DOWN_CAKE -> "14"
            Build.VERSION_CODES.TIRAMISU -> "13"
            Build.VERSION_CODES.S_V2 -> "12L"
            Build.VERSION_CODES.S -> "12"
            Build.VERSION_CODES.R -> "11"
            Build.VERSION_CODES.Q -> "10"
            Build.VERSION_CODES.P -> "9"
            Build.VERSION_CODES.O_MR1 -> "8.1"
            Build.VERSION_CODES.O -> "8.0"
            Build.VERSION_CODES.N_MR1 -> "7.1"
            Build.VERSION_CODES.N -> "7.0"
            else -> sdkVersion.toString()
        }
    }
    
    /**
     * Get device information for error reporting
     */
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            sdkVersion = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            device = Build.DEVICE
        )
    }
    
    /**
     * Log error for debugging
     */
    private fun logError(appError: AppError) {
        Timber.e(
            appError.throwable,
            "AppError [${appError.context}]: ${appError.userMessage} | Technical: ${appError.technicalMessage}"
        )
    }
    
    /**
     * Report error to crash analytics (implement based on your analytics service)
     */
    private fun reportError(appError: AppError) {
        // Implement crash reporting here (Firebase Crashlytics, Bugsnag, etc.)
        // Example:
        // FirebaseCrashlytics.getInstance().recordException(appError.throwable)
    }
}

/**
 * Enum representing different error contexts
 */
enum class ErrorContext {
    RECORDING,
    PLAYBACK,
    PERMISSIONS,
    STORAGE,
    NETWORK,
    DATABASE,
    COMPATIBILITY,
    LEGAL,
    UNKNOWN
}

/**
 * Enum representing recovery actions
 */
enum class RecoveryAction {
    REQUEST_PERMISSIONS,
    OPEN_SETTINGS,
    TRY_DIFFERENT_METHOD,
    RESTART_APP,
    FREE_UP_SPACE,
    CHANGE_STORAGE_SETTINGS,
    ENABLE_ACCESSIBILITY_SERVICE,
    CHECK_DEVICE_COMPATIBILITY,
    LEARN_MORE,
    CONTACT_SUPPORT
}
