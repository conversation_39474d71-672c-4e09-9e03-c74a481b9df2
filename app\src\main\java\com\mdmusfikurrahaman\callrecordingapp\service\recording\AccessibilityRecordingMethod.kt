package com.mdmusfikurrahaman.callrecordingapp.service.recording

import android.Manifest
import android.content.Context
import android.content.Intent
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.os.Build
import android.provider.Settings
import timber.log.Timber
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingAccessibilityService
import com.mdmusfikurrahaman.callrecordingapp.util.PermissionUtils

/**
 * AccessibilityService-based recording method
 * 
 * This method uses the AccessibilityService to detect call states and
 * records audio using the microphone. It serves as a fallback when
 * MediaProjection is not available or fails.
 * 
 * Limitations:
 * - Only records one side of the call (microphone input)
 * - Requires accessibility service to be enabled by user
 * - May not work reliably on all devices
 * - Audio quality is limited to microphone input
 */
class AccessibilityRecordingMethod : BaseRecordingMethod() {
    
    companion object {
        private const val SAMPLE_RATE = 44100
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE_FACTOR = 2
    }
    
    private var audioRecord: AudioRecord? = null
    private var recordingJob: Job? = null
    
    override fun getMethodName(): String = "AccessibilityService"
    
    override fun getPriority(): Int = 50 // Medium priority, used as fallback
    
    override fun isSupported(context: Context): Boolean {
        // Available on all Android versions but requires accessibility service
        return true
    }
    
    override fun hasRequiredPermissions(context: Context): Boolean {
        val basicPermissions = PermissionUtils.hasPermissions(context, getRequiredPermissions())
        val accessibilityEnabled = isAccessibilityServiceEnabled(context)
        
        return basicPermissions && accessibilityEnabled
    }
    
    override fun getRequiredPermissions(): List<String> {
        val permissions = mutableListOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.FOREGROUND_SERVICE
        )
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            permissions.add(Manifest.permission.FOREGROUND_SERVICE_MICROPHONE)
        }
        
        return permissions
    }
    
    override suspend fun initialize(context: Context): Boolean {
        return try {
            if (!isAccessibilityServiceEnabled(context)) {
                Timber.w("Accessibility service is not enabled")
                return false
            }
            
            isInitialized = true
            Timber.d("AccessibilityService recording method initialized")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize AccessibilityService recording method")
            false
        }
    }
    
    override suspend fun startRecording(
        outputFile: File,
        quality: RecordingQuality,
        phoneNumber: String
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isInitialized) {
                Timber.e("AccessibilityService method not initialized")
                return@withContext false
            }
            
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            ) * BUFFER_SIZE_FACTOR
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Timber.e("Invalid buffer size for AudioRecord")
                return@withContext false
            }
            
            // Create AudioRecord for microphone input
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            )
            
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                Timber.e("AudioRecord initialization failed")
                cleanup()
                return@withContext false
            }
            
            // Start recording
            audioRecord?.startRecording()
            markRecordingStarted(outputFile)
            
            // Start recording loop in background
            recordingJob = CoroutineScope(Dispatchers.IO).launch {
                recordAudioToFile(outputFile, quality)
            }
            
            Timber.d("AccessibilityService recording started")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start AccessibilityService recording")
            cleanup()
            false
        }
    }
    
    override suspend fun stopRecording(): Boolean = withContext(Dispatchers.IO) {
        try {
            recordingJob?.cancel()
            recordingJob = null
            
            audioRecord?.let { record ->
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                record.release()
            }
            audioRecord = null
            
            markRecordingStopped()
            Timber.d("AccessibilityService recording stopped")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to stop AccessibilityService recording")
            false
        }
    }
    
    override suspend fun cleanup() {
        super.cleanup()
        
        recordingJob?.cancel()
        recordingJob = null
        
        audioRecord?.let { record ->
            try {
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                record.release()
            } catch (e: Exception) {
                Timber.w(e, "Error releasing AudioRecord")
            }
        }
        audioRecord = null
        
        Timber.d("AccessibilityService recording method cleaned up")
    }
    
    /**
     * Record audio data to file
     */
    private suspend fun recordAudioToFile(outputFile: File, quality: RecordingQuality) {
        var fileOutputStream: FileOutputStream? = null
        
        try {
            fileOutputStream = FileOutputStream(outputFile)
            val buffer = ByteArray(1024)
            
            while (isCurrentlyRecording && !Thread.currentThread().isInterrupted) {
                val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (bytesRead > 0) {
                    fileOutputStream.write(buffer, 0, bytesRead)
                } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                    Timber.e("AudioRecord read error: Invalid operation")
                    break
                } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                    Timber.e("AudioRecord read error: Bad value")
                    break
                }
                
                // Small delay to prevent excessive CPU usage
                delay(10)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error recording audio to file")
        } finally {
            try {
                fileOutputStream?.close()
            } catch (e: Exception) {
                Timber.w(e, "Error closing file output stream")
            }
        }
    }
    
    /**
     * Check if the accessibility service is enabled
     */
    private fun isAccessibilityServiceEnabled(context: Context): Boolean {
        return try {
            CallRecordingAccessibilityService.isServiceRunning
        } catch (e: Exception) {
            Timber.e(e, "Error checking accessibility service status")
            false
        }
    }
    
    /**
     * Create an intent to open accessibility settings
     */
    fun createAccessibilitySettingsIntent(): Intent {
        return Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
    }
    
    /**
     * Get user-friendly instructions for enabling the accessibility service
     */
    fun getAccessibilityInstructions(): String {
        return """
            To use this recording method:
            1. Go to Settings > Accessibility
            2. Find "Call Recording Service" in the list
            3. Turn it on and confirm
            4. Return to the app and try recording again
            
            Note: This method only records your side of the conversation.
        """.trimIndent()
    }
}
