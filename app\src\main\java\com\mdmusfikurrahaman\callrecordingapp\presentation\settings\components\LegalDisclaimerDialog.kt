package com.mdmusfikurrahaman.callrecordingapp.presentation.settings.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Gavel
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog

/**
 * Legal disclaimer dialog
 */
@Composable
fun LegalDisclaimerDialog(
    onDismiss: () -> Unit,
    onAccept: () -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.8f)
                .padding(16.dp),
            shape = MaterialTheme.shapes.large
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // Header
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Gavel,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Legal Disclaimer",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Scrollable content
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    Text(
                        text = "IMPORTANT LEGAL NOTICE",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.error,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = buildString {
                            appendLine("By using this call recording application, you acknowledge and agree to the following:")
                            appendLine()
                            appendLine("1. LEGAL COMPLIANCE")
                            appendLine("• You are solely responsible for complying with all applicable laws and regulations regarding call recording in your jurisdiction.")
                            appendLine("• Call recording laws vary significantly by country, state, and region.")
                            appendLine("• Some jurisdictions require consent from all parties before recording.")
                            appendLine("• Some jurisdictions require only one-party consent.")
                            appendLine("• Some jurisdictions prohibit call recording entirely.")
                            appendLine()
                            appendLine("2. CONSENT REQUIREMENTS")
                            appendLine("• You must obtain proper consent before recording any call where required by law.")
                            appendLine("• You are responsible for informing all parties that the call is being recorded.")
                            appendLine("• Failure to obtain required consent may result in legal penalties.")
                            appendLine()
                            appendLine("3. PRIVACY AND DATA PROTECTION")
                            appendLine("• Recorded calls may contain sensitive personal information.")
                            appendLine("• You must handle recorded data in compliance with applicable privacy laws (GDPR, CCPA, etc.).")
                            appendLine("• You are responsible for securing and protecting recorded data.")
                            appendLine()
                            appendLine("4. PROHIBITED USES")
                            appendLine("• Do not use this app for illegal surveillance or harassment.")
                            appendLine("• Do not record calls without proper legal authority.")
                            appendLine("• Do not use recordings to violate others' privacy rights.")
                            appendLine()
                            appendLine("5. DISCLAIMER OF LIABILITY")
                            appendLine("• This app is provided \"as is\" without warranties.")
                            appendLine("• The developers are not responsible for any legal consequences of your use.")
                            appendLine("• You use this app at your own risk and responsibility.")
                            appendLine()
                            appendLine("6. TECHNICAL LIMITATIONS")
                            appendLine("• Recording functionality may not work on all devices or Android versions.")
                            appendLine("• Audio quality and recording success are not guaranteed.")
                            appendLine("• The app may not function properly due to device restrictions or OS limitations.")
                            appendLine()
                            appendLine("By clicking 'I Understand and Accept', you confirm that:")
                            appendLine("• You have read and understood this disclaimer")
                            appendLine("• You will use this app in compliance with all applicable laws")
                            appendLine("• You accept full responsibility for your use of this app")
                            appendLine("• You will not hold the developers liable for any consequences")
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Cancel")
                    }
                    
                    Button(
                        onClick = onAccept,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("I Understand and Accept")
                    }
                }
            }
        }
    }
}
