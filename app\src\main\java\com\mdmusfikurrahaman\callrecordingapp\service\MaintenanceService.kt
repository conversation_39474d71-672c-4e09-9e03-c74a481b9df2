package com.mdmusfikurrahaman.callrecordingapp.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import androidx.work.*
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import kotlinx.coroutines.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import com.mdmusfikurrahaman.callrecordingapp.util.StorageManager
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository

/**
 * Background service for periodic maintenance tasks
 * 
 * This service handles:
 * - Automatic cleanup of old recordings
 * - Storage monitoring and optimization
 * - Database maintenance
 * - Temporary file cleanup
 */
@AndroidEntryPoint
class MaintenanceService : Service() {
    
    @Inject
    lateinit var storageManager: StorageManager
    
    @Inject
    lateinit var repository: CallRecordingRepository
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    companion object {
        private const val MAINTENANCE_WORK_NAME = "maintenance_work"
        
        fun schedulePeriodicMaintenance(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(true)
                .setRequiresCharging(false)
                .build()
            
            val maintenanceWork = PeriodicWorkRequestBuilder<MaintenanceWorker>(
                repeatInterval = 24, // Run daily
                repeatIntervalTimeUnit = TimeUnit.HOURS,
                flexTimeInterval = 4, // Allow 4 hour flex window
                flexTimeIntervalUnit = TimeUnit.HOURS
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()
            
            WorkManager.getInstance(context)
                .enqueueUniquePeriodicWork(
                    MAINTENANCE_WORK_NAME,
                    ExistingPeriodicWorkPolicy.KEEP,
                    maintenanceWork
                )
            
            Timber.d("Scheduled periodic maintenance work")
        }
        
        fun cancelPeriodicMaintenance(context: Context) {
            WorkManager.getInstance(context)
                .cancelUniqueWork(MAINTENANCE_WORK_NAME)
            
            Timber.d("Cancelled periodic maintenance work")
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("MaintenanceService created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("MaintenanceService started")
        
        serviceScope.launch {
            performMaintenance()
            stopSelf()
        }
        
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
        Timber.d("MaintenanceService destroyed")
    }
    
    private suspend fun performMaintenance() {
        try {
            Timber.d("Starting maintenance tasks")
            
            // 1. Storage cleanup
            performStorageCleanup()
            
            // 2. Database maintenance
            performDatabaseMaintenance()
            
            // 3. Check storage status
            checkStorageStatus()
            
            Timber.d("Maintenance tasks completed")
            
        } catch (e: Exception) {
            Timber.e(e, "Error during maintenance")
        }
    }
    
    private suspend fun performStorageCleanup() {
        try {
            val result = storageManager.performAutomaticCleanup()
            
            if (result.success) {
                Timber.d("Storage cleanup completed: ${result.deletedFiles} files deleted, ${result.freedSpaceMB}MB freed")
            } else {
                Timber.w("Storage cleanup failed: ${result.error}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error during storage cleanup")
        }
    }
    
    private suspend fun performDatabaseMaintenance() {
        try {
            // Clean up old recordings from database
            repository.cleanupOldRecordings()
            
            Timber.d("Database maintenance completed")
            
        } catch (e: Exception) {
            Timber.e(e, "Error during database maintenance")
        }
    }
    
    private suspend fun checkStorageStatus() {
        try {
            val storageInfo = storageManager.getStorageInfo()
            val storageStatus = storageManager.getStorageStatus()
            
            Timber.d("Storage status: $storageStatus, Free: ${storageInfo.freeSpaceMB}MB")
            
            // Log warning if storage is low
            when (storageStatus) {
                StorageManager.StorageStatus.LOW -> {
                    Timber.w("Storage is running low: ${storageInfo.freeSpaceMB}MB remaining")
                }
                StorageManager.StorageStatus.CRITICAL -> {
                    Timber.e("Storage is critically low: ${storageInfo.freeSpaceMB}MB remaining")
                }
                StorageManager.StorageStatus.FULL -> {
                    Timber.e("Storage is full!")
                }
                else -> {
                    // Normal status, no action needed
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error checking storage status")
        }
    }
}

/**
 * WorkManager worker for periodic maintenance tasks
 */
@AndroidEntryPoint
class MaintenanceWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    @Inject
    lateinit var storageManager: StorageManager
    
    @Inject
    lateinit var repository: CallRecordingRepository
    
    override suspend fun doWork(): Result {
        return try {
            Timber.d("MaintenanceWorker started")
            
            // Perform storage cleanup
            val cleanupResult = storageManager.performAutomaticCleanup()
            
            if (cleanupResult.success) {
                Timber.d("Maintenance work completed successfully")
                Result.success()
            } else {
                Timber.w("Maintenance work completed with warnings: ${cleanupResult.error}")
                Result.success() // Still consider it successful
            }
            
        } catch (e: Exception) {
            Timber.e(e, "MaintenanceWorker failed")
            Result.retry()
        }
    }
}
