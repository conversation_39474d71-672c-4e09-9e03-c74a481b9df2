# Call Recording App

A comprehensive Android application for recording phone calls with multiple recording methods, legal compliance features, and modern UI built with Jetpack Compose.

## Features

### Core Functionality
- **Multiple Recording Methods**: Supports MediaProjection (Android 10+), AccessibilityService fallback, and legacy AudioSource methods
- **Automatic Call Detection**: Detects incoming and outgoing calls automatically
- **High-Quality Audio Recording**: Multiple quality settings from low to very high
- **Modern UI**: Built with Jetpack Compose and Material Design 3
- **Audio Playback**: Built-in audio player with speed control and seeking
- **Search and Filter**: Search recordings by contact name or phone number

### Legal Compliance
- **Legal Disclaimer**: Comprehensive legal disclaimer with acceptance tracking
- **Consent Management**: Optional consent dialogs before recording
- **Privacy Policy**: Built-in privacy policy compliance
- **Audit Trail**: Logging of legal events for compliance purposes
- **Jurisdiction Warnings**: Warnings about local recording laws

### Advanced Features
- **AI Transcription**: Optional AI-powered transcription of recordings (extensible)
- **Contact Integration**: Displays caller names from contacts
- **Favorites System**: Mark important recordings as favorites
- **Auto-Delete**: Automatic cleanup of old recordings
- **Storage Management**: Comprehensive storage monitoring and cleanup
- **Error Handling**: Robust error handling with user-friendly messages
- **Device Compatibility**: Automatic device compatibility checking

## Technical Architecture

### Architecture Pattern
- **MVVM**: Model-View-ViewModel architecture with Jetpack Compose
- **Repository Pattern**: Centralized data access layer
- **Dependency Injection**: Hilt for dependency management
- **Reactive Programming**: Kotlin Coroutines and Flow for async operations

### Key Components
- **Room Database**: Local storage for recordings and metadata
- **Foreground Service**: Background recording service with notifications
- **MediaPlayer**: Audio playback with advanced controls
- **WorkManager**: Background tasks for maintenance and cleanup
- **Encrypted SharedPreferences**: Secure storage for legal compliance data

### Recording Methods
1. **MediaProjection (Android 10+)**: System audio capture for best quality
2. **AccessibilityService**: Fallback method using accessibility features
3. **Legacy AudioSource**: Deprecated method for older Android versions

## Installation

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24 (Android 7.0) or higher
- Kotlin 1.8.0 or later

### Setup
1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/call-recording-app.git
   cd call-recording-app
   ```

2. Open the project in Android Studio

3. Sync the project with Gradle files

4. Build and run the application

### Required Permissions
The app requires the following permissions:
- `RECORD_AUDIO`: For recording audio
- `READ_PHONE_STATE`: For detecting call states
- `READ_CALL_LOG`: For accessing call information
- `READ_CONTACTS`: For displaying contact names (optional)
- `POST_NOTIFICATIONS`: For recording notifications (Android 13+)
- `FOREGROUND_SERVICE`: For background recording service
- `FOREGROUND_SERVICE_MICROPHONE`: For microphone access in background (Android 14+)

## Usage

### First Launch
1. Accept the legal disclaimer
2. Grant required permissions
3. Configure recording settings
4. Enable accessibility service if needed (for fallback recording)

### Recording Calls
- **Automatic**: Calls are recorded automatically when enabled in settings
- **Manual**: Use the floating action button to start manual recording
- **Consent**: Optional consent dialogs can be enabled for legal compliance

### Managing Recordings
- View all recordings in the main list
- Search by contact name or phone number
- Play recordings with built-in audio player
- Share recordings with other apps
- Delete unwanted recordings
- Mark important recordings as favorites

### Settings
- **Recording Quality**: Choose from low to very high quality
- **Auto-Delete**: Configure automatic deletion of old recordings
- **Consent Dialogs**: Enable/disable consent prompts
- **Storage Management**: Monitor and manage storage usage
- **Legal Compliance**: View compliance status and legal information

## Legal Considerations

### Important Notice
**This app is provided for educational and lawful purposes only. Users are solely responsible for complying with all applicable laws and regulations regarding call recording in their jurisdiction.**

### Key Legal Points
- Call recording laws vary significantly by country, state, and region
- Some jurisdictions require consent from all parties before recording
- Some jurisdictions require only one-party consent
- Some jurisdictions prohibit call recording entirely
- Always research local laws before using this app
- Obtain proper consent when required by law
- Use recordings only for lawful purposes

### Compliance Features
- Legal disclaimer with acceptance tracking
- Optional consent dialogs before recording
- Privacy policy compliance
- Audit trail for legal events
- Jurisdiction-specific warnings

## Device Compatibility

### Supported Android Versions
- **Android 7.0 (API 24)**: Minimum supported version
- **Android 7.1-9 (API 25-28)**: Legacy AudioSource method available
- **Android 10+ (API 29+)**: Full MediaProjection support

### Known Limitations
- **Samsung devices**: May have call recording restrictions
- **Google Pixel**: Limited call recording capability
- **Huawei/Honor**: May require additional permissions
- **Xiaomi/MIUI**: Aggressive power management may affect recording
- **OnePlus/OxygenOS**: Generally good compatibility

### Compatibility Checking
The app includes automatic device compatibility checking that:
- Verifies Android version support
- Checks hardware capabilities
- Identifies manufacturer restrictions
- Provides recommendations for optimal setup

## Development

### Project Structure
```
app/src/main/java/com/mdmusfikurrahaman/callrecordingapp/
├── audio/                  # Audio playback management
├── compatibility/          # Device compatibility checking
├── data/                   # Data layer (database, repository)
├── di/                     # Dependency injection modules
├── error/                  # Error handling system
├── legal/                  # Legal compliance features
├── permission/             # Permission management
├── presentation/           # UI layer (Compose screens)
├── receiver/               # Broadcast receivers
├── service/                # Background services
└── util/                   # Utility classes
```

### Key Dependencies
- **Jetpack Compose**: Modern UI toolkit
- **Hilt**: Dependency injection
- **Room**: Local database
- **Coroutines**: Asynchronous programming
- **WorkManager**: Background tasks
- **Timber**: Logging
- **Security Crypto**: Encrypted storage

### Building
```bash
# Debug build
./gradlew assembleDebug

# Release build
./gradlew assembleRelease

# Run tests
./gradlew test
```

## Contributing

### Guidelines
1. Follow the existing code style and architecture
2. Add appropriate tests for new features
3. Update documentation for significant changes
4. Ensure legal compliance features are maintained
5. Test on multiple devices and Android versions

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Update documentation
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Disclaimer

This software is provided "as is" without warranties of any kind. The developers are not responsible for any legal consequences arising from the use of this application. Users must ensure compliance with all applicable laws and regulations in their jurisdiction.

## Support

For support, bug reports, or feature requests, please:
1. Check the existing issues on GitHub
2. Create a new issue with detailed information
3. Include device information and Android version
4. Provide steps to reproduce any bugs

## Acknowledgments

- Android Open Source Project for the platform
- Jetpack Compose team for the modern UI toolkit
- Contributors and testers who helped improve the app
- Legal experts who provided guidance on compliance features
