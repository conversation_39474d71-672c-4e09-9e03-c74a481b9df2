package com.mdmusfikurrahaman.callrecordingapp.service.recording

import android.Manifest
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioPlaybackCaptureConfiguration
import android.media.AudioRecord
import android.media.MediaRecorder
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import androidx.annotation.RequiresApi
import timber.log.Timber
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality

/**
 * MediaProjection-based recording method for Android 10+ (API 29+)
 * 
 * This method uses the MediaProjection API to capture audio from the system.
 * It requires user permission through a system dialog and can capture
 * both sides of a phone call on supported devices.
 * 
 * Note: This method has limitations:
 * - Requires explicit user permission for each recording session
 * - May not work on all devices due to OEM restrictions
 * - Audio quality depends on device implementation
 */
@RequiresApi(Build.VERSION_CODES.Q)
class MediaProjectionRecordingMethod : BaseRecordingMethod() {
    
    companion object {
        private const val SAMPLE_RATE = 44100
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_STEREO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE_FACTOR = 2
    }
    
    private var mediaProjection: MediaProjection? = null
    private var audioRecord: AudioRecord? = null
    private var recordingJob: Job? = null
    private var mediaProjectionManager: MediaProjectionManager? = null
    
    override fun getMethodName(): String = "MediaProjection"
    
    override fun getPriority(): Int = 100 // Highest priority for Android 10+
    
    override fun isSupported(context: Context): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q
    }
    
    override fun hasRequiredPermissions(context: Context): Boolean {
        return PermissionUtils.hasPermissions(context, getRequiredPermissions())
    }
    
    override fun getRequiredPermissions(): List<String> {
        return listOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.FOREGROUND_SERVICE,
            Manifest.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
        )
    }
    
    override suspend fun initialize(context: Context): Boolean {
        return try {
            mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            isInitialized = true
            Timber.d("MediaProjection recording method initialized")
            true
        } catch (e: Exception) {
            Timber.e(e, "Failed to initialize MediaProjection recording method")
            false
        }
    }
    
    override suspend fun startRecording(
        outputFile: File,
        quality: RecordingQuality,
        phoneNumber: String
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!isInitialized) {
                Timber.e("MediaProjection method not initialized")
                return@withContext false
            }
            
            // Note: In a real implementation, you would need to request MediaProjection permission
            // from the user through an Activity. This is a simplified version.
            
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            ) * BUFFER_SIZE_FACTOR
            
            if (bufferSize == AudioRecord.ERROR || bufferSize == AudioRecord.ERROR_BAD_VALUE) {
                Timber.e("Invalid buffer size for AudioRecord")
                return@withContext false
            }
            
            // Create AudioPlaybackCaptureConfiguration for capturing system audio
            val config = AudioPlaybackCaptureConfiguration.Builder(mediaProjection!!)
                .addMatchingUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                .addMatchingUsage(AudioAttributes.USAGE_MEDIA)
                .build()
            
            // Create AudioRecord with playback capture configuration
            audioRecord = AudioRecord.Builder()
                .setAudioSource(MediaRecorder.AudioSource.DEFAULT)
                .setAudioFormat(
                    AudioFormat.Builder()
                        .setEncoding(AUDIO_FORMAT)
                        .setSampleRate(SAMPLE_RATE)
                        .setChannelMask(CHANNEL_CONFIG)
                        .build()
                )
                .setBufferSizeInBytes(bufferSize)
                .setAudioPlaybackCaptureConfig(config)
                .build()
            
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                Timber.e("AudioRecord initialization failed")
                cleanup()
                return@withContext false
            }
            
            // Start recording
            audioRecord?.startRecording()
            markRecordingStarted(outputFile)
            
            // Start recording loop in background
            recordingJob = CoroutineScope(Dispatchers.IO).launch {
                recordAudioToFile(outputFile, quality)
            }
            
            Timber.d("MediaProjection recording started")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start MediaProjection recording")
            cleanup()
            false
        }
    }
    
    override suspend fun stopRecording(): Boolean = withContext(Dispatchers.IO) {
        try {
            recordingJob?.cancel()
            recordingJob = null
            
            audioRecord?.let { record ->
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                record.release()
            }
            audioRecord = null
            
            markRecordingStopped()
            Timber.d("MediaProjection recording stopped")
            true
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to stop MediaProjection recording")
            false
        }
    }
    
    override suspend fun cleanup() {
        super.cleanup()
        
        recordingJob?.cancel()
        recordingJob = null
        
        audioRecord?.let { record ->
            try {
                if (record.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                    record.stop()
                }
                record.release()
            } catch (e: Exception) {
                Timber.w(e, "Error releasing AudioRecord")
            }
        }
        audioRecord = null
        
        mediaProjection?.stop()
        mediaProjection = null
        
        Timber.d("MediaProjection recording method cleaned up")
    }
    
    /**
     * Record audio data to file
     */
    private suspend fun recordAudioToFile(outputFile: File, quality: RecordingQuality) {
        var fileOutputStream: FileOutputStream? = null
        
        try {
            fileOutputStream = FileOutputStream(outputFile)
            val buffer = ByteArray(1024)
            
            while (isCurrentlyRecording && !Thread.currentThread().isInterrupted) {
                val bytesRead = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                
                if (bytesRead > 0) {
                    fileOutputStream.write(buffer, 0, bytesRead)
                } else if (bytesRead == AudioRecord.ERROR_INVALID_OPERATION) {
                    Timber.e("AudioRecord read error: Invalid operation")
                    break
                } else if (bytesRead == AudioRecord.ERROR_BAD_VALUE) {
                    Timber.e("AudioRecord read error: Bad value")
                    break
                }
                
                // Small delay to prevent excessive CPU usage
                delay(10)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error recording audio to file")
        } finally {
            try {
                fileOutputStream?.close()
            } catch (e: Exception) {
                Timber.w(e, "Error closing file output stream")
            }
        }
    }
    
    /**
     * Set the MediaProjection instance (should be called from an Activity)
     */
    fun setMediaProjection(mediaProjection: MediaProjection) {
        this.mediaProjection = mediaProjection
    }
    
    /**
     * Create an intent to request MediaProjection permission
     */
    fun createMediaProjectionIntent(context: Context): Intent? {
        return mediaProjectionManager?.createScreenCaptureIntent()
    }
    
    /**
     * Handle the result from MediaProjection permission request
     */
    fun handleMediaProjectionResult(resultCode: Int, data: Intent?): Boolean {
        return try {
            if (data != null && mediaProjectionManager != null) {
                mediaProjection = mediaProjectionManager!!.getMediaProjection(resultCode, data)
                mediaProjection != null
            } else {
                false
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to handle MediaProjection result")
            false
        }
    }
}
