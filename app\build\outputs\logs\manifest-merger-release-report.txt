-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:137:9-145:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:141:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:139:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:140:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:138:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
MERGED from [androidx.databinding:databinding-adapters:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\864db2a38b1ada49a12ab4f904eeaf56\transformed\databinding-adapters-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50bf7ae7242ae495650e1b2d029e682b\transformed\databinding-ktx-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a242d0e3fe05611829b9bf565c2d2230\transformed\databinding-runtime-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64f3758e6dea3a8441592ebaf2785e87\transformed\viewbinding-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89a534fb9477b24ba9e77b6102d9554c\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1304ab331c40c828a08441e2836dbb2\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bbd9ac083b2cb72ba4533977049ddf5\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da996110ab603543fbc3bfb15cf57a25\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03959fa5a9aef612010f325a5f5415c1\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a89022cf95ec4feb687ddd861b473905\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b170b3dce601d0e2e77a421053829c3\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125614bd4c72f93cfeffaa0b5947d611\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7473b31c75d2d339c0da8a1a3f9146d6\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43940a1b6e151f9713d4f2712d4ab6e8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e031c7c3109e62e465dfe831a17ba4f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1e4d034409f719670a3d1d52262c91\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c649ea3347429505298e138ee9bc0d\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab9a8fce592b1bb05a0549bf6b2f96e\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef1f7dd8d30bc2ef8e93356a5c134cb6\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23f21d3c419ff137a435cdfe89d2284c\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290852fed67b1f7ede9e4d6be3af45e0\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d7a50004433e3faf5d01891f7be7e6\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4e95de88c8795ab61f3756bf426f85\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f56a1774045d70a99114b425fe27abf5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e0b3af703173a357d5c452b0d31980\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d69932c91f0fef05914460b87ad306\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05bfc679feb84f7960673e14a092f722\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fe02253847a043fcae5afbcc27c97f6\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c3cb673e92d298dc35787fd074de573\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\034e2573f36290a4a32128b6b71bbd8a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71c526efb0071c19d0ae659cf86c0651\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29b25e9474ca7db2b6a6e7147143490b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbbf53aeb58a5d429e474487ae97e95f\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\892927a0cab796c0476c1ab09195e311\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c5662662e8664478368d7ed31dbfa7\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c59d19bc7fc035d2203a5a23b4fc9284\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12be4bad4acc6098c8fc1af27fb62470\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66bf99fee238692c9365d367ed901c17\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8fa1040e50668cfb5d01b2d1e6ab69\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828144479f57a357cc602468c3955bc\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc1a006e739b7acb2f5c9728bd3e669\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5cb725166ce2049d007c478cd34ec67\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a34c0e5d021ef8bc7ea262a08830343\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45c65098f87a82b2ced71dc910d12f54\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee7e408228fe400cb0e8fd0c3f71164\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c84042c82f293e7e69815da52c19631\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d8b1567230f4f9d84206667cd766088\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cead9067d77ad52a172ebd0b049dbc08\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\137fd311b6f2521592e86ef7d4099744\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af92e979f9adeda4e43ee29d199099e8\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7556c0ff2cefe1e8342821623d39f001\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a3c9d4c48c3cb223481b0ee64ecc96d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81961c3202906e68c6880cd14c4c1b00\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97b638c71545b7a061eb5555f7542f7\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01674ce1aea2017cb3b29df3cd41b3bd\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06bc9f5677e619f00f30f344d5b20567\transformed\timber-5.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\771cce08319481ccef920c4e5c93b6c1\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf975b981b84831bc57ef5c23ba962ba\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90e14be56c06b51a1131ce756dbd0276\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a681391b1621fd08da49955106caed3d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bf3b24e6c908fe4fd1a22f410b145a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7972879ce704e341328075eb7c94ff66\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fcc5c4baa22c099f18a0c48ba6d68f5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db2256c39b757080d15588c7bb6b9a4e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85790714e108f535684af6d7ff89c8d7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4373c12c440c07d886c38ae8c2dccb8a\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd6f44a2bd2bb29c5f51fa52f8fc647b\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4214dc7ef7dce5e677f3067d609fd5ff\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16887201ebd48a74c77eb510706bfd9\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bde0e766f81ba79bd68e871676e4c294\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c120a20c476563419e57261d92ff995c\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2b019ace5aca54dc238ff1947938c\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f72f6a7d6750f3b86ead51fabda3f950\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8daee8ba08b95304eec9fe5a37244dd0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80499a551b6ae3398b7f90b7c540df3b\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a3b7673d704d8318f3f29ac16e49652\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:22-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:5-94
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:22-91
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:5-88
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:22-85
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:5-17:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:5-19:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:19:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:5-29:47
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:29:9-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:22-82
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:22-69
uses-permission#android.permission.PROCESS_OUTGOING_CALLS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:5-34:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:34:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:22-65
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e0b3af703173a357d5c452b0d31980\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e0b3af703173a357d5c452b0d31980\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c120a20c476563419e57261d92ff995c\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c120a20c476563419e57261d92ff995c\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:22-76
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7473b31c75d2d339c0da8a1a3f9146d6\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7473b31c75d2d339c0da8a1a3f9146d6\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43940a1b6e151f9713d4f2712d4ab6e8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43940a1b6e151f9713d4f2712d4ab6e8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06bc9f5677e619f00f30f344d5b20567\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06bc9f5677e619f00f30f344d5b20567\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bf3b24e6c908fe4fd1a22f410b145a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bf3b24e6c908fe4fd1a22f410b145a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:53:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:51:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:49:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:47:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:50:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:54:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:48:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:45:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:52:9-54
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:46:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:44:9-49
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:56:9-66:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:59:13-45
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:61:13-43
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:58:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:60:13-58
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:57:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:62:13-65:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:27-74
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.settings.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:69:9-73:71
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:73:13-68
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:72:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:70:13-67
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.detail.RecordingDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:76:9-80:71
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:80:13-68
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:79:13-54
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:78:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:77:13-72
service#com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:83:9-87:74
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:85:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:86:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:87:13-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:84:13-57
service#com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingAccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:90:9-101:19
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:92:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:93:13-37
	android:permission
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:94:13-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:91:13-70
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:95:13-97:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:17-92
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:98:13-100:72
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:100:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:99:17-60
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.PhoneStateReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:104:9-111:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:106:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:107:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:105:13-56
intent-filter#action:name:android.intent.action.PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:13-110:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:28-51
action#android.intent.action.PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:25-73
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.CallLogReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:114:9-121:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:116:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:117:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:115:13-53
intent-filter#action:name:android.intent.action.NEW_OUTGOING_CALL
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:118:13-120:29
action#android.intent.action.NEW_OUTGOING_CALL
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:17-82
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:25-79
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.BootReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:124:9-134:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:126:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:127:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:125:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:13-133:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:17-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:17-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:17-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:25-78
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:17-50
	android:scheme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:23-47
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:142:13-144:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:144:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:143:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\864db2a38b1ada49a12ab4f904eeaf56\transformed\databinding-adapters-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\864db2a38b1ada49a12ab4f904eeaf56\transformed\databinding-adapters-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50bf7ae7242ae495650e1b2d029e682b\transformed\databinding-ktx-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50bf7ae7242ae495650e1b2d029e682b\transformed\databinding-ktx-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a242d0e3fe05611829b9bf565c2d2230\transformed\databinding-runtime-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a242d0e3fe05611829b9bf565c2d2230\transformed\databinding-runtime-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64f3758e6dea3a8441592ebaf2785e87\transformed\viewbinding-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64f3758e6dea3a8441592ebaf2785e87\transformed\viewbinding-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89a534fb9477b24ba9e77b6102d9554c\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89a534fb9477b24ba9e77b6102d9554c\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1304ab331c40c828a08441e2836dbb2\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1304ab331c40c828a08441e2836dbb2\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bbd9ac083b2cb72ba4533977049ddf5\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bbd9ac083b2cb72ba4533977049ddf5\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da996110ab603543fbc3bfb15cf57a25\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da996110ab603543fbc3bfb15cf57a25\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03959fa5a9aef612010f325a5f5415c1\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03959fa5a9aef612010f325a5f5415c1\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a89022cf95ec4feb687ddd861b473905\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a89022cf95ec4feb687ddd861b473905\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b170b3dce601d0e2e77a421053829c3\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b170b3dce601d0e2e77a421053829c3\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125614bd4c72f93cfeffaa0b5947d611\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125614bd4c72f93cfeffaa0b5947d611\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7473b31c75d2d339c0da8a1a3f9146d6\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7473b31c75d2d339c0da8a1a3f9146d6\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43940a1b6e151f9713d4f2712d4ab6e8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43940a1b6e151f9713d4f2712d4ab6e8\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e031c7c3109e62e465dfe831a17ba4f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e031c7c3109e62e465dfe831a17ba4f\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1e4d034409f719670a3d1d52262c91\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1e4d034409f719670a3d1d52262c91\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c649ea3347429505298e138ee9bc0d\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c649ea3347429505298e138ee9bc0d\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab9a8fce592b1bb05a0549bf6b2f96e\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab9a8fce592b1bb05a0549bf6b2f96e\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef1f7dd8d30bc2ef8e93356a5c134cb6\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef1f7dd8d30bc2ef8e93356a5c134cb6\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23f21d3c419ff137a435cdfe89d2284c\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23f21d3c419ff137a435cdfe89d2284c\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290852fed67b1f7ede9e4d6be3af45e0\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290852fed67b1f7ede9e4d6be3af45e0\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d7a50004433e3faf5d01891f7be7e6\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d7a50004433e3faf5d01891f7be7e6\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4e95de88c8795ab61f3756bf426f85\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4e95de88c8795ab61f3756bf426f85\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f56a1774045d70a99114b425fe27abf5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f56a1774045d70a99114b425fe27abf5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e0b3af703173a357d5c452b0d31980\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e0b3af703173a357d5c452b0d31980\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d69932c91f0fef05914460b87ad306\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d69932c91f0fef05914460b87ad306\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05bfc679feb84f7960673e14a092f722\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05bfc679feb84f7960673e14a092f722\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fe02253847a043fcae5afbcc27c97f6\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fe02253847a043fcae5afbcc27c97f6\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c3cb673e92d298dc35787fd074de573\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c3cb673e92d298dc35787fd074de573\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\034e2573f36290a4a32128b6b71bbd8a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\034e2573f36290a4a32128b6b71bbd8a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71c526efb0071c19d0ae659cf86c0651\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71c526efb0071c19d0ae659cf86c0651\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29b25e9474ca7db2b6a6e7147143490b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29b25e9474ca7db2b6a6e7147143490b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbbf53aeb58a5d429e474487ae97e95f\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbbf53aeb58a5d429e474487ae97e95f\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\892927a0cab796c0476c1ab09195e311\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\892927a0cab796c0476c1ab09195e311\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c5662662e8664478368d7ed31dbfa7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c5662662e8664478368d7ed31dbfa7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c59d19bc7fc035d2203a5a23b4fc9284\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c59d19bc7fc035d2203a5a23b4fc9284\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12be4bad4acc6098c8fc1af27fb62470\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12be4bad4acc6098c8fc1af27fb62470\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66bf99fee238692c9365d367ed901c17\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66bf99fee238692c9365d367ed901c17\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8fa1040e50668cfb5d01b2d1e6ab69\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8fa1040e50668cfb5d01b2d1e6ab69\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828144479f57a357cc602468c3955bc\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828144479f57a357cc602468c3955bc\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc1a006e739b7acb2f5c9728bd3e669\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc1a006e739b7acb2f5c9728bd3e669\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5cb725166ce2049d007c478cd34ec67\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5cb725166ce2049d007c478cd34ec67\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a34c0e5d021ef8bc7ea262a08830343\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a34c0e5d021ef8bc7ea262a08830343\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45c65098f87a82b2ced71dc910d12f54\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45c65098f87a82b2ced71dc910d12f54\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee7e408228fe400cb0e8fd0c3f71164\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee7e408228fe400cb0e8fd0c3f71164\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c84042c82f293e7e69815da52c19631\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c84042c82f293e7e69815da52c19631\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d8b1567230f4f9d84206667cd766088\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d8b1567230f4f9d84206667cd766088\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cead9067d77ad52a172ebd0b049dbc08\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cead9067d77ad52a172ebd0b049dbc08\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\137fd311b6f2521592e86ef7d4099744\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\137fd311b6f2521592e86ef7d4099744\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af92e979f9adeda4e43ee29d199099e8\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af92e979f9adeda4e43ee29d199099e8\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7556c0ff2cefe1e8342821623d39f001\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7556c0ff2cefe1e8342821623d39f001\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a3c9d4c48c3cb223481b0ee64ecc96d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a3c9d4c48c3cb223481b0ee64ecc96d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81961c3202906e68c6880cd14c4c1b00\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81961c3202906e68c6880cd14c4c1b00\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97b638c71545b7a061eb5555f7542f7\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97b638c71545b7a061eb5555f7542f7\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01674ce1aea2017cb3b29df3cd41b3bd\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01674ce1aea2017cb3b29df3cd41b3bd\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06bc9f5677e619f00f30f344d5b20567\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06bc9f5677e619f00f30f344d5b20567\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\771cce08319481ccef920c4e5c93b6c1\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\771cce08319481ccef920c4e5c93b6c1\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf975b981b84831bc57ef5c23ba962ba\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf975b981b84831bc57ef5c23ba962ba\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90e14be56c06b51a1131ce756dbd0276\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90e14be56c06b51a1131ce756dbd0276\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a681391b1621fd08da49955106caed3d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a681391b1621fd08da49955106caed3d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bf3b24e6c908fe4fd1a22f410b145a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bf3b24e6c908fe4fd1a22f410b145a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7972879ce704e341328075eb7c94ff66\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7972879ce704e341328075eb7c94ff66\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fcc5c4baa22c099f18a0c48ba6d68f5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fcc5c4baa22c099f18a0c48ba6d68f5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db2256c39b757080d15588c7bb6b9a4e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db2256c39b757080d15588c7bb6b9a4e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85790714e108f535684af6d7ff89c8d7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85790714e108f535684af6d7ff89c8d7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4373c12c440c07d886c38ae8c2dccb8a\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4373c12c440c07d886c38ae8c2dccb8a\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd6f44a2bd2bb29c5f51fa52f8fc647b\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd6f44a2bd2bb29c5f51fa52f8fc647b\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4214dc7ef7dce5e677f3067d609fd5ff\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4214dc7ef7dce5e677f3067d609fd5ff\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16887201ebd48a74c77eb510706bfd9\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b16887201ebd48a74c77eb510706bfd9\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bde0e766f81ba79bd68e871676e4c294\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bde0e766f81ba79bd68e871676e4c294\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c120a20c476563419e57261d92ff995c\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c120a20c476563419e57261d92ff995c\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2b019ace5aca54dc238ff1947938c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2b019ace5aca54dc238ff1947938c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f72f6a7d6750f3b86ead51fabda3f950\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f72f6a7d6750f3b86ead51fabda3f950\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8daee8ba08b95304eec9fe5a37244dd0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8daee8ba08b95304eec9fe5a37244dd0\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80499a551b6ae3398b7f90b7c540df3b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80499a551b6ae3398b7f90b7c540df3b\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a3b7673d704d8318f3f29ac16e49652\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a3b7673d704d8318f3f29ac16e49652\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.mdmusfikurrahaman.callrecordingapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.mdmusfikurrahaman.callrecordingapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
