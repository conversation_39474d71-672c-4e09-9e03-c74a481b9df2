{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-64:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,196,264,330,410,488,588,686", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "127,191,259,325,405,483,583,681,759"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3980,4057,4121,4189,4255,4335,4413,4513,4611", "endColumns": "76,63,67,65,79,77,99,97,77", "endOffsets": "4052,4116,4184,4250,4330,4408,4508,4606,4684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,301,419,540,639,739,856,1003,1130,1280,1365,1464,1559,1657,1778,1916,2020,2167,2315,2462,2632,2770,2893,3018,3143,3239,3338,3463,3598,3705,3809,3922,4067,4216,4332,4438,4514,4614,4711,4821,4910,4999,5106,5186,5270,5370,5474,5574,5680,5768,5880,5985,6095,6214,6294,6401", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "174,296,414,535,634,734,851,998,1125,1275,1360,1459,1554,1652,1773,1911,2015,2162,2310,2457,2627,2765,2888,3013,3138,3234,3333,3458,3593,3700,3804,3917,4062,4211,4327,4433,4509,4609,4706,4816,4905,4994,5101,5181,5265,5365,5469,5569,5675,5763,5875,5980,6090,6209,6289,6396,6491"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5990,6114,6236,6354,6475,6574,6674,6791,6938,7065,7215,7300,7399,7494,7592,7713,7851,7955,8102,8250,8397,8567,8705,8828,8953,9078,9174,9273,9398,9533,9640,9744,9857,10002,10151,10267,10373,10449,10549,10646,10756,10845,10934,11041,11121,11205,11305,11409,11509,11615,11703,11815,11920,12030,12149,12229,12336", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "6109,6231,6349,6470,6569,6669,6786,6933,7060,7210,7295,7394,7489,7587,7708,7846,7950,8097,8245,8392,8562,8700,8823,8948,9073,9169,9268,9393,9528,9635,9739,9852,9997,10146,10262,10368,10444,10544,10641,10751,10840,10929,11036,11116,11200,11300,11404,11504,11610,11698,11810,11915,12025,12144,12224,12331,12426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,91", "endOffsets": "136,228"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "13458,13544", "endColumns": "85,91", "endOffsets": "13539,13631"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,1016,1102,1173,1256,1333,1408,1486,1552", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,1011,1097,1168,1251,1328,1403,1481,1547,1674"}, "to": {"startLines": "26,27,28,29,30,82,83,141,142,143,144,145,146,147,148,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1476,1575,1661,1758,1860,5816,5898,12431,12523,12607,12694,12780,12851,12934,13011,13187,13265,13331", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "1570,1656,1753,1855,1945,5893,5985,12518,12602,12689,12775,12846,12929,13006,13081,13260,13326,13453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,497,692,783,874,954,1039,1130,1208,1274,1375,1478,1545,1610,1672,1743,1861,1981,2101,2170,2257,2331,2411,2502,2593,2658,2722,2775,2833,2881,2942,3007,3069,3134,3202,3266,3324,3390,3454,3520,3572,3634,3713,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "280,492,687,778,869,949,1034,1125,1203,1269,1370,1473,1540,1605,1667,1738,1856,1976,2096,2165,2252,2326,2406,2497,2588,2653,2717,2770,2828,2876,2937,3002,3064,3129,3197,3261,3319,3385,3449,3515,3567,3629,3708,3787,3844"}, "to": {"startLines": "2,11,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,547,1950,2041,2132,2212,2297,2388,2466,2532,2633,2736,2803,2868,2930,3001,3119,3239,3359,3428,3515,3589,3669,3760,3851,3916,4689,4742,4800,4848,4909,4974,5036,5101,5169,5233,5291,5357,5421,5487,5539,5601,5680,5759", "endLines": "10,14,18,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,90,90,79,84,90,77,65,100,102,66,64,61,70,117,119,119,68,86,73,79,90,90,64,63,52,57,47,60,64,61,64,67,63,57,65,63,65,51,61,78,78,56", "endOffsets": "330,542,737,2036,2127,2207,2292,2383,2461,2527,2628,2731,2798,2863,2925,2996,3114,3234,3354,3423,3510,3584,3664,3755,3846,3911,3975,4737,4795,4843,4904,4969,5031,5096,5164,5228,5286,5352,5416,5482,5534,5596,5675,5754,5811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "19,20,21,22,23,24,25,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "742,839,941,1042,1139,1246,1354,13086", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "834,936,1037,1134,1241,1349,1471,13182"}}]}]}