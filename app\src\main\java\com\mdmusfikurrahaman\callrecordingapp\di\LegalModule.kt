package com.mdmusfikurrahaman.callrecordingapp.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.legal.LegalComplianceManager
import com.mdmusfikurrahaman.callrecordingapp.legal.ConsentDialogManager
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository

/**
 * Hilt module for providing legal compliance dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object LegalModule {
    
    @Provides
    @Singleton
    fun provideLegalComplianceManager(
        @ApplicationContext context: Context,
        repository: CallRecordingRepository
    ): LegalComplianceManager {
        return LegalComplianceManager(context, repository)
    }
    
    @Provides
    @Singleton
    fun provideConsentDialogManager(
        legalComplianceManager: LegalComplianceManager
    ): ConsentDialogManager {
        return ConsentDialogManager(legalComplianceManager)
    }
}
