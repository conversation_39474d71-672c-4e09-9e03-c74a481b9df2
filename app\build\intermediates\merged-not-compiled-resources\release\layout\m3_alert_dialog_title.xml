<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/topPanel"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

  <!-- If the client uses a customTitle, it will be added here. -->

  <LinearLayout
      android:id="@+id/title_template"
      style="?attr/materialAlertDialogTitlePanelStyle">

    <ImageView
        android:id="@android:id/icon"
        tools:ignore="ContentDescription"
        style="?attr/materialAlertDialogTitleIconStyle"/>

    <androidx.appcompat.widget.DialogTitle
        android:id="@+id/alertTitle"
        style="?attr/materialAlertDialogTitleTextStyle"/>
  </LinearLayout>

  <android.widget.Space
      android:id="@+id/titleDividerNoCustom"
      android:layout_width="match_parent"
      android:layout_height="@dimen/m3_alert_dialog_title_bottom_margin"
      android:visibility="gone"/>
</LinearLayout>
