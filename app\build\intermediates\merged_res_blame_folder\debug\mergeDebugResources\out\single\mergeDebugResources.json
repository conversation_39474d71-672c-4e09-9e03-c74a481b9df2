[{"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/xml_backup_rules.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/xml/backup_rules.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/drawable_ic_launcher_background.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/drawable/ic_launcher_background.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/xml_accessibility_service_config.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/xml/accessibility_service_config.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/xml_data_extraction_rules.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/xml/data_extraction_rules.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/drawable_ic_launcher_foreground.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/xml_file_paths.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/xml/file_paths.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/drawable_ic_stop_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/drawable/ic_stop_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/drawable_ic_error_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/drawable/ic_error_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-76:/drawable_ic_mic_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-78:/drawable/ic_mic_24.xml"}]