[{"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_launcher_background.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_launcher_background.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_stop_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_stop_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/xml_backup_rules.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/xml/backup_rules.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_more_vert_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_more_vert_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/layout_activity_main.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/layout/activity_main.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/xml_data_extraction_rules.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/xml/data_extraction_rules.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/layout_item_recording.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/layout/item_recording.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/xml_file_paths.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/xml/file_paths.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/xml_accessibility_service_config.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/xml/accessibility_service_config.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_call_received_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_call_received_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_search_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_search_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_play_arrow_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_play_arrow_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_launcher_foreground.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_mic_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_mic_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/drawable_ic_error_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/drawable/ic_error_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-54:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-56:/mipmap-hdpi/ic_launcher.webp"}]