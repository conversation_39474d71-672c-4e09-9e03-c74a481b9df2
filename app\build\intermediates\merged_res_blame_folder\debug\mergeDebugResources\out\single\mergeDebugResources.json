[{"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/xml_accessibility_service_config.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/xml/accessibility_service_config.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/drawable_ic_stop_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/drawable/ic_stop_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/xml_data_extraction_rules.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/xml/data_extraction_rules.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/drawable_ic_launcher_background.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/drawable/ic_launcher_background.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/xml_file_paths.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/xml/file_paths.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/drawable_ic_mic_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/drawable/ic_mic_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/drawable_ic_launcher_foreground.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/xml_backup_rules.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/xml/backup_rules.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/drawable_ic_error_24.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/drawable/ic_error_24.xml"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.mdmusfikurrahaman.callrecordingapp-debug-66:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.mdmusfikurrahaman.callrecordingapp-main-68:/mipmap-anydpi-v26/ic_launcher.xml"}]