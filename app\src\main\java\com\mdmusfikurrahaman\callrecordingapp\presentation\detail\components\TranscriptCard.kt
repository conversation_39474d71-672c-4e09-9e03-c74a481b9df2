package com.mdmusfikurrahaman.callrecordingapp.presentation.detail.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.TranscriptWithSegments
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.Speaker
import com.mdmusfikurrahaman.callrecordingapp.util.DateTimeUtils

/**
 * Card displaying call transcript with segments
 */
@Composable
fun TranscriptCard(
    transcript: TranscriptWithSegments,
    currentPosition: Long,
    onSeekToSegment: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { isExpanded = !isExpanded },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Subtitles,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(24.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "Transcript",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Icon(
                    imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (isExpanded) "Collapse" else "Expand"
                )
            }
            
            if (isExpanded) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // Transcript metadata
                if (transcript.transcript.confidenceScore != null) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Confidence: ${(transcript.transcript.confidenceScore * 100).toInt()}%",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        transcript.transcript.language?.let { language ->
                            Text(
                                text = "Language: $language",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                }
                
                // Transcript segments or full text
                if (transcript.segments.isNotEmpty()) {
                    TranscriptSegments(
                        segments = transcript.segments,
                        currentPosition = currentPosition,
                        onSeekToSegment = onSeekToSegment
                    )
                } else {
                    // Full transcript text
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Text(
                            text = transcript.transcript.transcriptText,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TranscriptSegments(
    segments: List<com.mdmusfikurrahaman.callrecordingapp.data.database.entity.TranscriptSegment>,
    currentPosition: Long,
    onSeekToSegment: (Float) -> Unit
) {
    LazyColumn(
        modifier = Modifier.heightIn(max = 300.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(segments) { segment ->
            TranscriptSegmentItem(
                segment = segment,
                isCurrentSegment = currentPosition >= segment.startTime && currentPosition <= segment.endTime,
                onSeekToSegment = { onSeekToSegment(segment.startTime.toFloat()) }
            )
        }
    }
}

@Composable
private fun TranscriptSegmentItem(
    segment: com.mdmusfikurrahaman.callrecordingapp.data.database.entity.TranscriptSegment,
    isCurrentSegment: Boolean,
    onSeekToSegment: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSeekToSegment() },
        colors = CardDefaults.cardColors(
            containerColor = if (isCurrentSegment) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // Segment header with speaker and timestamp
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Speaker indicator
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(
                                when (segment.speaker) {
                                    Speaker.USER -> MaterialTheme.colorScheme.primary
                                    Speaker.CALLER -> MaterialTheme.colorScheme.secondary
                                    Speaker.UNKNOWN -> MaterialTheme.colorScheme.outline
                                }
                            )
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = when (segment.speaker) {
                            Speaker.USER -> "You"
                            Speaker.CALLER -> "Caller"
                            Speaker.UNKNOWN -> "Unknown"
                        },
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Medium,
                        color = if (isCurrentSegment) {
                            MaterialTheme.colorScheme.onPrimaryContainer
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
                
                // Timestamp
                Text(
                    text = DateTimeUtils.formatDuration(segment.startTime),
                    style = MaterialTheme.typography.labelSmall,
                    color = if (isCurrentSegment) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // Segment text
            Text(
                text = segment.text,
                style = MaterialTheme.typography.bodyMedium,
                color = if (isCurrentSegment) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
            
            // Confidence score if available
            segment.confidence?.let { confidence ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Confidence: ${(confidence * 100).toInt()}%",
                    style = MaterialTheme.typography.labelSmall,
                    color = if (isCurrentSegment) {
                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    }
                )
            }
        }
    }
}
