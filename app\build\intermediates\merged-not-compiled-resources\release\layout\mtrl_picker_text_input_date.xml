<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 The Android Open Source Project

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mtrl_calendar_text_input_frame"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/mtrl_calendar_text_input_padding_top"
    android:paddingBottom="16dp"
    android:paddingLeft="@dimen/mtrl_calendar_content_padding"
    android:paddingRight="@dimen/mtrl_calendar_content_padding">

  <com.google.android.material.textfield.TextInputLayout
      android:id="@+id/mtrl_picker_text_input_date"
      android:layout_width="match_parent"
      android:layout_height="wrap_content">

    <com.google.android.material.textfield.TextInputEditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/mtrl_picker_text_input_date_hint"
        android:inputType="date"
        android:imeOptions="flagNoExtractUi"/>
  </com.google.android.material.textfield.TextInputLayout>
</FrameLayout>
