package com.mdmusfikurrahaman.callrecordingapp.presentation.settings

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.UserPreferences
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import com.mdmusfikurrahaman.callrecordingapp.permission.PermissionManager
import com.mdmusfikurrahaman.callrecordingapp.util.StorageManager
import com.mdmusfikurrahaman.callrecordingapp.BuildConfig

/**
 * ViewModel for the settings screen
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val repository: CallRecordingRepository,
    private val permissionManager: PermissionManager,
    private val storageManager: StorageManager
) : ViewModel() {
    
    private val _preferences = MutableStateFlow<UserPreferences?>(null)
    private val _permissionStatus = MutableStateFlow<PermissionManager.PermissionStatusReport?>(null)
    private val _storageInfo = MutableStateFlow<StorageManager.StorageInfo?>(null)
    private val _showLegalDisclaimer = MutableStateFlow(false)
    private val _isLoading = MutableStateFlow(false)
    private val _errorMessage = MutableStateFlow<String?>(null)
    
    val uiState = combine(
        _preferences,
        _permissionStatus,
        _storageInfo,
        _showLegalDisclaimer,
        _isLoading,
        _errorMessage
    ) { preferences, permissionStatus, storageInfo, showLegalDisclaimer, isLoading, errorMessage ->
        SettingsUiState(
            preferences = preferences,
            permissionStatus = permissionStatus,
            storageInfo = storageInfo,
            showLegalDisclaimer = showLegalDisclaimer,
            isLoading = isLoading,
            errorMessage = errorMessage,
            appVersion = BuildConfig.VERSION_NAME
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = SettingsUiState()
    )
    
    /**
     * Load settings and initial data
     */
    fun loadSettings() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // Load user preferences
                repository.getUserPreferences().collect { preferences ->
                    _preferences.value = preferences
                }
                
                // Load storage info
                refreshStorageInfo()
                
            } catch (e: Exception) {
                Timber.e(e, "Error loading settings")
                _errorMessage.value = "Error loading settings"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Refresh storage information
     */
    fun refreshStorageInfo() {
        viewModelScope.launch {
            try {
                val storageInfo = storageManager.getStorageInfo()
                _storageInfo.value = storageInfo
            } catch (e: Exception) {
                Timber.e(e, "Error refreshing storage info")
            }
        }
    }
    
    /**
     * Check permission status
     */
    fun checkPermissions(context: Context) {
        viewModelScope.launch {
            try {
                val status = permissionManager.getPermissionStatusReport(context)
                _permissionStatus.value = status
            } catch (e: Exception) {
                Timber.e(e, "Error checking permissions")
            }
        }
    }
    
    /**
     * Request permissions
     */
    fun requestPermissions(context: Context) {
        // This would typically trigger a permission request flow
        // For now, just refresh the permission status
        checkPermissions(context)
    }
    
    /**
     * Open app settings
     */
    fun openAppSettings(context: Context) {
        try {
            val intent = permissionManager.createAppSettingsIntent(context)
            context.startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e, "Error opening app settings")
            _errorMessage.value = "Error opening settings"
        }
    }
    
    // Preference update methods
    
    fun updateAutoRecordEnabled(enabled: Boolean) {
        updatePreferences { it.copy(autoRecordEnabled = enabled) }
    }
    
    fun updateRecordIncomingCalls(enabled: Boolean) {
        updatePreferences { it.copy(recordIncomingCalls = enabled) }
    }
    
    fun updateRecordOutgoingCalls(enabled: Boolean) {
        updatePreferences { it.copy(recordOutgoingCalls = enabled) }
    }
    
    fun updateRecordingQuality(qualityName: String) {
        val quality = when (qualityName) {
            "Low Quality" -> RecordingQuality.LOW
            "Medium Quality" -> RecordingQuality.MEDIUM
            "High Quality" -> RecordingQuality.HIGH
            "Very High Quality" -> RecordingQuality.VERY_HIGH
            else -> RecordingQuality.MEDIUM
        }
        updatePreferences { it.copy(recordingQuality = quality) }
    }
    
    fun updateAutoDeleteEnabled(enabled: Boolean) {
        updatePreferences { it.copy(autoDeleteEnabled = enabled) }
    }
    
    fun updateAutoDeleteDays(days: Int) {
        updatePreferences { it.copy(autoDeleteDays = days) }
    }
    
    fun updateShowConsentDialog(enabled: Boolean) {
        updatePreferences { it.copy(showConsentDialog = enabled) }
    }
    
    fun updateShowRecordingIndicator(enabled: Boolean) {
        updatePreferences { it.copy(showRecordingIndicator = enabled) }
    }
    
    fun updateVibrateOnRecordingStart(enabled: Boolean) {
        updatePreferences { it.copy(vibrateOnRecordingStart = enabled) }
    }
    
    fun updateAiTranscriptionEnabled(enabled: Boolean) {
        updatePreferences { it.copy(aiTranscriptionEnabled = enabled) }
    }
    
    fun updateAutoTranscribe(enabled: Boolean) {
        updatePreferences { it.copy(autoTranscribe = enabled) }
    }
    
    /**
     * Helper method to update preferences
     */
    private fun updatePreferences(update: (UserPreferences) -> UserPreferences) {
        viewModelScope.launch {
            try {
                val currentPreferences = _preferences.value ?: UserPreferences()
                val updatedPreferences = update(currentPreferences).copy(
                    updatedAt = System.currentTimeMillis()
                )
                
                repository.updateUserPreferences(updatedPreferences)
                _preferences.value = updatedPreferences
                
            } catch (e: Exception) {
                Timber.e(e, "Error updating preferences")
                _errorMessage.value = "Error updating settings"
            }
        }
    }
    
    /**
     * Show legal disclaimer dialog
     */
    fun showLegalDisclaimer() {
        _showLegalDisclaimer.value = true
    }
    
    /**
     * Dismiss legal disclaimer dialog
     */
    fun dismissLegalDisclaimer() {
        _showLegalDisclaimer.value = false
    }
    
    /**
     * Accept legal disclaimer
     */
    fun acceptLegalDisclaimer() {
        viewModelScope.launch {
            try {
                repository.acceptLegalDisclaimer()
                _showLegalDisclaimer.value = false
            } catch (e: Exception) {
                Timber.e(e, "Error accepting legal disclaimer")
                _errorMessage.value = "Error saving legal acceptance"
            }
        }
    }
    
    /**
     * Open privacy policy
     */
    fun openPrivacyPolicy(context: Context) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://example.com/privacy-policy"))
            context.startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e, "Error opening privacy policy")
            _errorMessage.value = "Error opening privacy policy"
        }
    }
    
    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}

/**
 * UI state for the settings screen
 */
data class SettingsUiState(
    val preferences: UserPreferences? = null,
    val permissionStatus: PermissionManager.PermissionStatusReport? = null,
    val storageInfo: StorageManager.StorageInfo? = null,
    val showLegalDisclaimer: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val appVersion: String = "1.0.0"
)
