// Generated by view binder compiler. Do not edit!
package com.mdmusfikurrahaman.callrecordingapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.mdmusfikurrahaman.callrecordingapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecordingBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView callTypeIcon;

  @NonNull
  public final TextView contactNameText;

  @NonNull
  public final TextView dateText;

  @NonNull
  public final TextView durationText;

  @NonNull
  public final MaterialButton moreButton;

  @NonNull
  public final TextView phoneNumberText;

  @NonNull
  public final MaterialButton playButton;

  private ItemRecordingBinding(@NonNull MaterialCardView rootView, @NonNull ImageView callTypeIcon,
      @NonNull TextView contactNameText, @NonNull TextView dateText, @NonNull TextView durationText,
      @NonNull MaterialButton moreButton, @NonNull TextView phoneNumberText,
      @NonNull MaterialButton playButton) {
    this.rootView = rootView;
    this.callTypeIcon = callTypeIcon;
    this.contactNameText = contactNameText;
    this.dateText = dateText;
    this.durationText = durationText;
    this.moreButton = moreButton;
    this.phoneNumberText = phoneNumberText;
    this.playButton = playButton;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecordingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecordingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recording, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecordingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.callTypeIcon;
      ImageView callTypeIcon = ViewBindings.findChildViewById(rootView, id);
      if (callTypeIcon == null) {
        break missingId;
      }

      id = R.id.contactNameText;
      TextView contactNameText = ViewBindings.findChildViewById(rootView, id);
      if (contactNameText == null) {
        break missingId;
      }

      id = R.id.dateText;
      TextView dateText = ViewBindings.findChildViewById(rootView, id);
      if (dateText == null) {
        break missingId;
      }

      id = R.id.durationText;
      TextView durationText = ViewBindings.findChildViewById(rootView, id);
      if (durationText == null) {
        break missingId;
      }

      id = R.id.moreButton;
      MaterialButton moreButton = ViewBindings.findChildViewById(rootView, id);
      if (moreButton == null) {
        break missingId;
      }

      id = R.id.phoneNumberText;
      TextView phoneNumberText = ViewBindings.findChildViewById(rootView, id);
      if (phoneNumberText == null) {
        break missingId;
      }

      id = R.id.playButton;
      MaterialButton playButton = ViewBindings.findChildViewById(rootView, id);
      if (playButton == null) {
        break missingId;
      }

      return new ItemRecordingBinding((MaterialCardView) rootView, callTypeIcon, contactNameText,
          dateText, durationText, moreButton, phoneNumberText, playButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
