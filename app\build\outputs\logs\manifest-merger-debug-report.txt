-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:137:9-145:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:141:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:139:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:140:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:138:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
MERGED from [androidx.databinding:databinding-adapters:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12278f89c9c1a6d0266149d8322959d5\transformed\databinding-adapters-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\373881bb763ba8f40077597d8edf58b3\transformed\databinding-ktx-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56172adef7cc12b8e15a5c96b53dfc6d\transformed\databinding-runtime-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b3f4ccc5b47138f911fb1b720850eae\transformed\viewbinding-8.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1242b96d8035efc7f0a4192cf371d3e5\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4def19185d615ed27d3d5d0fe1ec5fd4\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a13d9491f1ed0281a7410749213ecc\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b77400d7b7f49ad7737be6fd94e8a6e9\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f7a790844c605e4d7ad8cce076370b\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f37b319b4dcbff051679aba76f8b692d\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ae340efcbf554f8b310e3624932a8d\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f743c2e5f2a61bc0a20ac96fdd74bd6f\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e5330ea37f1d0a4ed46f4a86900feb4\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\732e256b958928f5597c0b5b5d201eb6\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f318696097e153bba7c8055ba51fc2e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46864e7f8e3972dd5b88a3c5321cb854\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\058997b6231e245395c71a1548e9c9ca\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16b8dac02a484144e9b877600396f6d3\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d199c28c617ca78de992a9836861ec3\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad6060d0fec0ce8b877e62ca1e29f41b\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adb51a7e6b5f714eda15dc4696925b87\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dc7eb056b660fbe2c8ce673b42e4d67\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c35df0f83472824d43979229151684fc\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c7e5664ef39c0a18d3b3ddb9909661\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944d23d0a2a0a4de0e44ec3c7bd88e96\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8be88397ed354ff354a57bb4a1c1ec9\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\965b6545637dcbeb742a4cc30cca84d4\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c855c931b6d64414c7303062caa4ad\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\524490ab245bb45a3b1777ff7ff39b14\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a2a10bc907d55ef20f3d0f4aadac517\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef84f517d9a9703fa5ba9ac9196552c6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1270ea2657d9c27a21e7a30637a34067\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da38c774e011b0f2076c2e0427b9dd67\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\379735225bb6668911f7c8d2a479210c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86e3a4b44989232eb6f64ca9d6d2e763\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\885c8c3cf57db21d5f8375627b374aeb\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6da4f649387e7513bba587ac9a42747\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b68af0e52805af56ab39fbe7aec8d271\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b594ecb4ebd7d1c3f2e94e75b98737e\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\575e1bec40cb225d4501af24db426e2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e23183624f32e549780b0f7ff1957bad\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26e83ea22611f2c3e7a7fb951b84c34d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4ee22aafe8405c9952dfc7edd6eda01\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f92f9af74e3aaf10344f71da6e0e0f0\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c0ba3f0c50f98f88f8c6d4d99e0a11\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea9826dffd13d49fe6f54aa835508540\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfe8595f4c83a5affcce580f6468af53\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5348b409ec7aefb2d3889d9b1681831f\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8058f69873396ca1bdfc08415001e0\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8f95dbee17f48ec614f512b102c9ad\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\444d182b06ec144f713384c1ad49b6d5\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\630e9a1070986358042d0b4c75b21be0\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\298c23db24cf56ae08de2553787fb3d8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d267f1405bf59528e7248fca1be7c58\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bf3aadf8752a0fb4baaed0404fcf951\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f372618c04d0917eddb0b34fc4b7e96\transformed\timber-5.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141494e4442b6d040d54e39a243cdd85\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d5886b5b0a4b3454933e0212d32a84d\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0a6d184abbff2ecdac525e8d1f09511\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad9f80662281f027a567ab49f793a044\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d72aa17fec5b104aefa321b0c57b7ac\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27a11efe7203645011845d5d1ca14ece\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\317586fb7da34e655a48cecd3c589963\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dd993684e14c53f226dbd859d2e610\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\890cd1f5ac7da60f51332e9c5e5ade45\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ee0524d2d452822e3d44f6777bf57aa\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89f363567fc2bd81a815a0e17f54d002\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccd567d3a1e0f78f6b84eab9924e3095\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ff79d35347b18b592e53d695675c0d7\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77279a5d31a9e721ffd1ca5ec923262f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07696f29ac2554cc201734802185718\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dea95eef174924418c90b4347bea055b\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9cd7b16d4075ffafd0f4d426a66395d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16c7bc52f93e8ed6d03a495d0a6039b7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\befd548b489367a83f4f5fe751220172\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57156d9a9615cdef4071ff587f5136c2\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:22-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:5-94
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:22-91
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:5-88
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:22-85
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:5-17:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:5-19:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:19:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:5-29:47
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:29:9-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:22-82
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:22-69
uses-permission#android.permission.PROCESS_OUTGOING_CALLS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:5-34:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:34:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:22-65
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944d23d0a2a0a4de0e44ec3c7bd88e96\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944d23d0a2a0a4de0e44ec3c7bd88e96\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07696f29ac2554cc201734802185718\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07696f29ac2554cc201734802185718\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:22-76
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e5330ea37f1d0a4ed46f4a86900feb4\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e5330ea37f1d0a4ed46f4a86900feb4\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\732e256b958928f5597c0b5b5d201eb6\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\732e256b958928f5597c0b5b5d201eb6\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f372618c04d0917eddb0b34fc4b7e96\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f372618c04d0917eddb0b34fc4b7e96\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d72aa17fec5b104aefa321b0c57b7ac\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d72aa17fec5b104aefa321b0c57b7ac\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:53:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:51:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:49:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:47:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:50:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:54:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:48:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:45:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:52:9-54
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:46:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:44:9-49
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:56:9-66:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:59:13-45
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:61:13-43
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:58:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:60:13-58
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:57:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:62:13-65:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:27-74
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.settings.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:69:9-73:71
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:73:13-68
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:72:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:70:13-67
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.detail.RecordingDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:76:9-80:71
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:80:13-68
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:79:13-54
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:78:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:77:13-72
service#com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:83:9-87:74
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:85:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:86:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:87:13-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:84:13-57
service#com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingAccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:90:9-101:19
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:92:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:93:13-37
	android:permission
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:94:13-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:91:13-70
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:95:13-97:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:17-92
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:98:13-100:72
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:100:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:99:17-60
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.PhoneStateReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:104:9-111:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:106:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:107:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:105:13-56
intent-filter#action:name:android.intent.action.PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:13-110:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:28-51
action#android.intent.action.PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:25-73
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.CallLogReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:114:9-121:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:116:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:117:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:115:13-53
intent-filter#action:name:android.intent.action.NEW_OUTGOING_CALL
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:118:13-120:29
action#android.intent.action.NEW_OUTGOING_CALL
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:17-82
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:25-79
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.BootReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:124:9-134:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:126:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:127:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:125:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:13-133:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:17-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:17-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:17-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:25-78
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:17-50
	android:scheme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:23-47
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:142:13-144:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:144:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:143:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12278f89c9c1a6d0266149d8322959d5\transformed\databinding-adapters-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12278f89c9c1a6d0266149d8322959d5\transformed\databinding-adapters-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\373881bb763ba8f40077597d8edf58b3\transformed\databinding-ktx-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\373881bb763ba8f40077597d8edf58b3\transformed\databinding-ktx-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56172adef7cc12b8e15a5c96b53dfc6d\transformed\databinding-runtime-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56172adef7cc12b8e15a5c96b53dfc6d\transformed\databinding-runtime-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b3f4ccc5b47138f911fb1b720850eae\transformed\viewbinding-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b3f4ccc5b47138f911fb1b720850eae\transformed\viewbinding-8.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1242b96d8035efc7f0a4192cf371d3e5\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1242b96d8035efc7f0a4192cf371d3e5\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4def19185d615ed27d3d5d0fe1ec5fd4\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4def19185d615ed27d3d5d0fe1ec5fd4\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a13d9491f1ed0281a7410749213ecc\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a13d9491f1ed0281a7410749213ecc\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b77400d7b7f49ad7737be6fd94e8a6e9\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b77400d7b7f49ad7737be6fd94e8a6e9\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f7a790844c605e4d7ad8cce076370b\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f7a790844c605e4d7ad8cce076370b\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f37b319b4dcbff051679aba76f8b692d\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f37b319b4dcbff051679aba76f8b692d\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ae340efcbf554f8b310e3624932a8d\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ae340efcbf554f8b310e3624932a8d\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f743c2e5f2a61bc0a20ac96fdd74bd6f\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f743c2e5f2a61bc0a20ac96fdd74bd6f\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e5330ea37f1d0a4ed46f4a86900feb4\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e5330ea37f1d0a4ed46f4a86900feb4\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\732e256b958928f5597c0b5b5d201eb6\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\732e256b958928f5597c0b5b5d201eb6\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f318696097e153bba7c8055ba51fc2e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f318696097e153bba7c8055ba51fc2e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46864e7f8e3972dd5b88a3c5321cb854\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46864e7f8e3972dd5b88a3c5321cb854\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\058997b6231e245395c71a1548e9c9ca\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\058997b6231e245395c71a1548e9c9ca\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16b8dac02a484144e9b877600396f6d3\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16b8dac02a484144e9b877600396f6d3\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d199c28c617ca78de992a9836861ec3\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d199c28c617ca78de992a9836861ec3\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad6060d0fec0ce8b877e62ca1e29f41b\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad6060d0fec0ce8b877e62ca1e29f41b\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adb51a7e6b5f714eda15dc4696925b87\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adb51a7e6b5f714eda15dc4696925b87\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dc7eb056b660fbe2c8ce673b42e4d67\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dc7eb056b660fbe2c8ce673b42e4d67\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c35df0f83472824d43979229151684fc\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c35df0f83472824d43979229151684fc\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c7e5664ef39c0a18d3b3ddb9909661\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c7e5664ef39c0a18d3b3ddb9909661\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944d23d0a2a0a4de0e44ec3c7bd88e96\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944d23d0a2a0a4de0e44ec3c7bd88e96\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8be88397ed354ff354a57bb4a1c1ec9\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8be88397ed354ff354a57bb4a1c1ec9\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\965b6545637dcbeb742a4cc30cca84d4\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\965b6545637dcbeb742a4cc30cca84d4\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c855c931b6d64414c7303062caa4ad\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c855c931b6d64414c7303062caa4ad\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\524490ab245bb45a3b1777ff7ff39b14\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\524490ab245bb45a3b1777ff7ff39b14\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a2a10bc907d55ef20f3d0f4aadac517\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a2a10bc907d55ef20f3d0f4aadac517\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef84f517d9a9703fa5ba9ac9196552c6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef84f517d9a9703fa5ba9ac9196552c6\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1270ea2657d9c27a21e7a30637a34067\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1270ea2657d9c27a21e7a30637a34067\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da38c774e011b0f2076c2e0427b9dd67\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da38c774e011b0f2076c2e0427b9dd67\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\379735225bb6668911f7c8d2a479210c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\379735225bb6668911f7c8d2a479210c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86e3a4b44989232eb6f64ca9d6d2e763\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86e3a4b44989232eb6f64ca9d6d2e763\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\885c8c3cf57db21d5f8375627b374aeb\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\885c8c3cf57db21d5f8375627b374aeb\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6da4f649387e7513bba587ac9a42747\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6da4f649387e7513bba587ac9a42747\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b68af0e52805af56ab39fbe7aec8d271\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b68af0e52805af56ab39fbe7aec8d271\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b594ecb4ebd7d1c3f2e94e75b98737e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b594ecb4ebd7d1c3f2e94e75b98737e\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\575e1bec40cb225d4501af24db426e2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\575e1bec40cb225d4501af24db426e2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e23183624f32e549780b0f7ff1957bad\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e23183624f32e549780b0f7ff1957bad\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26e83ea22611f2c3e7a7fb951b84c34d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26e83ea22611f2c3e7a7fb951b84c34d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4ee22aafe8405c9952dfc7edd6eda01\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4ee22aafe8405c9952dfc7edd6eda01\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f92f9af74e3aaf10344f71da6e0e0f0\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f92f9af74e3aaf10344f71da6e0e0f0\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c0ba3f0c50f98f88f8c6d4d99e0a11\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c0ba3f0c50f98f88f8c6d4d99e0a11\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea9826dffd13d49fe6f54aa835508540\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea9826dffd13d49fe6f54aa835508540\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfe8595f4c83a5affcce580f6468af53\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfe8595f4c83a5affcce580f6468af53\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5348b409ec7aefb2d3889d9b1681831f\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5348b409ec7aefb2d3889d9b1681831f\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8058f69873396ca1bdfc08415001e0\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8058f69873396ca1bdfc08415001e0\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8f95dbee17f48ec614f512b102c9ad\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8f95dbee17f48ec614f512b102c9ad\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\444d182b06ec144f713384c1ad49b6d5\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\444d182b06ec144f713384c1ad49b6d5\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\630e9a1070986358042d0b4c75b21be0\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\630e9a1070986358042d0b4c75b21be0\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\298c23db24cf56ae08de2553787fb3d8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\298c23db24cf56ae08de2553787fb3d8\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d267f1405bf59528e7248fca1be7c58\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d267f1405bf59528e7248fca1be7c58\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bf3aadf8752a0fb4baaed0404fcf951\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bf3aadf8752a0fb4baaed0404fcf951\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f372618c04d0917eddb0b34fc4b7e96\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f372618c04d0917eddb0b34fc4b7e96\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141494e4442b6d040d54e39a243cdd85\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141494e4442b6d040d54e39a243cdd85\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d5886b5b0a4b3454933e0212d32a84d\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d5886b5b0a4b3454933e0212d32a84d\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0a6d184abbff2ecdac525e8d1f09511\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0a6d184abbff2ecdac525e8d1f09511\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad9f80662281f027a567ab49f793a044\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad9f80662281f027a567ab49f793a044\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d72aa17fec5b104aefa321b0c57b7ac\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d72aa17fec5b104aefa321b0c57b7ac\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27a11efe7203645011845d5d1ca14ece\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27a11efe7203645011845d5d1ca14ece\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\317586fb7da34e655a48cecd3c589963\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\317586fb7da34e655a48cecd3c589963\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dd993684e14c53f226dbd859d2e610\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dd993684e14c53f226dbd859d2e610\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\890cd1f5ac7da60f51332e9c5e5ade45\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\890cd1f5ac7da60f51332e9c5e5ade45\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ee0524d2d452822e3d44f6777bf57aa\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ee0524d2d452822e3d44f6777bf57aa\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89f363567fc2bd81a815a0e17f54d002\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89f363567fc2bd81a815a0e17f54d002\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccd567d3a1e0f78f6b84eab9924e3095\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ccd567d3a1e0f78f6b84eab9924e3095\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ff79d35347b18b592e53d695675c0d7\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ff79d35347b18b592e53d695675c0d7\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77279a5d31a9e721ffd1ca5ec923262f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77279a5d31a9e721ffd1ca5ec923262f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07696f29ac2554cc201734802185718\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a07696f29ac2554cc201734802185718\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dea95eef174924418c90b4347bea055b\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dea95eef174924418c90b4347bea055b\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9cd7b16d4075ffafd0f4d426a66395d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9cd7b16d4075ffafd0f4d426a66395d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16c7bc52f93e8ed6d03a495d0a6039b7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16c7bc52f93e8ed6d03a495d0a6039b7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\befd548b489367a83f4f5fe751220172\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\befd548b489367a83f4f5fe751220172\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57156d9a9615cdef4071ff587f5136c2\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57156d9a9615cdef4071ff587f5136c2\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
