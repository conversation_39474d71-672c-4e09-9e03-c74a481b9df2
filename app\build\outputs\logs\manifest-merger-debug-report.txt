-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:137:9-145:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:141:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:139:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:140:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:138:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:1-149:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2297764476b0085eff92b7df0d84df7\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf5072ae2a3dd00a05987e4b9853314d\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811b1ce9077fecb6340e6d12cb4fa636\transformed\navigation-common-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43451c4cf576c4ca58d679413c1217ee\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5de16a5961e9a8a0abfabe68f66733d\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc744377875fb90cf4343be0bd4278b3\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f7942a7da1133b37f2dc2f0a00185d1\transformed\navigation-compose-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b046af37e0655556fbdda244d9f444\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e314a50eb315442fe0afa641180e00d\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9487e3866088520b15ceac3a4c50930f\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1555366647f964f993feaf4048cda5c2\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6752dc6b72dd7bbb78bd362d3e2d21e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87d78cd91b4d183bfa1262f9176c8808\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a56e6f83f69f990f170da7dd43f3f60\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e72416f097544af7c7b4a70ec06ad5f5\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37884121572ee391713c499f6dfa99b6\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d6467f6d4cc5d00a97e1927b19c497b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87745c7ebbabc59fd9697939087eba3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d2fb2a4c50b636d0dd7eb6af7089695\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5e5d8aaa040e560f332430c6f53500\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f34d3ab61b7e874636254012ea57c9bf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fea39264a18d3489500acf842f7188d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\494bfdab15dca442fa2e05e43f86b4ff\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64daf04d4922015e37dc200ad2ff335c\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f73f3a5398eee140153949db3abbd04\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3da52c2d4a55c4972ed19b8bad6382e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ccc667e872bc30fc2eec630895cb499\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f1dae5057588a17f266fe51273be58\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0eeb0b9511ee339dcb6b8b82d5d379aa\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\173274f889ef2d6c00aff2697a76749e\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f0d934159a745b77ce5e2c937655ae7\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f20658b89fc3f01924bca0353d78a86\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f62f63e997d45c40d2117e611c0a5f15\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a43f017882f66653ee8361060ded215\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafb0d57b3d20b66636abeb5c0403428\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4729f1ee5419b552b8b017a3211929f9\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4662a7cbcfa79b803217b6ed865c8a4\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\346f73fdf4db2af5e066d3593cf98121\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060f7bae30cd6eaca84919e959bbcf8e\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a4b479057fe37f7eb3a9066e0f99bd4\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44bd67fe1d0252bc4663d488a85dbd1e\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b42dad7c23b1308a3238d69bb6fed4b\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4767a0eaec58b98c57a7c7084cad69cc\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd6aef088fbfce15a74175d5a5d4fa6\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e18e2e42bcb2da444e71f8d08331114e\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b73d6fe7341ebd4a903e0e62b164ddf\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71ed6f76af2e64363dc91344784afe6\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb744c244286e105597223881c409671\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8b2c05cb8e30343c0d08a9b5ee322bb\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2369d583edc5ede10c1242d0609366fc\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ff7cf87a88e7f0b1294f3bb9ed7df3d\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0caafe874001aa745e69129a07aec6ad\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61fc0dc2a1f6804b5f71693c289df2ca\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1182d5d1bbc868453e478891bcc4e878\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02ea201e9bb3b55675877c0cb9193573\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253abac796458b2537b8cd04c458a32f\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5581cacaac74ad0e05c7f6643910a800\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e7f3b220239d1fe8ecbc13ab6ce9489\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e25f31d033dabb3e70e33f4bda4af09d\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\982b3ba6bae1ed151cdcdc2b94f499af\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f6b081858863bf6ac83d3e56dce9679\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c5167891782365507af10137fff434\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62ec83f1185741b584d6d3f738481309\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ca8b24603cb6f35d2957243908062a\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b980f2eb9d27b8cfd48cb43a893c235\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f38e18cc05604e09bd8dc2b34c49a57\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff937bdf593fc05465a9a8ec24a49e33\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c0f6e75be9e526d1df5e0931da20e85\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92882136cda3072b391319ae7a23b662\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7774a88993d586b7e2d450d455950b3\transformed\timber-5.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7dbe06a0bbe1db7492a30b03996a048\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0be67bf91164c0f46f9865096886e4f6\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec222c748024f46caa1cf84ecf12add\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\749be79bf2ac10b23bb300822828318a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9c0590751f93040ce3245ce08a8b45\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb6f24151fa433ca09df5def6693533d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc51dbf00f202b78ff7276406926514a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43a7cf14442012bd33dea247a134955\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863a09f42ba63cebc36cba53e1e50cf7\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64294e78aea9306fb48377391734e549\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cde0f356e015f59e847fac2e2f58187\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56d87aa1c4d19027828af1dd41c22a3e\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72627298b104f8551eebd253268cda9d\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9730553618874056b8391fdc294be96\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9134822624be0f01699e2836d3df6297\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\031a5cc06e20e9b84f07463cd7ec3451\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26b9e3ea960904b97d62c8c2c4de63a0\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11d4578a0bc0254638765961e63f13ae\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff74d50d4970e628b4992ee8420dec7d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4c8bade989dcfa8b56fdfeb703342bb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15eae166dd19a612ae5a94b143bc37d1\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:5-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:22-72
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:22-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:5-94
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:22-91
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:5-88
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:22-85
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:5-17:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:17:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:5-19:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:19:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:5-29:47
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:29:9-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:22-82
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:22-69
uses-permission#android.permission.PROCESS_OUTGOING_CALLS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:5-34:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:34:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:22-65
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37884121572ee391713c499f6dfa99b6\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37884121572ee391713c499f6dfa99b6\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9730553618874056b8391fdc294be96\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9730553618874056b8391fdc294be96\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:22-76
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e314a50eb315442fe0afa641180e00d\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e314a50eb315442fe0afa641180e00d\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1555366647f964f993feaf4048cda5c2\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1555366647f964f993feaf4048cda5c2\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7774a88993d586b7e2d450d455950b3\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7774a88993d586b7e2d450d455950b3\transformed\timber-5.0.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb6f24151fa433ca09df5def6693533d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb6f24151fa433ca09df5def6693533d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:53:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:51:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:49:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:47:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:50:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:54:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:48:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:45:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:52:9-54
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:46:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:44:9-49
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:56:9-66:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:59:13-45
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:61:13-43
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:58:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:60:13-58
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:57:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:62:13-65:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:27-74
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.settings.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:69:9-73:71
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:73:13-68
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:72:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:70:13-67
activity#com.mdmusfikurrahaman.callrecordingapp.presentation.detail.RecordingDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:76:9-80:71
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:80:13-68
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:79:13-54
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:78:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:77:13-72
service#com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:83:9-87:74
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:85:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:86:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:87:13-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:84:13-57
service#com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingAccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:90:9-101:19
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:92:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:93:13-37
	android:permission
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:94:13-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:91:13-70
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:95:13-97:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:17-92
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:98:13-100:72
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:100:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:99:17-60
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.PhoneStateReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:104:9-111:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:106:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:107:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:105:13-56
intent-filter#action:name:android.intent.action.PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:13-110:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:28-51
action#android.intent.action.PHONE_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:25-73
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.CallLogReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:114:9-121:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:116:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:117:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:115:13-53
intent-filter#action:name:android.intent.action.NEW_OUTGOING_CALL
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:118:13-120:29
action#android.intent.action.NEW_OUTGOING_CALL
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:17-82
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:25-79
receiver#com.mdmusfikurrahaman.callrecordingapp.receiver.BootReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:124:9-134:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:126:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:127:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:125:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:13-133:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:17-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:17-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:17-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:25-78
data
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:17-50
	android:scheme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:23-47
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:142:13-144:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:144:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:143:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2297764476b0085eff92b7df0d84df7\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2297764476b0085eff92b7df0d84df7\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf5072ae2a3dd00a05987e4b9853314d\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf5072ae2a3dd00a05987e4b9853314d\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811b1ce9077fecb6340e6d12cb4fa636\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811b1ce9077fecb6340e6d12cb4fa636\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43451c4cf576c4ca58d679413c1217ee\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43451c4cf576c4ca58d679413c1217ee\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5de16a5961e9a8a0abfabe68f66733d\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5de16a5961e9a8a0abfabe68f66733d\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc744377875fb90cf4343be0bd4278b3\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc744377875fb90cf4343be0bd4278b3\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f7942a7da1133b37f2dc2f0a00185d1\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f7942a7da1133b37f2dc2f0a00185d1\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b046af37e0655556fbdda244d9f444\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96b046af37e0655556fbdda244d9f444\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e314a50eb315442fe0afa641180e00d\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e314a50eb315442fe0afa641180e00d\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9487e3866088520b15ceac3a4c50930f\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9487e3866088520b15ceac3a4c50930f\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1555366647f964f993feaf4048cda5c2\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1555366647f964f993feaf4048cda5c2\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6752dc6b72dd7bbb78bd362d3e2d21e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6752dc6b72dd7bbb78bd362d3e2d21e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87d78cd91b4d183bfa1262f9176c8808\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87d78cd91b4d183bfa1262f9176c8808\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a56e6f83f69f990f170da7dd43f3f60\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a56e6f83f69f990f170da7dd43f3f60\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e72416f097544af7c7b4a70ec06ad5f5\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e72416f097544af7c7b4a70ec06ad5f5\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37884121572ee391713c499f6dfa99b6\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37884121572ee391713c499f6dfa99b6\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d6467f6d4cc5d00a97e1927b19c497b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d6467f6d4cc5d00a97e1927b19c497b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87745c7ebbabc59fd9697939087eba3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87745c7ebbabc59fd9697939087eba3\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d2fb2a4c50b636d0dd7eb6af7089695\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d2fb2a4c50b636d0dd7eb6af7089695\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5e5d8aaa040e560f332430c6f53500\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5e5d8aaa040e560f332430c6f53500\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f34d3ab61b7e874636254012ea57c9bf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f34d3ab61b7e874636254012ea57c9bf\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fea39264a18d3489500acf842f7188d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fea39264a18d3489500acf842f7188d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\494bfdab15dca442fa2e05e43f86b4ff\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\494bfdab15dca442fa2e05e43f86b4ff\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64daf04d4922015e37dc200ad2ff335c\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64daf04d4922015e37dc200ad2ff335c\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f73f3a5398eee140153949db3abbd04\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f73f3a5398eee140153949db3abbd04\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3da52c2d4a55c4972ed19b8bad6382e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3da52c2d4a55c4972ed19b8bad6382e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ccc667e872bc30fc2eec630895cb499\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ccc667e872bc30fc2eec630895cb499\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f1dae5057588a17f266fe51273be58\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95f1dae5057588a17f266fe51273be58\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0eeb0b9511ee339dcb6b8b82d5d379aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0eeb0b9511ee339dcb6b8b82d5d379aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\173274f889ef2d6c00aff2697a76749e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\173274f889ef2d6c00aff2697a76749e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f0d934159a745b77ce5e2c937655ae7\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f0d934159a745b77ce5e2c937655ae7\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f20658b89fc3f01924bca0353d78a86\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f20658b89fc3f01924bca0353d78a86\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f62f63e997d45c40d2117e611c0a5f15\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f62f63e997d45c40d2117e611c0a5f15\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a43f017882f66653ee8361060ded215\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a43f017882f66653ee8361060ded215\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafb0d57b3d20b66636abeb5c0403428\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafb0d57b3d20b66636abeb5c0403428\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4729f1ee5419b552b8b017a3211929f9\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4729f1ee5419b552b8b017a3211929f9\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4662a7cbcfa79b803217b6ed865c8a4\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4662a7cbcfa79b803217b6ed865c8a4\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\346f73fdf4db2af5e066d3593cf98121\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\346f73fdf4db2af5e066d3593cf98121\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060f7bae30cd6eaca84919e959bbcf8e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060f7bae30cd6eaca84919e959bbcf8e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a4b479057fe37f7eb3a9066e0f99bd4\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a4b479057fe37f7eb3a9066e0f99bd4\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44bd67fe1d0252bc4663d488a85dbd1e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44bd67fe1d0252bc4663d488a85dbd1e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b42dad7c23b1308a3238d69bb6fed4b\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b42dad7c23b1308a3238d69bb6fed4b\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4767a0eaec58b98c57a7c7084cad69cc\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4767a0eaec58b98c57a7c7084cad69cc\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd6aef088fbfce15a74175d5a5d4fa6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd6aef088fbfce15a74175d5a5d4fa6\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e18e2e42bcb2da444e71f8d08331114e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e18e2e42bcb2da444e71f8d08331114e\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b73d6fe7341ebd4a903e0e62b164ddf\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b73d6fe7341ebd4a903e0e62b164ddf\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71ed6f76af2e64363dc91344784afe6\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71ed6f76af2e64363dc91344784afe6\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb744c244286e105597223881c409671\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb744c244286e105597223881c409671\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8b2c05cb8e30343c0d08a9b5ee322bb\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8b2c05cb8e30343c0d08a9b5ee322bb\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2369d583edc5ede10c1242d0609366fc\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2369d583edc5ede10c1242d0609366fc\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ff7cf87a88e7f0b1294f3bb9ed7df3d\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ff7cf87a88e7f0b1294f3bb9ed7df3d\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0caafe874001aa745e69129a07aec6ad\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0caafe874001aa745e69129a07aec6ad\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61fc0dc2a1f6804b5f71693c289df2ca\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61fc0dc2a1f6804b5f71693c289df2ca\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1182d5d1bbc868453e478891bcc4e878\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1182d5d1bbc868453e478891bcc4e878\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02ea201e9bb3b55675877c0cb9193573\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02ea201e9bb3b55675877c0cb9193573\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253abac796458b2537b8cd04c458a32f\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253abac796458b2537b8cd04c458a32f\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5581cacaac74ad0e05c7f6643910a800\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5581cacaac74ad0e05c7f6643910a800\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e7f3b220239d1fe8ecbc13ab6ce9489\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e7f3b220239d1fe8ecbc13ab6ce9489\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e25f31d033dabb3e70e33f4bda4af09d\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e25f31d033dabb3e70e33f4bda4af09d\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\982b3ba6bae1ed151cdcdc2b94f499af\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\982b3ba6bae1ed151cdcdc2b94f499af\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f6b081858863bf6ac83d3e56dce9679\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f6b081858863bf6ac83d3e56dce9679\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c5167891782365507af10137fff434\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c5167891782365507af10137fff434\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62ec83f1185741b584d6d3f738481309\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62ec83f1185741b584d6d3f738481309\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ca8b24603cb6f35d2957243908062a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ca8b24603cb6f35d2957243908062a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b980f2eb9d27b8cfd48cb43a893c235\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b980f2eb9d27b8cfd48cb43a893c235\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f38e18cc05604e09bd8dc2b34c49a57\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f38e18cc05604e09bd8dc2b34c49a57\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff937bdf593fc05465a9a8ec24a49e33\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff937bdf593fc05465a9a8ec24a49e33\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c0f6e75be9e526d1df5e0931da20e85\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c0f6e75be9e526d1df5e0931da20e85\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92882136cda3072b391319ae7a23b662\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92882136cda3072b391319ae7a23b662\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7774a88993d586b7e2d450d455950b3\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7774a88993d586b7e2d450d455950b3\transformed\timber-5.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7dbe06a0bbe1db7492a30b03996a048\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7dbe06a0bbe1db7492a30b03996a048\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0be67bf91164c0f46f9865096886e4f6\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0be67bf91164c0f46f9865096886e4f6\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec222c748024f46caa1cf84ecf12add\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec222c748024f46caa1cf84ecf12add\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\749be79bf2ac10b23bb300822828318a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\749be79bf2ac10b23bb300822828318a\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9c0590751f93040ce3245ce08a8b45\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d9c0590751f93040ce3245ce08a8b45\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb6f24151fa433ca09df5def6693533d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb6f24151fa433ca09df5def6693533d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc51dbf00f202b78ff7276406926514a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc51dbf00f202b78ff7276406926514a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43a7cf14442012bd33dea247a134955\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43a7cf14442012bd33dea247a134955\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863a09f42ba63cebc36cba53e1e50cf7\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863a09f42ba63cebc36cba53e1e50cf7\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64294e78aea9306fb48377391734e549\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64294e78aea9306fb48377391734e549\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cde0f356e015f59e847fac2e2f58187\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cde0f356e015f59e847fac2e2f58187\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56d87aa1c4d19027828af1dd41c22a3e\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56d87aa1c4d19027828af1dd41c22a3e\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72627298b104f8551eebd253268cda9d\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72627298b104f8551eebd253268cda9d\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9730553618874056b8391fdc294be96\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e9730553618874056b8391fdc294be96\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9134822624be0f01699e2836d3df6297\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9134822624be0f01699e2836d3df6297\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\031a5cc06e20e9b84f07463cd7ec3451\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\031a5cc06e20e9b84f07463cd7ec3451\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26b9e3ea960904b97d62c8c2c4de63a0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26b9e3ea960904b97d62c8c2c4de63a0\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11d4578a0bc0254638765961e63f13ae\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\11d4578a0bc0254638765961e63f13ae\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff74d50d4970e628b4992ee8420dec7d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff74d50d4970e628b4992ee8420dec7d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4c8bade989dcfa8b56fdfeb703342bb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4c8bade989dcfa8b56fdfeb703342bb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15eae166dd19a612ae5a94b143bc37d1\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15eae166dd19a612ae5a94b143bc37d1\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
