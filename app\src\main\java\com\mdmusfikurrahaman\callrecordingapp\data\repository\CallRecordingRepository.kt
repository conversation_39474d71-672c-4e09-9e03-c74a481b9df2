package com.mdmusfikurrahaman.callrecordingapp.data.repository

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.CallRecordingDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.CallTranscriptDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.UserPreferencesDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallTranscript
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.TranscriptSegment
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.UserPreferences
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.TranscriptWithSegments
import java.io.File

/**
 * Repository for managing call recordings, transcripts, and user preferences
 * 
 * This repository provides a clean API for the UI layer to interact with
 * the database and handles business logic for data operations
 */
@Singleton
class CallRecordingRepository @Inject constructor(
    private val callRecordingDao: CallRecordingDao,
    private val callTranscriptDao: CallTranscriptDao,
    private val userPreferencesDao: UserPreferencesDao
) {
    
    // Call Recording operations
    
    fun getAllRecordings(): Flow<List<CallRecording>> = callRecordingDao.getAllRecordings()
    
    suspend fun getRecordingById(id: Long): CallRecording? = callRecordingDao.getRecordingById(id)
    
    fun getRecordingsByPhoneNumber(phoneNumber: String): Flow<List<CallRecording>> = 
        callRecordingDao.getRecordingsByPhoneNumber(phoneNumber)
    
    fun getRecordingsByCallType(callType: CallType): Flow<List<CallRecording>> = 
        callRecordingDao.getRecordingsByCallType(callType)
    
    fun searchRecordings(query: String): Flow<List<CallRecording>> = 
        callRecordingDao.searchRecordings(query)
    
    fun getRecordingsInDateRange(startTime: Long, endTime: Long): Flow<List<CallRecording>> = 
        callRecordingDao.getRecordingsInDateRange(startTime, endTime)
    
    fun getFavoriteRecordings(): Flow<List<CallRecording>> = callRecordingDao.getFavoriteRecordings()
    
    fun getRecordingsWithTranscripts(): Flow<List<CallRecording>> = 
        callRecordingDao.getRecordingsWithTranscripts()
    
    suspend fun getTotalStorageUsed(): Long = callRecordingDao.getTotalStorageUsed() ?: 0L
    
    suspend fun getRecordingCount(): Int = callRecordingDao.getRecordingCount()
    
    suspend fun insertRecording(recording: CallRecording): Long = 
        callRecordingDao.insertRecording(recording)
    
    suspend fun updateRecording(recording: CallRecording) = callRecordingDao.updateRecording(recording)
    
    suspend fun updateFavoriteStatus(id: Long, isFavorite: Boolean) = 
        callRecordingDao.updateFavoriteStatus(id, isFavorite)
    
    suspend fun updateTranscriptStatus(id: Long, hasTranscript: Boolean) = 
        callRecordingDao.updateTranscriptStatus(id, hasTranscript)
    
    suspend fun deleteRecording(recording: CallRecording) {
        // Delete the physical file first
        try {
            val file = File(recording.filePath)
            if (file.exists()) {
                file.delete()
            }
        } catch (e: Exception) {
            // Log error but continue with database deletion
        }
        
        // Delete from database (this will cascade delete transcripts)
        callRecordingDao.deleteRecording(recording)
    }
    
    suspend fun deleteRecordingById(id: Long) {
        val recording = getRecordingById(id)
        recording?.let { deleteRecording(it) }
    }
    
    /**
     * Clean up old recordings based on user preferences
     */
    suspend fun cleanupOldRecordings() {
        val preferences = getUserPreferences().first() ?: return
        
        if (preferences.autoDeleteEnabled) {
            val cutoffTime = System.currentTimeMillis() - (preferences.autoDeleteDays * 24 * 60 * 60 * 1000L)
            val oldRecordings = callRecordingDao.getRecordingsOlderThan(cutoffTime)
            
            // Delete physical files
            oldRecordings.forEach { recording ->
                try {
                    val file = File(recording.filePath)
                    if (file.exists()) {
                        file.delete()
                    }
                } catch (e: Exception) {
                    // Log error but continue
                }
            }
            
            // Delete from database
            callRecordingDao.deleteRecordingsOlderThan(cutoffTime)
        }
    }
    
    // Transcript operations
    
    suspend fun getTranscriptByRecordingId(recordingId: Long): CallTranscript? = 
        callTranscriptDao.getTranscriptByRecordingId(recordingId)
    
    suspend fun getTranscriptWithSegments(recordingId: Long): TranscriptWithSegments? = 
        callTranscriptDao.getTranscriptWithSegments(recordingId)
    
    fun searchTranscripts(query: String): Flow<List<CallTranscript>> = 
        callTranscriptDao.searchTranscripts(query)
    
    suspend fun insertTranscriptWithSegments(
        transcript: CallTranscript,
        segments: List<TranscriptSegment>
    ): Long {
        val transcriptId = callTranscriptDao.insertTranscriptWithSegments(transcript, segments)
        // Update the recording to indicate it has a transcript
        updateTranscriptStatus(transcript.recordingId, true)
        return transcriptId
    }
    
    suspend fun deleteTranscriptByRecordingId(recordingId: Long) {
        callTranscriptDao.deleteTranscriptByRecordingId(recordingId)
        updateTranscriptStatus(recordingId, false)
    }
    
    // User Preferences operations
    
    fun getUserPreferences(): Flow<UserPreferences?> = userPreferencesDao.getUserPreferences()
    
    suspend fun getUserPreferencesSync(): UserPreferences? = userPreferencesDao.getUserPreferencesSync()
    
    suspend fun updateUserPreferences(preferences: UserPreferences) = 
        userPreferencesDao.insertOrUpdatePreferences(preferences)
    
    suspend fun acceptLegalDisclaimer() = userPreferencesDao.acceptLegalDisclaimer()
    
    suspend fun acceptPrivacyPolicy() = userPreferencesDao.acceptPrivacyPolicy()
    
    /**
     * Initialize default preferences if they don't exist
     */
    suspend fun initializeDefaultPreferences() {
        if (getUserPreferencesSync() == null) {
            updateUserPreferences(UserPreferences())
        }
    }
}
