package com.mdmusfikurrahaman.callrecordingapp.data.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallTranscript
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.TranscriptSegment

/**
 * Data Access Object for CallTranscript and TranscriptSegment entities
 * 
 * Provides methods to interact with call transcripts and their segments
 */
@Dao
interface CallTranscriptDao {
    
    // CallTranscript operations
    
    /**
     * Get transcript for a specific recording
     */
    @Query("SELECT * FROM call_transcripts WHERE recording_id = :recordingId")
    suspend fun getTranscriptByRecordingId(recordingId: Long): CallTranscript?
    
    /**
     * Get transcript by ID
     */
    @Query("SELECT * FROM call_transcripts WHERE id = :id")
    suspend fun getTranscriptById(id: Long): CallTranscript?
    
    /**
     * Get all transcripts
     */
    @Query("SELECT * FROM call_transcripts ORDER BY created_at DESC")
    fun getAllTranscripts(): Flow<List<CallTranscript>>
    
    /**
     * Search transcripts by text content
     */
    @Query("""
        SELECT * FROM call_transcripts 
        WHERE transcript_text LIKE '%' || :query || '%'
        ORDER BY created_at DESC
    """)
    fun searchTranscripts(query: String): Flow<List<CallTranscript>>
    
    /**
     * Insert a new transcript
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTranscript(transcript: CallTranscript): Long
    
    /**
     * Update transcript
     */
    @Update
    suspend fun updateTranscript(transcript: CallTranscript)
    
    /**
     * Delete transcript
     */
    @Delete
    suspend fun deleteTranscript(transcript: CallTranscript)
    
    /**
     * Delete transcript by recording ID
     */
    @Query("DELETE FROM call_transcripts WHERE recording_id = :recordingId")
    suspend fun deleteTranscriptByRecordingId(recordingId: Long)
    
    // TranscriptSegment operations
    
    /**
     * Get all segments for a transcript
     */
    @Query("SELECT * FROM transcript_segments WHERE transcript_id = :transcriptId ORDER BY start_time ASC")
    suspend fun getSegmentsByTranscriptId(transcriptId: Long): List<TranscriptSegment>
    
    /**
     * Get segments within a time range
     */
    @Query("""
        SELECT * FROM transcript_segments 
        WHERE transcript_id = :transcriptId 
        AND start_time >= :startTime 
        AND end_time <= :endTime
        ORDER BY start_time ASC
    """)
    suspend fun getSegmentsInTimeRange(
        transcriptId: Long, 
        startTime: Long, 
        endTime: Long
    ): List<TranscriptSegment>
    
    /**
     * Insert transcript segments
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSegments(segments: List<TranscriptSegment>)
    
    /**
     * Delete segments by transcript ID
     */
    @Query("DELETE FROM transcript_segments WHERE transcript_id = :transcriptId")
    suspend fun deleteSegmentsByTranscriptId(transcriptId: Long)
    
    // Combined operations
    
    /**
     * Get transcript with segments
     */
    @Transaction
    suspend fun getTranscriptWithSegments(recordingId: Long): TranscriptWithSegments? {
        val transcript = getTranscriptByRecordingId(recordingId) ?: return null
        val segments = getSegmentsByTranscriptId(transcript.id)
        return TranscriptWithSegments(transcript, segments)
    }
    
    /**
     * Insert transcript with segments
     */
    @Transaction
    suspend fun insertTranscriptWithSegments(
        transcript: CallTranscript,
        segments: List<TranscriptSegment>
    ): Long {
        val transcriptId = insertTranscript(transcript)
        val segmentsWithId = segments.map { it.copy(transcriptId = transcriptId) }
        insertSegments(segmentsWithId)
        return transcriptId
    }
    
    /**
     * Delete transcript with all segments
     */
    @Transaction
    suspend fun deleteTranscriptWithSegments(transcriptId: Long) {
        deleteSegmentsByTranscriptId(transcriptId)
        // Transcript will be deleted by foreign key cascade
    }
}

/**
 * Data class representing a transcript with its segments
 */
data class TranscriptWithSegments(
    val transcript: CallTranscript,
    val segments: List<TranscriptSegment>
)
