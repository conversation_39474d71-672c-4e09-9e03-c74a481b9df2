package com.mdmusfikurrahaman.callrecordingapp.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.PowerManager
import androidx.core.app.NotificationCompat
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject
import com.mdmusfikurrahaman.callrecordingapp.R
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import com.mdmusfikurrahaman.callrecordingapp.service.recording.RecordingMethodManager
import com.mdmusfikurrahaman.callrecordingapp.service.recording.RecordingResult
import com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity
import com.mdmusfikurrahaman.callrecordingapp.util.FileManager
import java.io.File

/**
 * Foreground service responsible for recording phone calls
 * 
 * This service:
 * - Runs in the foreground with a persistent notification
 * - Manages the recording lifecycle
 * - Handles different recording methods
 * - Saves recording metadata to the database
 * - Provides crash recovery and restart capabilities
 */
@AndroidEntryPoint
class CallRecordingService : Service() {
    
    @Inject
    lateinit var repository: CallRecordingRepository
    
    @Inject
    lateinit var recordingMethodManager: RecordingMethodManager
    
    @Inject
    lateinit var fileManager: FileManager
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private var wakeLock: PowerManager.WakeLock? = null
    private var currentRecordingJob: Job? = null
    private var currentRecordingInfo: RecordingInfo? = null
    
    companion object {
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "call_recording_channel"
        
        // Actions
        const val ACTION_START_RECORDING = "start_recording"
        const val ACTION_STOP_RECORDING = "stop_recording"
        const val ACTION_STOP_SERVICE = "stop_service"
        
        // Extras
        const val EXTRA_PHONE_NUMBER = "phone_number"
        const val EXTRA_CONTACT_NAME = "contact_name"
        const val EXTRA_CALL_TYPE = "call_type"
        
        fun startRecording(
            context: Context,
            phoneNumber: String,
            contactName: String?,
            callType: CallType
        ) {
            val intent = Intent(context, CallRecordingService::class.java).apply {
                action = ACTION_START_RECORDING
                putExtra(EXTRA_PHONE_NUMBER, phoneNumber)
                putExtra(EXTRA_CONTACT_NAME, contactName)
                putExtra(EXTRA_CALL_TYPE, callType.name)
            }
            context.startForegroundService(intent)
        }
        
        fun stopRecording(context: Context) {
            val intent = Intent(context, CallRecordingService::class.java).apply {
                action = ACTION_STOP_RECORDING
            }
            context.startService(intent)
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("CallRecordingService created")
        createNotificationChannel()
        acquireWakeLock()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("CallRecordingService onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_RECORDING -> {
                val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER) ?: ""
                val contactName = intent.getStringExtra(EXTRA_CONTACT_NAME)
                val callTypeString = intent.getStringExtra(EXTRA_CALL_TYPE) ?: CallType.INCOMING.name
                val callType = try {
                    CallType.valueOf(callTypeString)
                } catch (e: IllegalArgumentException) {
                    CallType.INCOMING
                }
                
                startRecording(phoneNumber, contactName, callType)
            }
            ACTION_STOP_RECORDING -> {
                stopRecording()
            }
            ACTION_STOP_SERVICE -> {
                stopSelf()
            }
        }
        
        // Return START_STICKY to restart the service if it's killed
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Timber.d("CallRecordingService destroyed")
        
        // Stop any ongoing recording
        currentRecordingJob?.cancel()
        serviceScope.launch {
            recordingMethodManager.cleanup()
        }
        
        // Cancel the service scope
        serviceScope.cancel()
        
        // Release wake lock
        releaseWakeLock()
    }
    
    private fun startRecording(phoneNumber: String, contactName: String?, callType: CallType) {
        if (recordingMethodManager.isRecording()) {
            Timber.w("Recording already in progress")
            return
        }
        
        currentRecordingJob = serviceScope.launch {
            try {
                val preferences = repository.getUserPreferencesSync()
                val quality = preferences?.recordingQuality ?: RecordingQuality.MEDIUM
                
                // Create output file
                val outputFile = fileManager.createRecordingFile(phoneNumber, callType)
                
                // Start foreground notification
                startForeground(NOTIFICATION_ID, createRecordingNotification(phoneNumber, contactName))
                
                // Store recording info
                currentRecordingInfo = RecordingInfo(
                    phoneNumber = phoneNumber,
                    contactName = contactName,
                    callType = callType,
                    outputFile = outputFile,
                    startTime = System.currentTimeMillis()
                )
                
                // Start recording
                val result = recordingMethodManager.startRecording(outputFile, quality, phoneNumber)
                
                if (result.success) {
                    Timber.d("Recording started successfully")
                    updateNotification(phoneNumber, contactName, true)
                } else {
                    Timber.e("Failed to start recording: ${result.error}")
                    handleRecordingError(result.error ?: "Unknown error")
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error starting recording")
                handleRecordingError(e.message ?: "Unknown error")
            }
        }
    }
    
    private fun stopRecording() {
        currentRecordingJob?.cancel()
        currentRecordingJob = serviceScope.launch {
            try {
                val result = recordingMethodManager.stopRecording()
                val recordingInfo = currentRecordingInfo
                
                if (result.success && recordingInfo != null) {
                    // Save recording to database
                    saveRecordingToDatabase(recordingInfo, result)
                    Timber.d("Recording stopped and saved successfully")
                } else {
                    Timber.e("Failed to stop recording: ${result.error}")
                }
                
                // Stop foreground service
                stopForeground(true)
                stopSelf()
                
            } catch (e: Exception) {
                Timber.e(e, "Error stopping recording")
                stopForeground(true)
                stopSelf()
            } finally {
                currentRecordingInfo = null
            }
        }
    }
    
    private suspend fun saveRecordingToDatabase(recordingInfo: RecordingInfo, result: RecordingResult) {
        try {
            val fileSize = recordingInfo.outputFile.length()
            val duration = result.duration
            
            val callRecording = CallRecording(
                phoneNumber = recordingInfo.phoneNumber,
                contactName = recordingInfo.contactName,
                callType = recordingInfo.callType,
                timestamp = recordingInfo.startTime,
                duration = duration,
                filePath = recordingInfo.outputFile.absolutePath,
                fileSize = fileSize,
                recordingQuality = repository.getUserPreferencesSync()?.recordingQuality ?: RecordingQuality.MEDIUM
            )
            
            repository.insertRecording(callRecording)
            Timber.d("Recording saved to database: ${recordingInfo.outputFile.name}")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to save recording to database")
        }
    }
    
    private fun handleRecordingError(error: String) {
        Timber.e("Recording error: $error")
        // Update notification to show error
        updateNotification("Error", null, false)
        
        // Stop service after a delay
        serviceScope.launch {
            delay(3000)
            stopForeground(true)
            stopSelf()
        }
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.recording_notification_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.recording_notification_channel_description)
                setSound(null, null)
                enableVibration(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createRecordingNotification(phoneNumber: String, contactName: String?): Notification {
        val displayName = contactName ?: phoneNumber
        
        val mainIntent = Intent(this, MainActivity::class.java)
        val mainPendingIntent = PendingIntent.getActivity(
            this, 0, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val stopIntent = Intent(this, CallRecordingService::class.java).apply {
            action = ACTION_STOP_RECORDING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 1, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.recording_notification_title))
            .setContentText("Recording call with $displayName")
            .setSmallIcon(R.drawable.ic_mic_24)
            .setContentIntent(mainPendingIntent)
            .addAction(R.drawable.ic_stop_24, "Stop", stopPendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }
    
    private fun updateNotification(phoneNumber: String, contactName: String?, isRecording: Boolean) {
        val notification = if (isRecording) {
            createRecordingNotification(phoneNumber, contactName)
        } else {
            createErrorNotification()
        }
        
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    private fun createErrorNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Recording Error")
            .setContentText("Failed to record call")
            .setSmallIcon(R.drawable.ic_error_24)
            .setSilent(true)
            .build()
    }
    
    private fun acquireWakeLock() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "CallRecording::RecordingWakeLock"
        )
        wakeLock?.acquire(10 * 60 * 1000L) // 10 minutes max
    }
    
    private fun releaseWakeLock() {
        wakeLock?.let { wl ->
            if (wl.isHeld) {
                wl.release()
            }
        }
        wakeLock = null
    }
    
    private data class RecordingInfo(
        val phoneNumber: String,
        val contactName: String?,
        val callType: CallType,
        val outputFile: File,
        val startTime: Long
    )
}
