package com.mdmusfikurrahaman.callrecordingapp.util

import android.content.Context
import android.os.Environment
import dagger.hilt.android.qualifiers.ApplicationContext
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType

/**
 * Utility class for managing recording files and storage
 * 
 * This class handles:
 * - Creating recording files with proper naming conventions
 * - Managing storage directories
 * - Checking available storage space
 * - Cleaning up temporary files
 */
@Singleton
class FileManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val RECORDINGS_DIR = "recordings"
        private const val TEMP_DIR = "temp"
        private const val FILE_EXTENSION = ".m4a"
        private const val MIN_FREE_SPACE_MB = 100L // Minimum free space required in MB
    }
    
    private val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
    
    /**
     * Get the recordings directory, creating it if it doesn't exist
     */
    fun getRecordingsDirectory(): File {
        val recordingsDir = File(context.getExternalFilesDir(null), RECORDINGS_DIR)
        if (!recordingsDir.exists()) {
            recordingsDir.mkdirs()
        }
        return recordingsDir
    }
    
    /**
     * Get the temporary directory for recording files
     */
    fun getTempDirectory(): File {
        val tempDir = File(context.getExternalFilesDir(null), TEMP_DIR)
        if (!tempDir.exists()) {
            tempDir.mkdirs()
        }
        return tempDir
    }
    
    /**
     * Create a new recording file with proper naming convention
     * Format: call_[incoming|outgoing]_[phone_number]_[timestamp].m4a
     */
    fun createRecordingFile(phoneNumber: String, callType: CallType): File {
        val timestamp = dateFormat.format(Date())
        val sanitizedPhoneNumber = sanitizePhoneNumber(phoneNumber)
        val callTypeString = callType.name.lowercase()
        
        val fileName = "call_${callTypeString}_${sanitizedPhoneNumber}_${timestamp}$FILE_EXTENSION"
        
        return File(getRecordingsDirectory(), fileName)
    }
    
    /**
     * Create a temporary recording file
     */
    fun createTempRecordingFile(): File {
        val timestamp = System.currentTimeMillis()
        val fileName = "temp_recording_${timestamp}$FILE_EXTENSION"
        return File(getTempDirectory(), fileName)
    }
    
    /**
     * Move a temporary file to the recordings directory with proper naming
     */
    fun moveTempFileToRecordings(
        tempFile: File,
        phoneNumber: String,
        callType: CallType
    ): File? {
        return try {
            val finalFile = createRecordingFile(phoneNumber, callType)
            
            if (tempFile.renameTo(finalFile)) {
                Timber.d("Moved temp file to: ${finalFile.absolutePath}")
                finalFile
            } else {
                // If rename fails, try copy and delete
                tempFile.copyTo(finalFile, overwrite = true)
                tempFile.delete()
                finalFile
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to move temp file to recordings directory")
            null
        }
    }
    
    /**
     * Check if there's enough storage space for recording
     */
    fun hasEnoughStorageSpace(): Boolean {
        return try {
            val recordingsDir = getRecordingsDirectory()
            val freeSpaceBytes = recordingsDir.freeSpace
            val freeSpaceMB = freeSpaceBytes / (1024 * 1024)
            
            freeSpaceMB >= MIN_FREE_SPACE_MB
        } catch (e: Exception) {
            Timber.e(e, "Error checking storage space")
            false
        }
    }
    
    /**
     * Get available storage space in MB
     */
    fun getAvailableStorageSpaceMB(): Long {
        return try {
            val recordingsDir = getRecordingsDirectory()
            recordingsDir.freeSpace / (1024 * 1024)
        } catch (e: Exception) {
            Timber.e(e, "Error getting available storage space")
            0L
        }
    }
    
    /**
     * Get total storage used by recordings in MB
     */
    fun getTotalStorageUsedMB(): Long {
        return try {
            val recordingsDir = getRecordingsDirectory()
            var totalSize = 0L
            
            recordingsDir.listFiles()?.forEach { file ->
                if (file.isFile && file.name.endsWith(FILE_EXTENSION)) {
                    totalSize += file.length()
                }
            }
            
            totalSize / (1024 * 1024)
        } catch (e: Exception) {
            Timber.e(e, "Error calculating total storage used")
            0L
        }
    }
    
    /**
     * Clean up temporary files older than specified time
     */
    fun cleanupTempFiles(olderThanMillis: Long = 24 * 60 * 60 * 1000L) { // Default: 24 hours
        try {
            val tempDir = getTempDirectory()
            val cutoffTime = System.currentTimeMillis() - olderThanMillis
            
            tempDir.listFiles()?.forEach { file ->
                if (file.isFile && file.lastModified() < cutoffTime) {
                    if (file.delete()) {
                        Timber.d("Deleted temp file: ${file.name}")
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error cleaning up temp files")
        }
    }
    
    /**
     * Delete a recording file
     */
    fun deleteRecordingFile(filePath: String): Boolean {
        return try {
            val file = File(filePath)
            if (file.exists() && file.delete()) {
                Timber.d("Deleted recording file: ${file.name}")
                true
            } else {
                Timber.w("Failed to delete recording file: $filePath")
                false
            }
        } catch (e: Exception) {
            Timber.e(e, "Error deleting recording file: $filePath")
            false
        }
    }
    
    /**
     * Check if a recording file exists
     */
    fun recordingFileExists(filePath: String): Boolean {
        return try {
            File(filePath).exists()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get file size in bytes
     */
    fun getFileSize(filePath: String): Long {
        return try {
            File(filePath).length()
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * Sanitize phone number for use in file names
     */
    private fun sanitizePhoneNumber(phoneNumber: String): String {
        return phoneNumber
            .replace("+", "plus")
            .replace("-", "")
            .replace(" ", "")
            .replace("(", "")
            .replace(")", "")
            .take(15) // Limit length
            .ifEmpty { "unknown" }
    }
    
    /**
     * Get a human-readable file size string
     */
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "${bytes / 1024} KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
            else -> "${bytes / (1024 * 1024 * 1024)} GB"
        }
    }
    
    /**
     * Check if external storage is available and writable
     */
    fun isExternalStorageWritable(): Boolean {
        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
    }
}
