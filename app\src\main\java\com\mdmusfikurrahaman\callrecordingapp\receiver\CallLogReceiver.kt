package com.mdmusfikurrahaman.callrecordingapp.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import timber.log.Timber
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType
import com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService
import com.mdmusfikurrahaman.callrecordingapp.util.ContactUtils
import com.mdmusfikurrahaman.callrecordingapp.util.CallStateManager

/**
 * BroadcastReceiver for detecting outgoing calls
 * 
 * This receiver listens for NEW_OUTGOING_CALL broadcasts to detect
 * when the user initiates an outgoing call.
 * 
 * Note: NEW_OUTGOING_CALL is deprecated in Android 10+ and requires
 * special permissions. This receiver is mainly for older Android versions.
 */
class CallLogReceiver : BroadcastReceiver() {
    
    private val receiverScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            when (intent.action) {
                Intent.ACTION_NEW_OUTGOING_CALL -> {
                    if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.P) {
                        handleOutgoingCall(context, intent)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error in CallLogReceiver")
        }
    }
    
    private fun handleOutgoingCall(context: Context, intent: Intent) {
        val phoneNumber = intent.getStringExtra(Intent.EXTRA_PHONE_NUMBER)
        
        if (phoneNumber.isNullOrBlank()) {
            Timber.w("No phone number in outgoing call intent")
            return
        }
        
        receiverScope.launch {
            try {
                Timber.d("Outgoing call detected: $phoneNumber")
                
                // Store outgoing call information
                CallStateManager.setOutgoingCall(phoneNumber)
                
                // Get contact name
                val contactName = ContactUtils.getContactName(context, phoneNumber)
                
                // Check if recording is enabled for outgoing calls
                if (shouldRecordOutgoingCall(context)) {
                    // Note: We don't start recording immediately for outgoing calls
                    // because the call hasn't connected yet. The PhoneStateReceiver
                    // will handle starting the recording when the call goes OFFHOOK.
                    Timber.d("Outgoing call will be recorded when connected")
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error handling outgoing call")
            }
        }
    }
    
    private suspend fun shouldRecordOutgoingCall(context: Context): Boolean {
        return try {
            // This would check user preferences from the repository
            // For now, return true to enable recording
            true
        } catch (e: Exception) {
            Timber.e(e, "Error checking outgoing call recording preferences")
            false
        }
    }
}
