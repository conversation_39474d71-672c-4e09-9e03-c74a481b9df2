package com.mdmusfikurrahaman.callrecordingapp.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ColumnInfo
import androidx.room.Index

/**
 * Entity representing a call recording in the database
 * 
 * This entity stores metadata about call recordings including:
 * - Call information (phone number, contact name, type)
 * - Recording details (file path, duration, size)
 * - Timestamps and status information
 */
@Entity(
    tableName = "call_recordings",
    indices = [
        Index(value = ["phone_number"]),
        Index(value = ["timestamp"]),
        Index(value = ["call_type"])
    ]
)
data class CallRecording(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "phone_number")
    val phoneNumber: String,
    
    @ColumnInfo(name = "contact_name")
    val contactName: String? = null,
    
    @ColumnInfo(name = "call_type")
    val callType: CallType,
    
    @ColumnInfo(name = "timestamp")
    val timestamp: Long, // Unix timestamp when call started
    
    @ColumnInfo(name = "duration")
    val duration: Long, // Duration in milliseconds
    
    @ColumnInfo(name = "file_path")
    val filePath: String,
    
    @ColumnInfo(name = "file_size")
    val fileSize: Long, // File size in bytes
    
    @ColumnInfo(name = "recording_quality")
    val recordingQuality: RecordingQuality,
    
    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean = false,
    
    @ColumnInfo(name = "has_transcript")
    val hasTranscript: Boolean = false,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * Enum representing the type of call (incoming or outgoing)
 */
enum class CallType {
    INCOMING,
    OUTGOING
}

/**
 * Enum representing the recording quality settings
 */
enum class RecordingQuality(val displayName: String, val bitrate: Int) {
    LOW("Low Quality", 32000),
    MEDIUM("Medium Quality", 64000),
    HIGH("High Quality", 128000),
    VERY_HIGH("Very High Quality", 256000)
}
