package com.mdmusfikurrahaman.callrecordingapp.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallTranscript
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.TranscriptSegment
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.UserPreferences
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.CallRecordingDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.CallTranscriptDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.UserPreferencesDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.converter.DatabaseConverters

/**
 * Room database for the Call Recording App
 * 
 * This database stores:
 * - Call recordings metadata
 * - AI-generated transcripts
 * - User preferences and settings
 * 
 * Version 1: Initial database schema
 */
@Database(
    entities = [
        CallRecording::class,
        CallTranscript::class,
        TranscriptSegment::class,
        UserPreferences::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(DatabaseConverters::class)
abstract class CallRecordingDatabase : RoomDatabase() {
    
    abstract fun callRecordingDao(): CallRecordingDao
    abstract fun callTranscriptDao(): CallTranscriptDao
    abstract fun userPreferencesDao(): UserPreferencesDao
    
    companion object {
        const val DATABASE_NAME = "call_recordings.db"
        
        @Volatile
        private var INSTANCE: CallRecordingDatabase? = null
        
        fun getDatabase(context: Context): CallRecordingDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CallRecordingDatabase::class.java,
                    DATABASE_NAME
                )
                    .addMigrations(
                        // Add future migrations here
                    )
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * Database callback to initialize default data
         */
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // Initialize default user preferences
                db.execSQL("""
                    INSERT INTO user_preferences (
                        id, recording_quality, auto_record_enabled, 
                        record_incoming_calls, record_outgoing_calls,
                        auto_delete_enabled, auto_delete_days, max_storage_mb,
                        show_consent_dialog, legal_disclaimer_accepted, privacy_policy_accepted,
                        ai_transcription_enabled, auto_transcribe,
                        theme_mode, show_recording_indicator, vibrate_on_recording_start,
                        show_recording_notifications, notification_sound_enabled,
                        backup_enabled, backup_wifi_only,
                        created_at, updated_at
                    ) VALUES (
                        1, 'MEDIUM', 1,
                        1, 1,
                        0, 30, 1000,
                        1, 0, 0,
                        0, 0,
                        'SYSTEM', 1, 1,
                        1, 0,
                        0, 1,
                        ${System.currentTimeMillis()}, ${System.currentTimeMillis()}
                    )
                """.trimIndent())
            }
        }
        
        // Future migration examples:
        
        /*
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Example: Add new column to call_recordings table
                database.execSQL("ALTER TABLE call_recordings ADD COLUMN new_column TEXT")
            }
        }
        
        val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Example: Create new table
                database.execSQL("""
                    CREATE TABLE new_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        name TEXT NOT NULL,
                        created_at INTEGER NOT NULL
                    )
                """.trimIndent())
            }
        }
        */
    }
}
