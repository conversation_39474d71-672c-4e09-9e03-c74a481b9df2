<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.mdmusfikurrahaman.callrecordingapp.debug"
    android:versionCode="1"
    android:versionName="1.0-debug" >

    <uses-sdk
        android:minSdkVersion="25"
        android:targetSdkVersion="35" />

    <!-- Core recording permissions -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />

    <!-- Service permissions -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />

    <!-- Storage permissions -->
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- Notification permission for Android 13+ -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- MediaProjection for screen/audio capture on Android 10+ -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- Accessibility service permission -->
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />

    <!-- Phone state and call management -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission
        android:name="android.permission.PROCESS_OUTGOING_CALLS"
        android:maxSdkVersion="28" />

    <!-- Wake lock for background recording -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Internet for AI transcription (optional) -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <permission
        android:name="com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.mdmusfikurrahaman.callrecordingapp.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.mdmusfikurrahaman.callrecordingapp.CallRecordingApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.CallRecordingApp" >

        <!-- Main Activity -->
        <activity
            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:theme="@style/Theme.CallRecordingApp" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Settings Activity -->
        <activity
            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.settings.SettingsActivity"
            android:exported="false"
            android:label="@string/settings"
            android:parentActivityName="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity" />

        <!-- Recording Detail Activity -->
        <activity
            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.detail.RecordingDetailActivity"
            android:exported="false"
            android:label="@string/recording_details"
            android:parentActivityName="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity" />

        <!-- Call Recording Service -->
        <service
            android:name="com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection|microphone" />

        <!-- Accessibility Service for fallback recording -->
        <service
            android:name="com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingAccessibilityService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- Phone State Receiver -->
        <receiver
            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.PhoneStateReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.PHONE_STATE" />
            </intent-filter>
        </receiver>

        <!-- Call Log Receiver -->
        <receiver
            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.CallLogReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
            </intent-filter>
        </receiver>

        <!-- Boot Receiver to restart service after reboot -->
        <receiver
            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.BootReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- File Provider for sharing recordings -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.mdmusfikurrahaman.callrecordingapp.debug.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.mdmusfikurrahaman.callrecordingapp.debug.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>