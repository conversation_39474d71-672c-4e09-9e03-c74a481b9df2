{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeReleaseResources-59:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5e1e4d034409f719670a3d1d52262c91\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1320,1432,1534,1642,1729,1832,1951,2032,2110,2202,2296,2391,2485,2580,2674,2770,2870,2962,3054,3138,3246,3354,3454,3567,3675,3780,3960,14468", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "1427,1529,1637,1724,1827,1946,2027,2105,2197,2291,2386,2480,2575,2669,2765,2865,2957,3049,3133,3241,3349,3449,3562,3670,3775,3955,4055,14547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\125614bd4c72f93cfeffaa0b5947d611\\transformed\\navigation-ui-2.7.6\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,120", "endOffsets": "158,279"}, "to": {"startLines": "181,182", "startColumns": "4,4", "startOffsets": "14162,14270", "endColumns": "107,120", "endOffsets": "14265,14386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\663fb4dbb7e440a36499eafaa6fbda5d\\transformed\\core-1.16.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "61,62,63,64,65,66,67,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4515,4612,4714,4812,4916,5019,5121,14552", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "4607,4709,4807,4911,5014,5116,5233,14648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7473b31c75d2d339c0da8a1a3f9146d6\\transformed\\material-1.11.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1204,1298,1374,1437,1549,1609,1674,1728,1798,1858,1914,2026,2083,2145,2201,2274,2408,2493,2578,2721,2805,2888,3022,3111,3188,3244,3299,3365,3438,3515,3599,3678,3752,3828,3903,3976,4081,4169,4242,4332,4423,4495,4569,4660,4712,4794,4861,4945,5032,5094,5158,5221,5290,5393,5501,5599,5703,5763,5822", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,133,88,76,55,54,65,72,76,83,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1199,1293,1369,1432,1544,1604,1669,1723,1793,1853,1909,2021,2078,2140,2196,2269,2403,2488,2573,2716,2800,2883,3017,3106,3183,3239,3294,3360,3433,3510,3594,3673,3747,3823,3898,3971,4076,4164,4237,4327,4418,4490,4564,4655,4707,4789,4856,4940,5027,5089,5153,5216,5285,5388,5496,5594,5698,5758,5817,5894"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,71,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1053,4060,4149,4238,4326,4424,5238,5344,5470,5554,9544,9638,9714,9777,9889,9949,10014,10068,10138,10198,10254,10366,10423,10485,10541,10614,10748,10833,10918,11061,11145,11228,11362,11451,11528,11584,11639,11705,11778,11855,11939,12018,12092,12168,12243,12316,12421,12509,12582,12672,12763,12835,12909,13000,13052,13134,13201,13285,13372,13434,13498,13561,13630,13733,13841,13939,14043,14103,14391", "endLines": "28,56,57,58,59,60,68,69,70,71,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,183", "endColumns": "12,88,88,87,97,90,105,125,83,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,84,142,83,82,133,88,76,55,54,65,72,76,83,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76", "endOffsets": "1315,4144,4233,4321,4419,4510,5339,5465,5549,5615,9633,9709,9772,9884,9944,10009,10063,10133,10193,10249,10361,10418,10480,10536,10609,10743,10828,10913,11056,11140,11223,11357,11446,11523,11579,11634,11700,11773,11850,11934,12013,12087,12163,12238,12311,12416,12504,12577,12667,12758,12830,12904,12995,13047,13129,13196,13280,13367,13429,13493,13556,13625,13728,13836,13934,14038,14098,14157,14463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81e0b3af703173a357d5c452b0d31980\\transformed\\exoplayer-core-2.19.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7719,7787,7847,7911,7975,8050,8131,8230,8321", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "7782,7842,7906,7970,8045,8126,8225,8316,8390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fab9a8fce592b1bb05a0549bf6b2f96e\\transformed\\exoplayer-ui-2.19.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2149,2277,2399,2476,2556,2629,2709,2816,2924,2992,3057,3110,3168,3216,3277,3347,3416,3479,3544,3607,3664,3740,3809,3883,3935,3998,4075,4152", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2144,2272,2394,2471,2551,2624,2704,2811,2919,2987,3052,3105,3163,3211,3272,3342,3411,3474,3539,3602,3659,3735,3804,3878,3930,3993,4070,4147,4201"}, "to": {"startLines": "2,11,17,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,418,740,5620,5707,5795,5878,5976,6077,6160,6225,6322,6416,6487,6557,6621,6689,6811,6939,7061,7138,7218,7291,7371,7478,7586,7654,8395,8448,8506,8554,8615,8685,8754,8817,8882,8945,9002,9078,9147,9221,9273,9336,9413,9490", "endLines": "10,16,22,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "413,735,1048,5702,5790,5873,5971,6072,6155,6220,6317,6411,6482,6552,6616,6684,6806,6934,7056,7133,7213,7286,7366,7473,7581,7649,7714,8443,8501,8549,8610,8680,8749,8812,8877,8940,8997,9073,9142,9216,9268,9331,9408,9485,9539"}}]}]}