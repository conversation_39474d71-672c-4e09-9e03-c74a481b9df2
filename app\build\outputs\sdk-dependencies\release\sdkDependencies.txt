# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.9.2"
  }
  digests {
    sha256: "\027\232\017\336Y\223OK\327\345\310\'\241\255N\272\366\025\365\022\346c\227\241\206\235B\021\366\2711\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.0"
  }
  digests {
    sha256: "\326\371\033{\0170l\312)\237\354t\373|4\344\207Mo^\305\271%\240\264\336!\220\036\021\234?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "2.1.0"
  }
  digests {
    sha256: "\375\352lB\003rOB\350\346K\357/\v\367\221)\314\321\337\036\337\034\317\375\302-\347\337I\214v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "2.1.0"
  }
  digests {
    sha256: "#\215<~I/\021\233P\332\034\"Tm\327bF.U\362$\ta\037^S\335wb\\\325D"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-common"
    version: "8.9.2"
  }
  digests {
    sha256: "f\312\270&9\332\300\366\302C4d\300\223\260t\326\b\304\273\210~\303\212\233\213\304\254\230\022g2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-runtime"
    version: "8.9.2"
  }
  digests {
    sha256: "G\326\307\036\264D\361\033\245\233\034&\n\243\bh\006\233\342\207\354-\212\232\024\210v}\025y\0162"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\v\340Y\305\207fPHs\t\t`S\213}^\225[c\315\325\323\300+\271\2250\307\364\r\277s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.1"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\363\324\365\336\0349\033\274\302\017;45\314\272\300\023R\036v\266\220-}Yc^\301\\\037y~"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.8.1"
  }
  digests {
    sha256: "\2414\332\317Nex\262\2332\351z\245\005H\320\234\264^<\263U\034\347z\302~U\342e\330\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.9.1"
  }
  digests {
    sha256: "\264g[\226\fC\330\313\206\2258^q\222\240\003h`T*\213\235r:\245dw\307i\364Ht"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.1"
  }
  digests {
    sha256: "E\343\033\236\a\000l\373\371\017<\302;\245\237\367\337\315\032\224\350*\212\311\336n\233nx\374\313\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.9.1"
  }
  digests {
    sha256: "\261m\301}\223$\326\243\261\306k\021\221mG\364F\004\240\277\364\324q\310\fo\302\316y7\353<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.9.1"
  }
  digests {
    sha256: "XH\266gP\365h\300R{\r\351\251\337\221\260\302P\301j\b\r\217\220\236DCi\261\324\202:"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.1"
  }
  digests {
    sha256: "\300P\362U>yu\3767\247\3118\n\316e\004\200\366Z\b\347\324\207d\245p\016\236\001P\205z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\231K\336\251\257\006\312\260\233\373\242\314\366\306\215\207(\275\001\306\326m:\324E]?f\221\031>\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.9.1"
  }
  digests {
    sha256: "9Re\321X\'\272\003\257\343\225\vN\020?\346nqN\361\210\367,\311\032\301\346S\032-\336U"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.1"
  }
  digests {
    sha256: "\312\264\004\260]_\256\322\t\020J*\357\024\300\313\263\026\253\237\253\344\250\3607\260C\345\322\36445"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\257\217\270k\221p\357K\035j\362\345Y\253}\017\376\362\3574Q\032\250\367\313\230\032\363crvB"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.1"
  }
  digests {
    sha256: "?\345\\\375\375\210c\365\307\"a\314\024>\262i\250\202\331\267\226\300\231.\033\344\311\201\235\032\336 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.1"
  }
  digests {
    sha256: "\023f\306l\234\341\006\032\301\214\267\311i\324r\220\306\f\030_\214\205\320\316\335\351\002\234\v\037\367\353"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-adapters"
    version: "8.9.2"
  }
  digests {
    sha256: "\330;R+\234W)S5]\031\035\364t\365\260\226\211\216\301\377$!{\364\306$\322|n\237H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-ktx"
    version: "8.9.2"
  }
  digests {
    sha256: "\206\021\346O\304\375lB\334\341S\316]1\243\314\365\275r4\t:\254\301\240mf?Jv\b\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.11.0"
  }
  digests {
    sha256: "\216\257\1779\236\340\224\334\023I\\6\347\324\357G\036\247\357\2129\236My\311kP\375O\202E\f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-reflect"
    version: "1.8.22"
  }
  digests {
    sha256: "eX%\372e\021\"\017\355\f\256Z\255M\376\242\253\371\217\321\017\n\347~\270\332\366\253\374\320\360\'"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.2.0"
  }
  digests {
    sha256: "w\224\b\261\2523\f\324\247\255\361\262\350_\322\211\303\016~\335.\216B\204{}\006X\232\367\025\372"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "a\304\234\357\253\235\006J\352\030\231\354\242\300<\002hl\242,j\367\337D\355\177\005\255\306\271!\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.7.6"
  }
  digests {
    sha256: "\372\'\3242\002z\3565Y\304\335{\020\365\226\"!\b\205\352\216\217\302\205\367I\033\356\342\262\024\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.6"
  }
  digests {
    sha256: "\333\255-K\312\257\260\r\r\346\274o\315\n\030\207\025j{\257\343<\242\313\345\203A\355\300\324:\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.6"
  }
  digests {
    sha256: "3\315U\3019Qr$\2376\255\231\037s\337x\213B\016\374\302\250/\022\310\244\027\365\317\364\b,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\311\325\267\331+ \030\252\351\205\027\266\003\244E\034\347\225\221B\\\020\227\376\024+\301,\352\vS\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\231o\242es\367\205\025\250\322\365.\272\037\262G\031\a\bd\2471[\347\022b\032WDU\347V"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.7.6"
  }
  digests {
    sha256: "\230&n\026\312\214\343,\321\301,\225\031\207\177h\236\270\307(\\\241\334\211^\321\361e\227\334\177\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\032\352\232\203\022u\017\215G3\332\213\243z\2221:\351\262I%D\270#\354nV\034IJ\rg"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-android"
    version: "2.52"
  }
  digests {
    sha256: "Q\357\320J\301\273\016\201\265j\211y\265\251\234A\340\232A\n\357k\206\261\242V0\333\fy\305]"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger"
    version: "2.52"
  }
  digests {
    sha256: "\337\270\3500 xm\256\373\326C5\316\016\016\277\001dj\033@\177\267z\'\340R\272\364-\364X"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "jakarta.inject"
    artifactId: "jakarta.inject-api"
    version: "2.0.1"
  }
  digests {
    sha256: "\367\334\230\006/\314\361A&\253\267Q\266O\253\022\303\022Vn\214\275\310H5\230\277\374\352\223\257|"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger-lint-aar"
    version: "2.52"
  }
  digests {
    sha256: "\357\n\f\264\232\367\030\035\v.\333\2753,\272s:^\a\267\325:\212\355\341\te[\'\001\337\032"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-core"
    version: "2.52"
  }
  digests {
    sha256: "\022}i\023`U\200\225\224\216\371P\031\035\177\v\020\333\377\255\360\324{\360\316\232u\260\334i\031G"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-core"
    version: "2.19.1"
  }
  digests {
    sha256: "\350\233v\360\324\226\321\f\357\033^\340\327``R\207_\322:V\256d\250\b;:aO*S\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-common"
    version: "2.19.1"
  }
  digests {
    sha256: "\\\300\225i\316R\201\a\"\311y\253s\261\202\001g\333\210\341\273\034\272\252\270\270e\fp\363_A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-container"
    version: "2.19.1"
  }
  digests {
    sha256: "\n\022d\337\324\224\335\303\333\f@\265\030{\314\347\267\302\371Y\336\227\026*\231\246Q#\246\346y\352"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-datasource"
    version: "2.19.1"
  }
  digests {
    sha256: "\322j<\277*o\312B\216\371]\224L\n\003\343\370\304G\tl\'\325\317S\362\035;\265\356\360\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-database"
    version: "2.19.1"
  }
  digests {
    sha256: "\237?\353\036\177\027\310r\177\317U\243\314\2115J\002\352\342\326.\233\2447\335\0344\326\016\22537"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-decoder"
    version: "2.19.1"
  }
  digests {
    sha256: "\2766\027\205\334\373_\226\355\332l\337\346MA]=D\327o%^\217|&\225\a\211JwD\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-extractor"
    version: "2.19.1"
  }
  digests {
    sha256: "\242\323D\252\321\223\313N\315\257/\224\3427\227\322\314\247\224\352\325Y)\a)\211\033\005\022\260\246Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-ui"
    version: "2.19.1"
  }
  digests {
    sha256: "\303X[\0046JO\371dY\n\241%6|\263\026\356\024\212\252\216\t`\254\251\224\321\352\t\005\305"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.6.0"
  }
  digests {
    sha256: "\376b\210G\330\371\t\312\217#\326\205\006\037\232v\204\252\f\233\311\301\262\001\304\377n\267\2637(("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.11.0"
  }
  digests {
    sha256: "\237O\273\316pr\205\204\373\356\323\215@a\363mDw\350\233\312t\264\342\254\212\353h\031\260\376C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.7.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.7.0"
  }
  digests {
    sha256: "\330\263Z\334(v\217C\256Z\376j}\032\242\250x\272Q\340\271jO0\210\021\363\261\365\261>U"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-moshi"
    version: "2.11.0"
  }
  digests {
    sha256: "\333\372\351\333\336\va\345\305g\026H\310\331\375Hn\372\351\255\a\325s\355\001\275\r\344h\215\233,"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.moshi"
    artifactId: "moshi"
    version: "1.15.1"
  }
  digests {
    sha256: "F\241\021\217\341\374\022r:W\\\224\023?\310\223m\314x\323\370\207<\016p\240U\336\236Xa\246"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.moshi"
    artifactId: "moshi-kotlin"
    version: "1.15.1"
  }
  digests {
    sha256: "<ZWr\000\374I\246\226dzR\261\251u\230\245m\215o\000\343C\377\355\t\330\213@\a\315\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.jakewharton.timber"
    artifactId: "timber"
    version: "5.0.1"
  }
  digests {
    sha256: "\306\355\335\374\310\357\364*\026\004\310W\177\317\244\264\377\331\362R\022,R\3526\317\347\226\177Q/q"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "\210\226\256o\325\2560\217\360P\025`\317\262\342\234\266\363s\2038\237\234\226e\251\344\334\215\177\373\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "r\030\210h\243\234\202T\022RR\243\300.*\365\274x\035\a\216I\203v\340~\235$\362\217\212\372"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 5
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 0
  library_dep_index: 12
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
}
library_dependencies {
  library_index: 10
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 11
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 9
  library_dep_index: 9
}
library_dependencies {
  library_index: 12
  library_dep_index: 13
}
library_dependencies {
  library_index: 13
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 52
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 14
}
library_dependencies {
  library_index: 16
  library_dep_index: 3
  library_dep_index: 3
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 3
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
  library_dep_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 22
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 3
}
library_dependencies {
  library_index: 24
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 23
  library_dep_index: 17
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 25
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 26
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 27
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 28
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 25
}
library_dependencies {
  library_index: 33
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 19
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 36
  library_dep_index: 34
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 3
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 25
  library_dep_index: 34
  library_dep_index: 46
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 19
  library_dep_index: 48
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 12
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 3
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 3
  library_dep_index: 40
  library_dep_index: 3
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 41
  library_dep_index: 9
  library_dep_index: 42
  library_dep_index: 16
  library_dep_index: 44
  library_dep_index: 12
  library_dep_index: 30
  library_dep_index: 45
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 39
  library_dep_index: 3
}
library_dependencies {
  library_index: 41
  library_dep_index: 3
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 43
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
  library_dep_index: 9
}
library_dependencies {
  library_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 48
  library_dep_index: 51
  library_dep_index: 3
}
library_dependencies {
  library_index: 48
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
  library_dep_index: 3
}
library_dependencies {
  library_index: 50
  library_dep_index: 49
  library_dep_index: 48
}
library_dependencies {
  library_index: 51
  library_dep_index: 46
  library_dep_index: 3
  library_dep_index: 46
  library_dep_index: 3
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 42
  library_dep_index: 29
  library_dep_index: 43
}
library_dependencies {
  library_index: 53
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 54
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 22
  library_dep_index: 31
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 1
  library_dep_index: 61
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 40
  library_dep_index: 66
  library_dep_index: 79
  library_dep_index: 41
  library_dep_index: 70
  library_dep_index: 12
  library_dep_index: 84
  library_dep_index: 74
  library_dep_index: 87
  library_dep_index: 63
  library_dep_index: 86
}
library_dependencies {
  library_index: 56
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 3
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 12
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 52
  library_dep_index: 46
  library_dep_index: 30
  library_dep_index: 3
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 59
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 3
  library_dep_index: 59
}
library_dependencies {
  library_index: 61
  library_dep_index: 59
  library_dep_index: 1
  library_dep_index: 62
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 39
  library_dep_index: 65
  library_dep_index: 66
  library_dep_index: 68
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 12
  library_dep_index: 34
  library_dep_index: 74
  library_dep_index: 46
  library_dep_index: 3
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 61
}
library_dependencies {
  library_index: 63
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 9
}
library_dependencies {
  library_index: 64
  library_dep_index: 63
  library_dep_index: 44
  library_dep_index: 9
}
library_dependencies {
  library_index: 65
  library_dep_index: 1
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 9
}
library_dependencies {
  library_index: 68
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 69
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 68
}
library_dependencies {
  library_index: 70
  library_dep_index: 59
  library_dep_index: 1
  library_dep_index: 41
  library_dep_index: 9
  library_dep_index: 39
  library_dep_index: 25
  library_dep_index: 12
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 71
  library_dep_index: 52
  library_dep_index: 46
  library_dep_index: 72
  library_dep_index: 3
  library_dep_index: 73
}
library_dependencies {
  library_index: 71
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 24
  library_dep_index: 34
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 67
}
library_dependencies {
  library_index: 73
  library_dep_index: 60
  library_dep_index: 11
  library_dep_index: 39
  library_dep_index: 70
  library_dep_index: 26
  library_dep_index: 36
  library_dep_index: 51
  library_dep_index: 3
  library_dep_index: 70
}
library_dependencies {
  library_index: 74
  library_dep_index: 1
}
library_dependencies {
  library_index: 75
  library_dep_index: 1
}
library_dependencies {
  library_index: 76
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 67
}
library_dependencies {
  library_index: 77
  library_dep_index: 61
  library_dep_index: 40
  library_dep_index: 78
}
library_dependencies {
  library_index: 79
  library_dep_index: 40
  library_dep_index: 9
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 81
  library_dep_index: 71
  library_dep_index: 82
  library_dep_index: 83
}
library_dependencies {
  library_index: 81
  library_dep_index: 1
}
library_dependencies {
  library_index: 82
  library_dep_index: 1
}
library_dependencies {
  library_index: 83
  library_dep_index: 1
}
library_dependencies {
  library_index: 84
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 67
  library_dep_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 85
  library_dep_index: 39
  library_dep_index: 3
}
library_dependencies {
  library_index: 86
  library_dep_index: 1
  library_dep_index: 41
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 70
  library_dep_index: 84
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 9
}
library_dependencies {
  library_index: 88
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 44
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
  library_dep_index: 94
  library_dep_index: 90
  library_dep_index: 94
  library_dep_index: 96
  library_dep_index: 95
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 92
}
library_dependencies {
  library_index: 90
  library_dep_index: 73
  library_dep_index: 91
  library_dep_index: 97
  library_dep_index: 3
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 93
  library_dep_index: 92
}
library_dependencies {
  library_index: 91
  library_dep_index: 60
  library_dep_index: 41
  library_dep_index: 9
  library_dep_index: 31
  library_dep_index: 36
  library_dep_index: 92
  library_dep_index: 3
  library_dep_index: 92
  library_dep_index: 93
  library_dep_index: 90
  library_dep_index: 89
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 92
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 39
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 52
  library_dep_index: 51
  library_dep_index: 3
  library_dep_index: 93
  library_dep_index: 90
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 93
  library_dep_index: 92
  library_dep_index: 92
  library_dep_index: 90
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 94
  library_dep_index: 93
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 90
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 92
}
library_dependencies {
  library_index: 95
  library_dep_index: 41
  library_dep_index: 67
  library_dep_index: 66
  library_dep_index: 91
  library_dep_index: 87
  library_dep_index: 55
  library_dep_index: 93
  library_dep_index: 90
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 94
  library_dep_index: 96
  library_dep_index: 92
}
library_dependencies {
  library_index: 96
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 90
  library_dep_index: 89
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 92
}
library_dependencies {
  library_index: 97
  library_dep_index: 1
  library_dep_index: 67
  library_dep_index: 40
  library_dep_index: 98
  library_dep_index: 87
}
library_dependencies {
  library_index: 98
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 40
}
library_dependencies {
  library_index: 99
  library_dep_index: 100
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 59
  library_dep_index: 1
  library_dep_index: 41
  library_dep_index: 70
  library_dep_index: 17
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 46
  library_dep_index: 102
  library_dep_index: 3
}
library_dependencies {
  library_index: 100
  library_dep_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 104
  library_dep_index: 100
  library_dep_index: 105
  library_dep_index: 102
}
library_dependencies {
  library_index: 106
  library_dep_index: 41
  library_dep_index: 15
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 107
  library_dep_index: 108
}
library_dependencies {
  library_index: 107
  library_dep_index: 1
  library_dep_index: 6
  library_dep_index: 108
  library_dep_index: 106
}
library_dependencies {
  library_index: 108
  library_dep_index: 107
  library_dep_index: 106
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 107
  library_dep_index: 106
}
library_dependencies {
  library_index: 109
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 110
}
library_dependencies {
  library_index: 110
  library_dep_index: 1
  library_dep_index: 109
  library_dep_index: 3
  library_dep_index: 109
}
library_dependencies {
  library_index: 111
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 112
  library_dep_index: 115
  library_dep_index: 116
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 117
}
library_dependencies {
  library_index: 112
  library_dep_index: 1
  library_dep_index: 113
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
  library_dep_index: 43
}
library_dependencies {
  library_index: 115
  library_dep_index: 112
  library_dep_index: 1
}
library_dependencies {
  library_index: 116
  library_dep_index: 112
  library_dep_index: 117
  library_dep_index: 1
}
library_dependencies {
  library_index: 117
  library_dep_index: 112
  library_dep_index: 1
}
library_dependencies {
  library_index: 118
  library_dep_index: 112
  library_dep_index: 1
}
library_dependencies {
  library_index: 119
  library_dep_index: 1
  library_dep_index: 112
  library_dep_index: 115
  library_dep_index: 118
}
library_dependencies {
  library_index: 120
  library_dep_index: 112
  library_dep_index: 1
  library_dep_index: 84
  library_dep_index: 121
}
library_dependencies {
  library_index: 121
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 40
}
library_dependencies {
  library_index: 122
  library_dep_index: 123
}
library_dependencies {
  library_index: 123
  library_dep_index: 124
  library_dep_index: 6
}
library_dependencies {
  library_index: 124
  library_dep_index: 125
}
library_dependencies {
  library_index: 125
  library_dep_index: 3
}
library_dependencies {
  library_index: 126
  library_dep_index: 122
  library_dep_index: 127
}
library_dependencies {
  library_index: 127
  library_dep_index: 124
  library_dep_index: 6
}
library_dependencies {
  library_index: 128
  library_dep_index: 123
  library_dep_index: 6
}
library_dependencies {
  library_index: 129
  library_dep_index: 127
  library_dep_index: 57
  library_dep_index: 6
}
library_dependencies {
  library_index: 130
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 131
  library_dep_index: 3
  library_dep_index: 132
}
library_dependencies {
  library_index: 132
  library_dep_index: 3
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 7
  dependency_index: 8
  dependency_index: 53
  dependency_index: 54
  dependency_index: 3
  dependency_index: 55
  dependency_index: 39
  dependency_index: 31
  dependency_index: 61
  dependency_index: 77
  dependency_index: 84
  dependency_index: 75
  dependency_index: 88
  dependency_index: 76
  dependency_index: 36
  dependency_index: 27
  dependency_index: 89
  dependency_index: 96
  dependency_index: 73
  dependency_index: 99
  dependency_index: 106
  dependency_index: 108
  dependency_index: 111
  dependency_index: 120
  dependency_index: 22
  dependency_index: 122
  dependency_index: 126
  dependency_index: 123
  dependency_index: 128
  dependency_index: 127
  dependency_index: 129
  dependency_index: 130
  dependency_index: 131
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
