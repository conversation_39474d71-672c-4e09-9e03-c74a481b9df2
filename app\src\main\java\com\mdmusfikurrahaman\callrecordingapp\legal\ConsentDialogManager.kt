package com.mdmusfikurrahaman.callrecordingapp.legal

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.widget.CheckBox
import android.widget.TextView
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import com.mdmusfikurrahaman.callrecordingapp.R

/**
 * Manager for showing consent dialogs before recording calls
 * 
 * This class handles:
 * - Pre-call consent dialogs
 * - Consent tracking and logging
 * - Customizable consent messages
 * - Legal compliance for different jurisdictions
 */
@Singleton
class ConsentDialogManager @Inject constructor(
    private val legalComplianceManager: LegalComplianceManager
) {
    
    /**
     * Show consent dialog before recording a call
     */
    suspend fun showConsentDialog(
        context: Context,
        phoneNumber: String,
        contactName: String?
    ): ConsentResult = suspendCancellableCoroutine { continuation ->
        
        try {
            val displayName = contactName ?: phoneNumber
            
            val dialog = AlertDialog.Builder(context)
                .setTitle("Recording Consent Required")
                .setMessage(buildConsentMessage(displayName))
                .setCancelable(false)
                .setPositiveButton("I Consent & Record") { dialog, _ ->
                    // Log consent
                    try {
                        kotlinx.coroutines.GlobalScope.launch {
                            legalComplianceManager.logCallConsent(phoneNumber, true)
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "Error logging consent")
                    }
                    
                    dialog.dismiss()
                    continuation.resume(ConsentResult.GRANTED)
                }
                .setNegativeButton("Decline") { dialog, _ ->
                    // Log consent denial
                    try {
                        kotlinx.coroutines.GlobalScope.launch {
                            legalComplianceManager.logCallConsent(phoneNumber, false)
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "Error logging consent denial")
                    }
                    
                    dialog.dismiss()
                    continuation.resume(ConsentResult.DENIED)
                }
                .setNeutralButton("Don't Ask Again") { dialog, _ ->
                    // Disable consent dialog
                    legalComplianceManager.setConsentDialogEnabled(false)
                    
                    dialog.dismiss()
                    continuation.resume(ConsentResult.DISABLED)
                }
                .create()
            
            // Handle cancellation
            continuation.invokeOnCancellation {
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
            }
            
            dialog.show()
            
        } catch (e: Exception) {
            Timber.e(e, "Error showing consent dialog")
            continuation.resume(ConsentResult.ERROR)
        }
    }
    
    /**
     * Show jurisdiction warning dialog
     */
    suspend fun showJurisdictionWarning(context: Context): Boolean = suspendCancellableCoroutine { continuation ->
        
        try {
            val dialog = AlertDialog.Builder(context)
                .setTitle("Important Legal Notice")
                .setMessage(getJurisdictionWarningMessage())
                .setCancelable(false)
                .setPositiveButton("I Understand") { dialog, _ ->
                    legalComplianceManager.markJurisdictionWarningShown()
                    dialog.dismiss()
                    continuation.resume(true)
                }
                .setNegativeButton("Learn More") { dialog, _ ->
                    // You could open a web page with more information
                    dialog.dismiss()
                    continuation.resume(false)
                }
                .create()
            
            // Handle cancellation
            continuation.invokeOnCancellation {
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
            }
            
            dialog.show()
            
        } catch (e: Exception) {
            Timber.e(e, "Error showing jurisdiction warning")
            continuation.resume(false)
        }
    }
    
    /**
     * Build consent message for the dialog
     */
    private fun buildConsentMessage(displayName: String): String {
        return buildString {
            appendLine("You are about to record a call with $displayName.")
            appendLine()
            appendLine("IMPORTANT LEGAL REQUIREMENTS:")
            appendLine()
            appendLine("• Recording phone calls may be illegal in your jurisdiction without proper consent")
            appendLine("• You are responsible for complying with all applicable laws")
            appendLine("• Some jurisdictions require consent from ALL parties")
            appendLine("• Some jurisdictions require only ONE-party consent")
            appendLine("• Some jurisdictions prohibit call recording entirely")
            appendLine()
            appendLine("By proceeding, you confirm that:")
            appendLine("• You have the legal right to record this call")
            appendLine("• You will obtain any required consent from other parties")
            appendLine("• You will comply with all applicable laws and regulations")
            appendLine("• You accept full responsibility for the recording")
            appendLine()
            appendLine("Do you consent to record this call?")
        }
    }
    
    /**
     * Get jurisdiction warning message
     */
    private fun getJurisdictionWarningMessage(): String {
        return buildString {
            appendLine("CALL RECORDING LEGAL NOTICE")
            appendLine()
            appendLine("Call recording laws vary significantly by location:")
            appendLine()
            appendLine("ONE-PARTY CONSENT JURISDICTIONS:")
            appendLine("• Only one party (you) needs to consent to recording")
            appendLine("• Examples: Most US states, Canada (some provinces)")
            appendLine()
            appendLine("TWO-PARTY/ALL-PARTY CONSENT JURISDICTIONS:")
            appendLine("• ALL parties must consent before recording")
            appendLine("• Examples: California, Florida, Pennsylvania, Germany, UK")
            appendLine()
            appendLine("PROHIBITED JURISDICTIONS:")
            appendLine("• Call recording may be completely prohibited")
            appendLine("• Examples: Some countries have strict privacy laws")
            appendLine()
            appendLine("YOUR RESPONSIBILITIES:")
            appendLine("• Research your local laws before using this app")
            appendLine("• Obtain proper consent when required")
            appendLine("• Use recordings only for lawful purposes")
            appendLine("• Respect privacy rights of all parties")
            appendLine()
            appendLine("This app does not provide legal advice. Consult a lawyer if you're unsure about the legality of call recording in your area.")
        }
    }
    
    /**
     * Show quick consent notification (for ongoing calls)
     */
    fun showQuickConsentNotification(context: Context, phoneNumber: String): Boolean {
        return try {
            // This could show a toast or notification instead of a full dialog
            // for calls that are already in progress
            
            // Log the quick consent
            kotlinx.coroutines.GlobalScope.launch {
                legalComplianceManager.logCallConsent(phoneNumber, true)
            }
            
            true
        } catch (e: Exception) {
            Timber.e(e, "Error showing quick consent notification")
            false
        }
    }
    
    /**
     * Check if consent is required for a specific number
     */
    fun isConsentRequired(phoneNumber: String): Boolean {
        // You could implement logic here to check if consent is required
        // based on the phone number (e.g., emergency numbers, business numbers, etc.)
        
        // For now, assume consent is always required unless disabled
        return legalComplianceManager.shouldShowConsentDialog()
    }
    
    /**
     * Get consent history for audit purposes
     */
    fun getConsentHistory(): List<ConsentRecord> {
        // This would typically query a database of consent records
        // For now, return empty list as this would be implemented
        // as part of a more comprehensive audit system
        return emptyList()
    }
}

/**
 * Enum representing consent dialog results
 */
enum class ConsentResult {
    GRANTED,    // User granted consent
    DENIED,     // User denied consent
    DISABLED,   // User disabled consent dialogs
    ERROR       // Error occurred
}

/**
 * Data class representing a consent record for audit purposes
 */
data class ConsentRecord(
    val phoneNumber: String,
    val timestamp: Long,
    val consentGiven: Boolean,
    val method: String // "dialog", "notification", etc.
)
