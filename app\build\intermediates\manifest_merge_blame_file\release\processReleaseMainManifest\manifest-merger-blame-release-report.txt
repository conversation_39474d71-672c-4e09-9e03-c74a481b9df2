1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdmusfikurrahaman.callrecordingapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="35" />
10
11    <!-- Core recording permissions -->
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:5-71
12-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:5-75
13-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.READ_CALL_LOG" />
14-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:5-72
14-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:8:22-69
15
16    <!-- Service permissions -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
18-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:5-94
18-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:12:22-91
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
19-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:5-88
19-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:13:22-85
20
21    <!-- Storage permissions -->
22    <uses-permission
22-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:5-17:38
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:16:22-78
24        android:maxSdkVersion="28" />
24-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:17:9-35
25    <uses-permission
25-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:5-19:38
26        android:name="android.permission.READ_EXTERNAL_STORAGE"
26-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:18:22-77
27        android:maxSdkVersion="32" />
27-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:19:9-35
28
29    <!-- Notification permission for Android 13+ -->
30    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
30-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:5-77
30-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:22:22-74
31
32    <!-- MediaProjection for screen/audio capture on Android 10+ -->
33    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
33-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:5-78
33-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:25:22-75
34
35    <!-- Accessibility service permission -->
36    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
36-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:5-29:47
36-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:28:22-82
37
38    <!-- Phone state and call management -->
39    <uses-permission android:name="android.permission.READ_CONTACTS" />
39-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:5-72
39-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:32:22-69
40    <uses-permission
40-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:5-34:38
41        android:name="android.permission.PROCESS_OUTGOING_CALLS"
41-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:33:22-78
42        android:maxSdkVersion="28" />
42-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:34:9-35
43
44    <!-- Wake lock for background recording -->
45    <uses-permission android:name="android.permission.WAKE_LOCK" />
45-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:5-68
45-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:37:22-65
46
47    <!-- Internet for AI transcription (optional) -->
48    <uses-permission android:name="android.permission.INTERNET" />
48-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:5-67
48-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:40:22-64
49    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
49-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:5-79
49-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:41:22-76
50
51    <permission
51-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
52        android:name="com.mdmusfikurrahaman.callrecordingapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="com.mdmusfikurrahaman.callrecordingapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
56
57    <application
57-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:43:5-147:19
58        android:name="com.mdmusfikurrahaman.callrecordingapp.CallRecordingApplication"
58-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:44:9-49
59        android:allowBackup="true"
59-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:45:9-35
60        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
60-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
61        android:dataExtractionRules="@xml/data_extraction_rules"
61-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:46:9-65
62        android:extractNativeLibs="false"
63        android:fullBackupContent="@xml/backup_rules"
63-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:47:9-54
64        android:icon="@mipmap/ic_launcher"
64-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:48:9-43
65        android:label="@string/app_name"
65-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:49:9-41
66        android:requestLegacyExternalStorage="true"
66-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:53:9-52
67        android:roundIcon="@mipmap/ic_launcher_round"
67-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:50:9-54
68        android:supportsRtl="true"
68-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:51:9-35
69        android:theme="@style/Theme.CallRecordingApp" >
69-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:52:9-54
70
71        <!-- Main Activity -->
72        <activity
72-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:56:9-66:20
73            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity"
73-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:57:13-54
74            android:exported="true"
74-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:58:13-36
75            android:label="@string/app_name"
75-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:59:13-45
76            android:launchMode="singleTop"
76-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:61:13-43
77            android:theme="@style/Theme.CallRecordingApp" >
77-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:60:13-58
78            <intent-filter>
78-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:62:13-65:29
79                <action android:name="android.intent.action.MAIN" />
79-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:17-69
79-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:63:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:17-77
81-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:64:27-74
82            </intent-filter>
83        </activity>
84
85        <!-- Settings Activity -->
86        <activity
86-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:69:9-73:71
87            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.settings.SettingsActivity"
87-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:70:13-67
88            android:exported="false"
88-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:71:13-37
89            android:label="@string/settings"
89-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:72:13-45
90            android:parentActivityName="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity" />
90-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:73:13-68
91
92        <!-- Recording Detail Activity -->
93        <activity
93-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:76:9-80:71
94            android:name="com.mdmusfikurrahaman.callrecordingapp.presentation.detail.RecordingDetailActivity"
94-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:77:13-72
95            android:exported="false"
95-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:78:13-37
96            android:label="@string/recording_details"
96-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:79:13-54
97            android:parentActivityName="com.mdmusfikurrahaman.callrecordingapp.presentation.MainActivity" />
97-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:80:13-68
98
99        <!-- Call Recording Service -->
100        <service
100-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:83:9-87:74
101            android:name="com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService"
101-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:84:13-57
102            android:enabled="true"
102-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:85:13-35
103            android:exported="false"
103-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:86:13-37
104            android:foregroundServiceType="mediaProjection|microphone" />
104-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:87:13-71
105
106        <!-- Accessibility Service for fallback recording -->
107        <service
107-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:90:9-101:19
108            android:name="com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingAccessibilityService"
108-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:91:13-70
109            android:enabled="true"
109-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:92:13-35
110            android:exported="false"
110-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:93:13-37
111            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
111-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:94:13-79
112            <intent-filter>
112-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:95:13-97:29
113                <action android:name="android.accessibilityservice.AccessibilityService" />
113-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:17-92
113-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:96:25-89
114            </intent-filter>
115
116            <meta-data
116-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:98:13-100:72
117                android:name="android.accessibilityservice"
117-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:99:17-60
118                android:resource="@xml/accessibility_service_config" />
118-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:100:17-69
119        </service>
120
121        <!-- Phone State Receiver -->
122        <receiver
122-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:104:9-111:20
123            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.PhoneStateReceiver"
123-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:105:13-56
124            android:enabled="true"
124-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:106:13-35
125            android:exported="false" >
125-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:107:13-37
126            <intent-filter android:priority="1000" >
126-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:13-110:29
126-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:108:28-51
127                <action android:name="android.intent.action.PHONE_STATE" />
127-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:17-76
127-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:109:25-73
128            </intent-filter>
129        </receiver>
130
131        <!-- Call Log Receiver -->
132        <receiver
132-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:114:9-121:20
133            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.CallLogReceiver"
133-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:115:13-53
134            android:enabled="true"
134-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:116:13-35
135            android:exported="false" >
135-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:117:13-37
136            <intent-filter>
136-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:118:13-120:29
137                <action android:name="android.intent.action.NEW_OUTGOING_CALL" />
137-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:17-82
137-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:119:25-79
138            </intent-filter>
139        </receiver>
140
141        <!-- Boot Receiver to restart service after reboot -->
142        <receiver
142-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:124:9-134:20
143            android:name="com.mdmusfikurrahaman.callrecordingapp.receiver.BootReceiver"
143-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:125:13-50
144            android:enabled="true"
144-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:126:13-35
145            android:exported="false" >
145-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:127:13-37
146            <intent-filter android:priority="1000" >
146-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:13-133:29
146-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:128:28-51
147                <action android:name="android.intent.action.BOOT_COMPLETED" />
147-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:17-79
147-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:129:25-76
148                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
148-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:17-84
148-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:130:25-81
149                <action android:name="android.intent.action.PACKAGE_REPLACED" />
149-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:17-81
149-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:131:25-78
150
151                <data android:scheme="package" />
151-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:17-50
151-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:132:23-47
152            </intent-filter>
153        </receiver>
154
155        <!-- File Provider for sharing recordings -->
156        <provider
157            android:name="androidx.core.content.FileProvider"
157-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:138:13-62
158            android:authorities="com.mdmusfikurrahaman.callrecordingapp.fileprovider"
158-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:139:13-64
159            android:exported="false"
159-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:140:13-37
160            android:grantUriPermissions="true" >
160-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:141:13-47
161            <meta-data
161-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:142:13-144:54
162                android:name="android.support.FILE_PROVIDER_PATHS"
162-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:143:17-67
163                android:resource="@xml/file_paths" />
163-->C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\AndroidManifest.xml:144:17-51
164        </provider>
165        <provider
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
166            android:name="androidx.startup.InitializationProvider"
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
167            android:authorities="com.mdmusfikurrahaman.callrecordingapp.androidx-startup"
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
168            android:exported="false" >
168-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
169            <meta-data
169-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.emoji2.text.EmojiCompatInitializer"
170-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
171                android:value="androidx.startup" />
171-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
173-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
174                android:value="androidx.startup" />
174-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
175            <meta-data
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
176                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
176-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
177                android:value="androidx.startup" />
177-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
178        </provider>
179
180        <uses-library
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
181            android:name="androidx.window.extensions"
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
182            android:required="false" />
182-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
183        <uses-library
183-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
184            android:name="androidx.window.sidecar"
184-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
185            android:required="false" />
185-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
186
187        <service
187-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
188            android:name="androidx.room.MultiInstanceInvalidationService"
188-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
189            android:directBootAware="true"
189-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
190            android:exported="false" />
190-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
191
192        <receiver
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
193            android:name="androidx.profileinstaller.ProfileInstallReceiver"
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
194            android:directBootAware="false"
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
195            android:enabled="true"
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
196            android:exported="true"
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
197            android:permission="android.permission.DUMP" >
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
199                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
202                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
205                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
206            </intent-filter>
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
208                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
209            </intent-filter>
210        </receiver>
211    </application>
212
213</manifest>
