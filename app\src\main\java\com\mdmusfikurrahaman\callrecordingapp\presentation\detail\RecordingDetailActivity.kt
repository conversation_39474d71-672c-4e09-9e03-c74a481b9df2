package com.mdmusfikurrahaman.callrecordingapp.presentation.detail

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import dagger.hilt.android.AndroidEntryPoint
import com.mdmusfikurrahaman.callrecordingapp.presentation.theme.CallRecordingAppTheme

/**
 * Activity for displaying detailed view of a call recording
 */
@AndroidEntryPoint
class RecordingDetailActivity : ComponentActivity() {
    
    companion object {
        const val EXTRA_RECORDING_ID = "recording_id"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val recordingId = intent.getLongExtra(EXTRA_RECORDING_ID, -1L)
        if (recordingId == -1L) {
            finish()
            return
        }
        
        enableEdgeToEdge()
        
        setContent {
            CallRecordingAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    RecordingDetailScreen(
                        recordingId = recordingId,
                        onNavigateBack = { finish() }
                    )
                }
            }
        }
    }
}
