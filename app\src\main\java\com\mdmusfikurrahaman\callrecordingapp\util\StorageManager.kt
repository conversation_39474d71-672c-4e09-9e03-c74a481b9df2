package com.mdmusfikurrahaman.callrecordingapp.util

import android.content.Context
import android.os.Environment
import android.os.StatFs
import timber.log.Timber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.data.repository.CallRecordingRepository
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.UserPreferences

/**
 * Advanced storage management utility
 * 
 * This class provides comprehensive storage management including:
 * - Storage space monitoring
 * - Automatic cleanup based on user preferences
 * - Storage usage analytics
 * - Low storage warnings
 */
@Singleton
class StorageManager @Inject constructor(
    private val context: Context,
    private val fileManager: FileManager,
    private val repository: CallRecordingRepository
) {
    
    companion object {
        private const val LOW_STORAGE_THRESHOLD_MB = 50L
        private const val CRITICAL_STORAGE_THRESHOLD_MB = 20L
        private const val BYTES_PER_MB = 1024L * 1024L
    }
    
    /**
     * Data class representing storage information
     */
    data class StorageInfo(
        val totalSpaceBytes: Long,
        val freeSpaceBytes: Long,
        val usedSpaceBytes: Long,
        val recordingsSpaceBytes: Long,
        val totalSpaceMB: Long = totalSpaceBytes / BYTES_PER_MB,
        val freeSpaceMB: Long = freeSpaceBytes / BYTES_PER_MB,
        val usedSpaceMB: Long = usedSpaceBytes / BYTES_PER_MB,
        val recordingsSpaceMB: Long = recordingsSpaceBytes / BYTES_PER_MB
    )
    
    /**
     * Enum representing storage status
     */
    enum class StorageStatus {
        NORMAL,
        LOW,
        CRITICAL,
        FULL
    }
    
    /**
     * Get comprehensive storage information
     */
    suspend fun getStorageInfo(): StorageInfo = withContext(Dispatchers.IO) {
        try {
            val recordingsDir = fileManager.getRecordingsDirectory()
            val statFs = StatFs(recordingsDir.absolutePath)
            
            val totalBytes = statFs.totalBytes
            val freeBytes = statFs.availableBytes
            val usedBytes = totalBytes - freeBytes
            val recordingsBytes = calculateRecordingsSize()
            
            StorageInfo(
                totalSpaceBytes = totalBytes,
                freeSpaceBytes = freeBytes,
                usedSpaceBytes = usedBytes,
                recordingsSpaceBytes = recordingsBytes
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Error getting storage info")
            StorageInfo(0, 0, 0, 0)
        }
    }
    
    /**
     * Get current storage status
     */
    suspend fun getStorageStatus(): StorageStatus {
        val storageInfo = getStorageInfo()
        
        return when {
            storageInfo.freeSpaceMB <= 0 -> StorageStatus.FULL
            storageInfo.freeSpaceMB <= CRITICAL_STORAGE_THRESHOLD_MB -> StorageStatus.CRITICAL
            storageInfo.freeSpaceMB <= LOW_STORAGE_THRESHOLD_MB -> StorageStatus.LOW
            else -> StorageStatus.NORMAL
        }
    }
    
    /**
     * Check if there's enough space for recording
     */
    suspend fun hasEnoughSpaceForRecording(estimatedDurationMinutes: Int = 30): Boolean {
        val storageInfo = getStorageInfo()
        
        // Estimate space needed (rough calculation: 1MB per minute for medium quality)
        val estimatedSpaceNeededMB = estimatedDurationMinutes.toLong()
        
        return storageInfo.freeSpaceMB > (estimatedSpaceNeededMB + LOW_STORAGE_THRESHOLD_MB)
    }
    
    /**
     * Perform automatic cleanup based on user preferences
     */
    suspend fun performAutomaticCleanup(): CleanupResult = withContext(Dispatchers.IO) {
        try {
            val preferences = repository.getUserPreferencesSync()
            var deletedFiles = 0
            var freedSpaceMB = 0L
            
            if (preferences?.autoDeleteEnabled == true) {
                // Clean up old recordings
                val result = cleanupOldRecordings(preferences)
                deletedFiles += result.deletedFiles
                freedSpaceMB += result.freedSpaceMB
            }
            
            // Clean up temporary files
            val tempResult = cleanupTempFiles()
            deletedFiles += tempResult.deletedFiles
            freedSpaceMB += tempResult.freedSpaceMB
            
            // If storage is still critical, perform emergency cleanup
            if (getStorageStatus() == StorageStatus.CRITICAL) {
                val emergencyResult = performEmergencyCleanup()
                deletedFiles += emergencyResult.deletedFiles
                freedSpaceMB += emergencyResult.freedSpaceMB
            }
            
            CleanupResult(
                success = true,
                deletedFiles = deletedFiles,
                freedSpaceMB = freedSpaceMB
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Error during automatic cleanup")
            CleanupResult(success = false, error = e.message)
        }
    }
    
    /**
     * Clean up old recordings based on user preferences
     */
    private suspend fun cleanupOldRecordings(preferences: UserPreferences): CleanupResult {
        return try {
            val cutoffTime = System.currentTimeMillis() - (preferences.autoDeleteDays * 24 * 60 * 60 * 1000L)
            val oldRecordings = repository.getAllRecordings().value?.filter { recording ->
                recording.timestamp < cutoffTime && !recording.isFavorite
            } ?: emptyList()
            
            var deletedFiles = 0
            var freedSpaceBytes = 0L
            
            oldRecordings.forEach { recording ->
                try {
                    val file = File(recording.filePath)
                    if (file.exists()) {
                        freedSpaceBytes += file.length()
                        if (file.delete()) {
                            deletedFiles++
                        }
                    }
                    
                    // Remove from database
                    repository.deleteRecordingById(recording.id)
                    
                } catch (e: Exception) {
                    Timber.w(e, "Error deleting old recording: ${recording.filePath}")
                }
            }
            
            CleanupResult(
                success = true,
                deletedFiles = deletedFiles,
                freedSpaceMB = freedSpaceBytes / BYTES_PER_MB
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Error cleaning up old recordings")
            CleanupResult(success = false, error = e.message)
        }
    }
    
    /**
     * Clean up temporary files
     */
    private suspend fun cleanupTempFiles(): CleanupResult = withContext(Dispatchers.IO) {
        try {
            val tempDir = fileManager.getTempDirectory()
            var deletedFiles = 0
            var freedSpaceBytes = 0L
            
            tempDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    try {
                        freedSpaceBytes += file.length()
                        if (file.delete()) {
                            deletedFiles++
                        }
                    } catch (e: Exception) {
                        Timber.w(e, "Error deleting temp file: ${file.name}")
                    }
                }
            }
            
            CleanupResult(
                success = true,
                deletedFiles = deletedFiles,
                freedSpaceMB = freedSpaceBytes / BYTES_PER_MB
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Error cleaning up temp files")
            CleanupResult(success = false, error = e.message)
        }
    }
    
    /**
     * Perform emergency cleanup when storage is critically low
     */
    private suspend fun performEmergencyCleanup(): CleanupResult {
        return try {
            // Delete oldest non-favorite recordings until we have enough space
            val allRecordings = repository.getAllRecordings().value
                ?.filter { !it.isFavorite }
                ?.sortedBy { it.timestamp } // Oldest first
                ?: emptyList()
            
            var deletedFiles = 0
            var freedSpaceBytes = 0L
            val targetFreeSpaceMB = LOW_STORAGE_THRESHOLD_MB * 2 // Try to free up 100MB
            
            for (recording in allRecordings) {
                if (freedSpaceBytes / BYTES_PER_MB >= targetFreeSpaceMB) {
                    break
                }
                
                try {
                    val file = File(recording.filePath)
                    if (file.exists()) {
                        freedSpaceBytes += file.length()
                        if (file.delete()) {
                            deletedFiles++
                        }
                    }
                    
                    repository.deleteRecordingById(recording.id)
                    
                } catch (e: Exception) {
                    Timber.w(e, "Error during emergency cleanup: ${recording.filePath}")
                }
            }
            
            CleanupResult(
                success = true,
                deletedFiles = deletedFiles,
                freedSpaceMB = freedSpaceBytes / BYTES_PER_MB
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Error during emergency cleanup")
            CleanupResult(success = false, error = e.message)
        }
    }
    
    /**
     * Calculate total size of all recordings
     */
    private suspend fun calculateRecordingsSize(): Long = withContext(Dispatchers.IO) {
        try {
            val recordingsDir = fileManager.getRecordingsDirectory()
            var totalSize = 0L
            
            recordingsDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    totalSize += file.length()
                }
            }
            
            totalSize
            
        } catch (e: Exception) {
            Timber.e(e, "Error calculating recordings size")
            0L
        }
    }
    
    /**
     * Get storage usage breakdown
     */
    suspend fun getStorageUsageBreakdown(): Map<String, Long> {
        return try {
            val recordingsSize = calculateRecordingsSize()
            val tempSize = calculateTempSize()
            
            mapOf(
                "recordings" to recordingsSize,
                "temp" to tempSize,
                "total" to (recordingsSize + tempSize)
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Error getting storage usage breakdown")
            emptyMap()
        }
    }
    
    /**
     * Calculate size of temporary files
     */
    private suspend fun calculateTempSize(): Long = withContext(Dispatchers.IO) {
        try {
            val tempDir = fileManager.getTempDirectory()
            var totalSize = 0L
            
            tempDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    totalSize += file.length()
                }
            }
            
            totalSize
            
        } catch (e: Exception) {
            Timber.e(e, "Error calculating temp size")
            0L
        }
    }
    
    /**
     * Data class representing cleanup results
     */
    data class CleanupResult(
        val success: Boolean,
        val deletedFiles: Int = 0,
        val freedSpaceMB: Long = 0,
        val error: String? = null
    )
}
