package com.mdmusfikurrahaman.callrecordingapp.data.database.converter

import androidx.room.TypeConverter
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.RecordingQuality
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.ThemeMode
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.Speaker

/**
 * Type converters for Room database
 * 
 * These converters allow Room to store enum types and other custom types
 * in the SQLite database by converting them to/from primitive types
 */
class DatabaseConverters {
    
    // CallType converters
    @TypeConverter
    fun fromCallType(callType: CallType): String {
        return callType.name
    }
    
    @TypeConverter
    fun toCallType(callType: String): CallType {
        return CallType.valueOf(callType)
    }
    
    // RecordingQuality converters
    @TypeConverter
    fun fromRecordingQuality(quality: RecordingQuality): String {
        return quality.name
    }
    
    @TypeConverter
    fun toRecordingQuality(quality: String): RecordingQuality {
        return RecordingQuality.valueOf(quality)
    }
    
    // ThemeMode converters
    @TypeConverter
    fun fromThemeMode(themeMode: ThemeMode): String {
        return themeMode.name
    }
    
    @TypeConverter
    fun toThemeMode(themeMode: String): ThemeMode {
        return ThemeMode.valueOf(themeMode)
    }
    
    // Speaker converters
    @TypeConverter
    fun fromSpeaker(speaker: Speaker): String {
        return speaker.name
    }
    
    @TypeConverter
    fun toSpeaker(speaker: String): Speaker {
        return Speaker.valueOf(speaker)
    }
}
