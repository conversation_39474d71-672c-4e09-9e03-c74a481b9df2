package com.mdmusfikurrahaman.callrecordingapp.presentation.detail.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.util.DateTimeUtils
import java.io.File

/**
 * Card displaying recording metadata and technical information
 */
@Composable
fun RecordingMetadataCard(
    recording: CallRecording,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Recording Details",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // File information
            MetadataRow(
                icon = Icons.Default.AudioFile,
                label = "File Size",
                value = formatFileSize(recording.fileSize)
            )
            
            MetadataRow(
                icon = Icons.Default.HighQuality,
                label = "Quality",
                value = recording.recordingQuality.displayName
            )
            
            MetadataRow(
                icon = Icons.Default.Schedule,
                label = "Duration",
                value = DateTimeUtils.formatDuration(recording.duration)
            )
            
            MetadataRow(
                icon = Icons.Default.Folder,
                label = "File Path",
                value = File(recording.filePath).name
            )
            
            MetadataRow(
                icon = Icons.Default.DateRange,
                label = "Created",
                value = DateTimeUtils.formatDateTime(recording.createdAt)
            )
            
            if (recording.updatedAt != recording.createdAt) {
                MetadataRow(
                    icon = Icons.Default.Update,
                    label = "Modified",
                    value = DateTimeUtils.formatDateTime(recording.updatedAt)
                )
            }
            
            // Additional features
            if (recording.hasTranscript) {
                MetadataRow(
                    icon = Icons.Default.Subtitles,
                    label = "Transcript",
                    value = "Available"
                )
            }
            
            if (recording.isFavorite) {
                MetadataRow(
                    icon = Icons.Default.Favorite,
                    label = "Favorite",
                    value = "Yes"
                )
            }
        }
    }
}

@Composable
private fun MetadataRow(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = label,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = value,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * Format file size in bytes to readable string
 */
private fun formatFileSize(bytes: Long): String {
    return when {
        bytes < 1024 -> "$bytes B"
        bytes < 1024 * 1024 -> "${bytes / 1024} KB"
        bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)} MB"
        else -> "${bytes / (1024 * 1024 * 1024)} GB"
    }
}
