com.mdmusfikurrahaman.callrecordingapp-lifecycle-process-2.9.1-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7558b4e2124f27144d2ebadd71191\transformed\lifecycle-process-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-recyclerview-1.3.2-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d199c28c617ca78de992a9836861ec3\transformed\recyclerview-1.3.2\res
com.mdmusfikurrahaman.callrecordingapp-databinding-adapters-8.9.2-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12278f89c9c1a6d0266149d8322959d5\transformed\databinding-adapters-8.9.2\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-2.7.6-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1242b96d8035efc7f0a4192cf371d3e5\transformed\navigation-common-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-views-helper-1.2.0-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1270ea2657d9c27a21e7a30637a34067\transformed\emoji2-views-helper-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-sqlite-framework-2.4.0-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141494e4442b6d040d54e39a243cdd85\transformed\sqlite-framework-2.4.0\res
com.mdmusfikurrahaman.callrecordingapp-exoplayer-ui-2.19.1-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16b8dac02a484144e9b877600396f6d3\transformed\exoplayer-ui-2.19.1\res
com.mdmusfikurrahaman.callrecordingapp-transition-1.4.1-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b594ecb4ebd7d1c3f2e94e75b98737e\transformed\transition-1.4.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-release-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26e83ea22611f2c3e7a7fb951b84c34d\transformed\lifecycle-viewmodel-release\res
com.mdmusfikurrahaman.callrecordingapp-cardview-1.0.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27a11efe7203645011845d5d1ca14ece\transformed\cardview-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-savedstate-release-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\298c23db24cf56ae08de2553787fb3d8\transformed\lifecycle-viewmodel-savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-runtime-release-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c0ba3f0c50f98f88f8c6d4d99e0a11\transformed\lifecycle-runtime-release\res
com.mdmusfikurrahaman.callrecordingapp-coordinatorlayout-1.2.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31c7e5664ef39c0a18d3b3ddb9909661\transformed\coordinatorlayout-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-slidingpanelayout-1.2.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\379735225bb6668911f7c8d2a479210c\transformed\slidingpanelayout-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-fragment-ktx-1.6.2-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dc7eb056b660fbe2c8ce673b42e4d67\transformed\fragment-ktx-1.6.2\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-resources-1.6.1-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f318696097e153bba7c8055ba51fc2e\transformed\appcompat-resources-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-core-1.16.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f319f1baa5473100673c9c69aa9ac79\transformed\core-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-release-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f92f9af74e3aaf10344f71da6e0e0f0\transformed\savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-2.9.1-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\444d182b06ec144f713384c1ad49b6d5\transformed\lifecycle-livedata-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-1.6.1-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46864e7f8e3972dd5b88a3c5321cb854\transformed\appcompat-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-sqlite-2.4.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d5886b5b0a4b3454933e0212d32a84d\transformed\sqlite-2.4.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-ktx-2.7.6-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4def19185d615ed27d3d5d0fe1ec5fd4\transformed\navigation-common-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-room-runtime-2.6.1-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f5e841102da3d653c64fdcb63e9ad42\transformed\room-runtime-2.6.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-service-2.9.1-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5348b409ec7aefb2d3889d9b1681831f\transformed\lifecycle-service-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-databinding-runtime-8.9.2-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56172adef7cc12b8e15a5c96b53dfc6d\transformed\databinding-runtime-8.9.2\res
com.mdmusfikurrahaman.callrecordingapp-customview-poolingcontainer-1.0.0-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\575e1bec40cb225d4501af24db426e2b\transformed\customview-poolingcontainer-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-material-1.11.0-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e5330ea37f1d0a4ed46f4a86900feb4\transformed\material-1.11.0\res
com.mdmusfikurrahaman.callrecordingapp-window-1.0.0-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68df6618fc4cbd57eb5948a610a8ab26\transformed\window-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-core-ktx-1.16.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d267f1405bf59528e7248fca1be7c58\transformed\core-ktx-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-constraintlayout-2.1.4-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\732e256b958928f5597c0b5b5d201eb6\transformed\constraintlayout-2.1.4\res
com.mdmusfikurrahaman.callrecordingapp-navigation-ui-ktx-2.7.6-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ae340efcbf554f8b310e3624932a8d\transformed\navigation-ui-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-drawerlayout-1.1.1-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c855c931b6d64414c7303062caa4ad\transformed\drawerlayout-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-profileinstaller-1.4.0-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86886142d67612e841804a9de58fb7f4\transformed\profileinstaller-1.4.0\res
com.mdmusfikurrahaman.callrecordingapp-core-runtime-2.2.0-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\890cd1f5ac7da60f51332e9c5e5ade45\transformed\core-runtime-2.2.0\res
com.mdmusfikurrahaman.callrecordingapp-room-ktx-2.6.1-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bf3aadf8752a0fb4baaed0404fcf951\transformed\room-ktx-2.6.1\res
com.mdmusfikurrahaman.callrecordingapp-exoplayer-core-2.19.1-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944d23d0a2a0a4de0e44ec3c7bd88e96\transformed\exoplayer-core-2.19.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-fragment-2.7.6-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f7a790844c605e4d7ad8cce076370b\transformed\navigation-fragment-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-activity-1.8.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\965b6545637dcbeb742a4cc30cca84d4\transformed\activity-1.8.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-2.7.6-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99a13d9491f1ed0281a7410749213ecc\transformed\navigation-runtime-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-viewpager2-1.1.0-beta02-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad6060d0fec0ce8b877e62ca1e29f41b\transformed\viewpager2-1.1.0-beta02\res
com.mdmusfikurrahaman.callrecordingapp-core-viewtree-1.0.0-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad9f80662281f027a567ab49f793a044\transformed\core-viewtree-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-fragment-1.6.2-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adb51a7e6b5f714eda15dc4696925b87\transformed\fragment-1.6.2\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-ktx-2.9.1-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8058f69873396ca1bdfc08415001e0\transformed\lifecycle-livedata-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-ktx-2.7.6-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b77400d7b7f49ad7737be6fd94e8a6e9\transformed\navigation-runtime-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-swiperefreshlayout-1.1.0-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c35df0f83472824d43979229151684fc\transformed\swiperefreshlayout-1.1.0\res
com.mdmusfikurrahaman.callrecordingapp-activity-ktx-1.8.0-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8be88397ed354ff354a57bb4a1c1ec9\transformed\activity-ktx-1.8.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-ktx-2.9.1-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cfe8595f4c83a5affcce580f6468af53\transformed\lifecycle-viewmodel-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-ktx-1.3.0-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4ee22aafe8405c9952dfc7edd6eda01\transformed\savedstate-ktx-1.3.0\res
com.mdmusfikurrahaman.callrecordingapp-media-1.6.0-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da38c774e011b0f2076c2e0427b9dd67\transformed\media-1.6.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-ktx-2.9.1-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc8f95dbee17f48ec614f512b102c9ad\transformed\lifecycle-livedata-core-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-tracing-1.2.0-50 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dea95eef174924418c90b4347bea055b\transformed\tracing-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-2.9.1-51 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea9826dffd13d49fe6f54aa835508540\transformed\lifecycle-livedata-core-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-1.2.0-52 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed42fd7d1a457f5a5acc81006cf13e2c\transformed\emoji2-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-annotation-experimental-1.4.1-53 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0a6d184abbff2ecdac525e8d1f09511\transformed\annotation-experimental-1.4.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-fragment-ktx-2.7.6-54 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f37b319b4dcbff051679aba76f8b692d\transformed\navigation-fragment-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-navigation-ui-2.7.6-55 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f743c2e5f2a61bc0a20ac96fdd74bd6f\transformed\navigation-ui-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-startup-runtime-1.1.1-56 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fe2b0b79fe4f2684a0b0fe1a64bd2977\transformed\startup-runtime-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-pngs-57 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\pngs\debug
com.mdmusfikurrahaman.callrecordingapp-resValues-58 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\debug
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-59 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-60 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.mdmusfikurrahaman.callrecordingapp-debug-61 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\merged_res\debug\mergeDebugResources
com.mdmusfikurrahaman.callrecordingapp-debug-62 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\debug\res
com.mdmusfikurrahaman.callrecordingapp-main-63 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res
