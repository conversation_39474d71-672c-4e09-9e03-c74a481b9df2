com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-release-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02cf662b1bf1232ca43317f6491cc6b0\transformed\lifecycle-viewmodel-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-runtime-release-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0703177d8177160d42923c6c418edc2a\transformed\lifecycle-runtime-release\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-ktx-1.3.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ebcc7f671fcf0b0a1d7c7b53dfb19f4\transformed\savedstate-ktx-1.3.0\res
com.mdmusfikurrahaman.callrecordingapp-fragment-ktx-1.6.2-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f076c73e03bba59123700d600aea1ea\transformed\fragment-ktx-1.6.2\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-2.9.1-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2830c8c64016f21bcda45c8cc9fe67da\transformed\lifecycle-livedata-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-fragment-1.6.2-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ade421e7e5e7dffd02a0b352f4b3d71\transformed\fragment-1.6.2\res
com.mdmusfikurrahaman.callrecordingapp-transition-1.4.1-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cffaf4642cd3bcd2f150c02d468804d\transformed\transition-1.4.1\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-1.6.1-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d598d56b858fa6016015791eed03ccf\transformed\appcompat-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-release-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\493dbf968bc7e8f374b2f24bfad753e6\transformed\savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-ktx-2.9.1-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49a3e0468e9a2cd299a9424678f66b41\transformed\lifecycle-livedata-core-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-core-1.16.0-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bf0b208e36a6980b2928b48f859cb61\transformed\core-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-databinding-adapters-8.9.2-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e2928ef343fb36812978bc9c5f46f32\transformed\databinding-adapters-8.9.2\res
com.mdmusfikurrahaman.callrecordingapp-activity-ktx-1.8.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b7d97d28c33b2e50dca2f3d17d2df2e\transformed\activity-ktx-1.8.0\res
com.mdmusfikurrahaman.callrecordingapp-core-viewtree-1.0.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\695e33b6e823c05cd29a250a796c23cc\transformed\core-viewtree-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-swiperefreshlayout-1.1.0-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6a34035d3356a771830599d0ded0a1bc\transformed\swiperefreshlayout-1.1.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-ui-2.7.6-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c40f6e097782dd91ae46759245f7394\transformed\navigation-ui-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-2.9.1-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6cb76bc73dbb46ee29ad0d98e414e7a5\transformed\lifecycle-livedata-core-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-service-2.9.1-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\712d09a49c7cb2c80955f7edc9a068dd\transformed\lifecycle-service-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-slidingpanelayout-1.2.0-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74c5cbdb44cb649913f1f59c606e8d84\transformed\slidingpanelayout-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-startup-runtime-1.1.1-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76469d4211962524d8fdd48679abd46a\transformed\startup-runtime-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-drawerlayout-1.1.1-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e2ec0218af4d3229aea80eb9695bb8\transformed\drawerlayout-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-material-1.11.0-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7af83f23284e2be0f9565d61e3558dd9\transformed\material-1.11.0\res
com.mdmusfikurrahaman.callrecordingapp-profileinstaller-1.4.0-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923a667a319e01c3a32bd3605fb62be1\transformed\profileinstaller-1.4.0\res
com.mdmusfikurrahaman.callrecordingapp-databinding-runtime-8.9.2-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e6eb137d314c27724e9c7b7813b6ea\transformed\databinding-runtime-8.9.2\res
com.mdmusfikurrahaman.callrecordingapp-core-runtime-2.2.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9675d8e73313f46cd1e967ac21f73f45\transformed\core-runtime-2.2.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-2.7.6-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\978781f658010815f57db84b49727ea2\transformed\navigation-runtime-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-ktx-2.7.6-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97d4ba6f42ffb9e4a101c855b9f96d7e\transformed\navigation-common-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-ktx-2.9.1-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cc47d038e2f5c0a45ce4a580b1acf0d\transformed\lifecycle-viewmodel-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-window-1.0.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d982f42ecdb2fa981cd79d4a47a54ed\transformed\window-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-1.2.0-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a17a9a5a3ccadb7245833f5233d84ffe\transformed\emoji2-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-constraintlayout-2.1.4-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1fbd5232aa1bfba3e42472a531ff534\transformed\constraintlayout-2.1.4\res
com.mdmusfikurrahaman.callrecordingapp-tracing-1.2.0-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaf55a25a3523d3483751b3e24cfe1c2\transformed\tracing-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-viewpager2-1.1.0-beta02-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae51cbd06adae738a99a77820185cacf\transformed\viewpager2-1.1.0-beta02\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-savedstate-release-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2117a8a6268ca8dbc86067d4111f1a1\transformed\lifecycle-viewmodel-savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-core-ktx-1.16.0-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3a49d49a44a6dca58de894ed1c01245\transformed\core-ktx-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-annotation-experimental-1.4.1-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd87069ebed95de5c0deb4f084e076cf\transformed\annotation-experimental-1.4.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-ktx-2.7.6-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1d0c7ee09b5147f4f8895c243054240\transformed\navigation-runtime-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-customview-poolingcontainer-1.0.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6cce96d63014f9adb6352799e9caa3b\transformed\customview-poolingcontainer-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-process-2.9.1-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb97b6678ad1a5ccbc700dde9d81fdce\transformed\lifecycle-process-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-fragment-ktx-2.7.6-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cccbfb14846f4723ac90ef7d73ad8b01\transformed\navigation-fragment-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-recyclerview-1.3.2-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d341764902c8e59459a02984fad0c42a\transformed\recyclerview-1.3.2\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-views-helper-1.2.0-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c1bc2640a3e9f3440730b1a711f763\transformed\emoji2-views-helper-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-cardview-1.0.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d58aaff12fbb27b2e73296a87a10e1b4\transformed\cardview-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-resources-1.6.1-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d97e0e14bcfda3056fe0b88ff9c1952f\transformed\appcompat-resources-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-coordinatorlayout-1.2.0-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db2377fb9da9c28d1b55176a318957f3\transformed\coordinatorlayout-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-2.7.6-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e50c89fbfacb8b94174d4be304a78b5b\transformed\navigation-common-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-ktx-2.9.1-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7a72867a4730a959b503570dfba550f\transformed\lifecycle-livedata-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-activity-1.8.0-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebc6ed04641d4b026dac658965193bbd\transformed\activity-1.8.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-fragment-2.7.6-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed80c35e421a6be559b9048e05311a47\transformed\navigation-fragment-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-navigation-ui-ktx-2.7.6-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2824b906daf084bd6cf790a70511669\transformed\navigation-ui-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-pngs-50 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\pngs\debug
com.mdmusfikurrahaman.callrecordingapp-resValues-51 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\debug
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-52 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-53 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.mdmusfikurrahaman.callrecordingapp-debug-54 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\merged_res\debug\mergeDebugResources
com.mdmusfikurrahaman.callrecordingapp-debug-55 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\debug\res
com.mdmusfikurrahaman.callrecordingapp-main-56 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res
