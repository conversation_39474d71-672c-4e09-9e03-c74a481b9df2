com.mdmusfikurrahaman.callrecordingapp-lifecycle-runtime-release-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02ea201e9bb3b55675877c0cb9193573\transformed\lifecycle-runtime-release\res
com.mdmusfikurrahaman.callrecordingapp-foundation-release-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060f7bae30cd6eaca84919e959bbcf8e\transformed\foundation-release\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-views-helper-1.3.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b73d6fe7341ebd4a903e0e62b164ddf\transformed\emoji2-views-helper-1.3.0\res
com.mdmusfikurrahaman.callrecordingapp-sqlite-2.4.0-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0be67bf91164c0f46f9865096886e4f6\transformed\sqlite-2.4.0\res
com.mdmusfikurrahaman.callrecordingapp-runtime-saveable-release-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c0f6e75be9e526d1df5e0931da20e85\transformed\runtime-saveable-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-release-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0caafe874001aa745e69129a07aec6ad\transformed\lifecycle-viewmodel-release\res
com.mdmusfikurrahaman.callrecordingapp-material3-release-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f0d934159a745b77ce5e2c937655ae7\transformed\material3-release\res
com.mdmusfikurrahaman.callrecordingapp-ui-release-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f6b081858863bf6ac83d3e56dce9679\transformed\ui-release\res
com.mdmusfikurrahaman.callrecordingapp-media-1.6.0-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f73f3a5398eee140153949db3abbd04\transformed\media-1.6.0\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-release-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1182d5d1bbc868453e478891bcc4e878\transformed\savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-constraintlayout-2.0.1-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1555366647f964f993feaf4048cda5c2\transformed\constraintlayout-2.0.1\res
com.mdmusfikurrahaman.callrecordingapp-viewpager2-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a56e6f83f69f990f170da7dd43f3f60\transformed\viewpager2-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-1.3.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ba6ee4b187ae5255edcbbf5f22b6308\transformed\emoji2-1.3.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-savedstate-release-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2369d583edc5ede10c1242d0609366fc\transformed\lifecycle-viewmodel-savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-2.9.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253abac796458b2537b8cd04c458a32f\transformed\lifecycle-livedata-core-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-tracing-1.2.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26b9e3ea960904b97d62c8c2c4de63a0\transformed\tracing-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-material-release-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a43f017882f66653ee8361060ded215\transformed\material-release\res
com.mdmusfikurrahaman.callrecordingapp-foundation-layout-release-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\346f73fdf4db2af5e066d3593cf98121\transformed\foundation-layout-release\res
com.mdmusfikurrahaman.callrecordingapp-exoplayer-core-2.19.1-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37884121572ee391713c499f6dfa99b6\transformed\exoplayer-core-2.19.1\res
com.mdmusfikurrahaman.callrecordingapp-core-1.16.0-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bbdb89120b629f45d0a6e4e4788a826\transformed\core-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-profileinstaller-1.4.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f59fdea12b1fb340578731e1ca1bb7b\transformed\profileinstaller-1.4.0\res
com.mdmusfikurrahaman.callrecordingapp-activity-ktx-1.10.1-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c5167891782365507af10137fff434\transformed\activity-ktx-1.10.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-2.8.5-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43451c4cf576c4ca58d679413c1217ee\transformed\navigation-runtime-2.8.5\res
com.mdmusfikurrahaman.callrecordingapp-ui-unit-release-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44bd67fe1d0252bc4663d488a85dbd1e\transformed\ui-unit-release\res
com.mdmusfikurrahaman.callrecordingapp-animation-core-release-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4729f1ee5419b552b8b017a3211929f9\transformed\animation-core-release\res
com.mdmusfikurrahaman.callrecordingapp-ui-geometry-release-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4767a0eaec58b98c57a7c7084cad69cc\transformed\ui-geometry-release\res
com.mdmusfikurrahaman.callrecordingapp-exoplayer-ui-2.19.1-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\494bfdab15dca442fa2e05e43f86b4ff\transformed\exoplayer-ui-2.19.1\res
com.mdmusfikurrahaman.callrecordingapp-room-runtime-2.6.1-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54ee7c8a6a2a9d22b501657a631edb9b\transformed\room-runtime-2.6.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-ktx-2.9.1-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5581cacaac74ad0e05c7f6643910a800\transformed\lifecycle-viewmodel-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-ui-util-release-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a4b479057fe37f7eb3a9066e0f99bd4\transformed\ui-util-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-process-2.9.1-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b00567f0d72eb657766b1fecec6ecc1\transformed\lifecycle-process-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-ui-text-release-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b42dad7c23b1308a3238d69bb6fed4b\transformed\ui-text-release\res
com.mdmusfikurrahaman.callrecordingapp-ui-graphics-release-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bd6aef088fbfce15a74175d5a5d4fa6\transformed\ui-graphics-release\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-ktx-1.3.0-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61fc0dc2a1f6804b5f71693c289df2ca\transformed\savedstate-ktx-1.3.0\res
com.mdmusfikurrahaman.callrecordingapp-activity-1.10.1-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62ec83f1185741b584d6d3f738481309\transformed\activity-1.10.1\res
com.mdmusfikurrahaman.callrecordingapp-recyclerview-1.3.0-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64daf04d4922015e37dc200ad2ff335c\transformed\recyclerview-1.3.0\res
com.mdmusfikurrahaman.callrecordingapp-ui-tooling-release-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6538381d99b4a5d14910578ae071a0e3\transformed\ui-tooling-release\res
com.mdmusfikurrahaman.callrecordingapp-navigation-compose-2.8.5-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f7942a7da1133b37f2dc2f0a00185d1\transformed\navigation-compose-2.8.5\res
com.mdmusfikurrahaman.callrecordingapp-core-viewtree-1.0.0-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\749be79bf2ac10b23bb300822828318a\transformed\core-viewtree-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-ui-tooling-data-release-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f20658b89fc3f01924bca0353d78a86\transformed\ui-tooling-data-release\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-2.8.5-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811b1ce9077fecb6340e6d12cb4fa636\transformed\navigation-common-2.8.5\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-1.6.1-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87d78cd91b4d183bfa1262f9176c8808\transformed\appcompat-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-customview-poolingcontainer-1.0.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b980f2eb9d27b8cfd48cb43a893c235\transformed\customview-poolingcontainer-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-coordinatorlayout-1.1.0-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d6467f6d4cc5d00a97e1927b19c497b\transformed\coordinatorlayout-1.1.0\res
com.mdmusfikurrahaman.callrecordingapp-runtime-release-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92882136cda3072b391319ae7a23b662\transformed\runtime-release\res
com.mdmusfikurrahaman.callrecordingapp-material-icons-core-release-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\982b3ba6bae1ed151cdcdc2b94f499af\transformed\material-icons-core-release\res
com.mdmusfikurrahaman.callrecordingapp-material-1.11.0-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e314a50eb315442fe0afa641180e00d\transformed\material-1.11.0\res
com.mdmusfikurrahaman.callrecordingapp-core-ktx-1.16.0-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f38e18cc05604e09bd8dc2b34c49a57\transformed\core-ktx-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-ui-test-manifest-1.7.8-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a0f51aec1cf4fb4e36116567f10e9c36\transformed\ui-test-manifest-1.7.8\res
com.mdmusfikurrahaman.callrecordingapp-hilt-navigation-compose-1.2.0-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2297764476b0085eff92b7df0d84df7\transformed\hilt-navigation-compose-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-resources-1.6.1-50 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6752dc6b72dd7bbb78bd362d3e2d21e\transformed\appcompat-resources-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-drawerlayout-1.1.1-51 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87745c7ebbabc59fd9697939087eba3\transformed\drawerlayout-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-annotation-experimental-1.4.1-52 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aec222c748024f46caa1cf84ecf12add\transformed\annotation-experimental-1.4.1\res
com.mdmusfikurrahaman.callrecordingapp-animation-release-53 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4662a7cbcfa79b803217b6ed865c8a4\transformed\animation-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-runtime-compose-release-54 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b71ed6f76af2e64363dc91344784afe6\transformed\lifecycle-runtime-compose-release\res
com.mdmusfikurrahaman.callrecordingapp-material-ripple-release-55 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafb0d57b3d20b66636abeb5c0403428\transformed\material-ripple-release\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-ktx-2.9.1-56 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb744c244286e105597223881c409671\transformed\lifecycle-livedata-core-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-startup-runtime-1.1.1-57 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc24f293396ce494ac1c8a020bc920ac\transformed\startup-runtime-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-cardview-1.0.0-58 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc51dbf00f202b78ff7276406926514a\transformed\cardview-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-hilt-navigation-1.2.0-59 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf5072ae2a3dd00a05987e4b9853314d\transformed\hilt-navigation-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-sqlite-framework-2.4.0-60 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7dbe06a0bbe1db7492a30b03996a048\transformed\sqlite-framework-2.4.0\res
com.mdmusfikurrahaman.callrecordingapp-core-runtime-2.2.0-61 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d43a7cf14442012bd33dea247a134955\transformed\core-runtime-2.2.0\res
com.mdmusfikurrahaman.callrecordingapp-graphics-path-1.0.1-62 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e18e2e42bcb2da444e71f8d08331114e\transformed\graphics-path-1.0.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-compose-release-63 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e25f31d033dabb3e70e33f4bda4af09d\transformed\lifecycle-viewmodel-compose-release\res
com.mdmusfikurrahaman.callrecordingapp-fragment-1.5.1-64 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e72416f097544af7c7b4a70ec06ad5f5\transformed\fragment-1.5.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-2.9.1-65 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8b2c05cb8e30343c0d08a9b5ee322bb\transformed\lifecycle-livedata-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-transition-1.2.0-66 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5e5d8aaa040e560f332430c6f53500\transformed\transition-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-activity-compose-1.10.1-67 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ca8b24603cb6f35d2957243908062a\transformed\activity-compose-1.10.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-ktx-2.8.5-68 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5de16a5961e9a8a0abfabe68f66733d\transformed\navigation-common-ktx-2.8.5\res
com.mdmusfikurrahaman.callrecordingapp-ui-tooling-preview-release-69 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f62f63e997d45c40d2117e611c0a5f15\transformed\ui-tooling-preview-release\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-ktx-2.8.5-70 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc744377875fb90cf4343be0bd4278b3\transformed\navigation-runtime-ktx-2.8.5\res
com.mdmusfikurrahaman.callrecordingapp-room-ktx-2.6.1-71 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff937bdf593fc05465a9a8ec24a49e33\transformed\room-ktx-2.6.1\res
com.mdmusfikurrahaman.callrecordingapp-pngs-72 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\pngs\debug
com.mdmusfikurrahaman.callrecordingapp-resValues-73 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\debug
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-74 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.mdmusfikurrahaman.callrecordingapp-packageDebugResources-75 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.mdmusfikurrahaman.callrecordingapp-debug-76 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\merged_res\debug\mergeDebugResources
com.mdmusfikurrahaman.callrecordingapp-debug-77 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\debug\res
com.mdmusfikurrahaman.callrecordingapp-main-78 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res
