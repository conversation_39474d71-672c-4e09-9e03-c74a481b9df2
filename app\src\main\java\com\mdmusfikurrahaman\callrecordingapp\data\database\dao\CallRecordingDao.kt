package com.mdmusfikurrahaman.callrecordingapp.data.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallRecording
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType

/**
 * Data Access Object for CallRecording entity
 * 
 * Provides methods to interact with call recordings in the database
 * including CRUD operations, search, and filtering capabilities
 */
@Dao
interface CallRecordingDao {
    
    /**
     * Get all call recordings ordered by timestamp (newest first)
     */
    @Query("SELECT * FROM call_recordings ORDER BY timestamp DESC")
    fun getAllRecordings(): Flow<List<CallRecording>>
    
    /**
     * Get a specific recording by ID
     */
    @Query("SELECT * FROM call_recordings WHERE id = :id")
    suspend fun getRecordingById(id: Long): CallRecording?
    
    /**
     * Get recordings by phone number
     */
    @Query("SELECT * FROM call_recordings WHERE phone_number = :phoneNumber ORDER BY timestamp DESC")
    fun getRecordingsByPhoneNumber(phoneNumber: String): Flow<List<CallRecording>>
    
    /**
     * Get recordings by call type
     */
    @Query("SELECT * FROM call_recordings WHERE call_type = :callType ORDER BY timestamp DESC")
    fun getRecordingsByCallType(callType: CallType): Flow<List<CallRecording>>
    
    /**
     * Search recordings by contact name or phone number
     */
    @Query("""
        SELECT * FROM call_recordings 
        WHERE contact_name LIKE '%' || :query || '%' 
        OR phone_number LIKE '%' || :query || '%'
        ORDER BY timestamp DESC
    """)
    fun searchRecordings(query: String): Flow<List<CallRecording>>
    
    /**
     * Get recordings within a date range
     */
    @Query("""
        SELECT * FROM call_recordings 
        WHERE timestamp BETWEEN :startTime AND :endTime 
        ORDER BY timestamp DESC
    """)
    fun getRecordingsInDateRange(startTime: Long, endTime: Long): Flow<List<CallRecording>>
    
    /**
     * Get favorite recordings
     */
    @Query("SELECT * FROM call_recordings WHERE is_favorite = 1 ORDER BY timestamp DESC")
    fun getFavoriteRecordings(): Flow<List<CallRecording>>
    
    /**
     * Get recordings with transcripts
     */
    @Query("SELECT * FROM call_recordings WHERE has_transcript = 1 ORDER BY timestamp DESC")
    fun getRecordingsWithTranscripts(): Flow<List<CallRecording>>
    
    /**
     * Get recordings older than specified timestamp (for cleanup)
     */
    @Query("SELECT * FROM call_recordings WHERE timestamp < :timestamp")
    suspend fun getRecordingsOlderThan(timestamp: Long): List<CallRecording>
    
    /**
     * Get total storage used by recordings
     */
    @Query("SELECT SUM(file_size) FROM call_recordings")
    suspend fun getTotalStorageUsed(): Long?
    
    /**
     * Get recording count
     */
    @Query("SELECT COUNT(*) FROM call_recordings")
    suspend fun getRecordingCount(): Int
    
    /**
     * Insert a new recording
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecording(recording: CallRecording): Long
    
    /**
     * Insert multiple recordings
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecordings(recordings: List<CallRecording>)
    
    /**
     * Update an existing recording
     */
    @Update
    suspend fun updateRecording(recording: CallRecording)
    
    /**
     * Update favorite status
     */
    @Query("UPDATE call_recordings SET is_favorite = :isFavorite, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateFavoriteStatus(id: Long, isFavorite: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update transcript status
     */
    @Query("UPDATE call_recordings SET has_transcript = :hasTranscript, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateTranscriptStatus(id: Long, hasTranscript: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Delete a recording
     */
    @Delete
    suspend fun deleteRecording(recording: CallRecording)
    
    /**
     * Delete recording by ID
     */
    @Query("DELETE FROM call_recordings WHERE id = :id")
    suspend fun deleteRecordingById(id: Long)
    
    /**
     * Delete recordings older than specified timestamp
     */
    @Query("DELETE FROM call_recordings WHERE timestamp < :timestamp")
    suspend fun deleteRecordingsOlderThan(timestamp: Long): Int
    
    /**
     * Delete all recordings
     */
    @Query("DELETE FROM call_recordings")
    suspend fun deleteAllRecordings()
}
