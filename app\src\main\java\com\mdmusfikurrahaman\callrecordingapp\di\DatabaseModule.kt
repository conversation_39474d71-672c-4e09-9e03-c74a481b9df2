package com.mdmusfikurrahaman.callrecordingapp.di

import android.content.Context
import androidx.room.Room
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.data.database.CallRecordingDatabase
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.CallRecordingDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.CallTranscriptDao
import com.mdmusfikurrahaman.callrecordingapp.data.database.dao.UserPreferencesDao

/**
 * Hilt module for providing database-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideCallRecordingDatabase(
        @ApplicationContext context: Context
    ): CallRecordingDatabase {
        return CallRecordingDatabase.getDatabase(context)
    }
    
    @Provides
    fun provideCallRecordingDao(database: CallRecordingDatabase): CallRecordingDao {
        return database.callRecordingDao()
    }
    
    @Provides
    fun provideCallTranscriptDao(database: CallRecordingDatabase): CallTranscriptDao {
        return database.callTranscriptDao()
    }
    
    @Provides
    fun provideUserPreferencesDao(database: CallRecordingDatabase): UserPreferencesDao {
        return database.userPreferencesDao()
    }
}
