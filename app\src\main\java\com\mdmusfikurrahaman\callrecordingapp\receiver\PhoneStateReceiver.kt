package com.mdmusfikurrahaman.callrecordingapp.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.telephony.TelephonyManager
import timber.log.Timber
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import com.mdmusfikurrahaman.callrecordingapp.data.database.entity.CallType
import com.mdmusfikurrahaman.callrecordingapp.service.CallRecordingService
import com.mdmusfikurrahaman.callrecordingapp.util.ContactUtils
import com.mdmusfikurrahaman.callrecordingapp.util.CallStateManager

/**
 * BroadcastReceiver for detecting phone state changes
 * 
 * This receiver listens for phone state changes and triggers call recording
 * when calls are detected. It handles both incoming and outgoing calls.
 */
class PhoneStateReceiver : BroadcastReceiver() {
    
    private val receiverScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            when (intent.action) {
                TelephonyManager.ACTION_PHONE_STATE_CHANGED -> {
                    handlePhoneStateChanged(context, intent)
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error in PhoneStateReceiver")
        }
    }
    
    private fun handlePhoneStateChanged(context: Context, intent: Intent) {
        val state = intent.getStringExtra(TelephonyManager.EXTRA_STATE)
        val phoneNumber = intent.getStringExtra(TelephonyManager.EXTRA_INCOMING_NUMBER)
        
        Timber.d("Phone state changed: $state, number: $phoneNumber")
        
        when (state) {
            TelephonyManager.EXTRA_STATE_RINGING -> {
                // Incoming call detected
                handleIncomingCall(context, phoneNumber)
            }
            TelephonyManager.EXTRA_STATE_OFFHOOK -> {
                // Call answered or outgoing call started
                handleCallStarted(context, phoneNumber)
            }
            TelephonyManager.EXTRA_STATE_IDLE -> {
                // Call ended
                handleCallEnded(context)
            }
        }
    }
    
    private fun handleIncomingCall(context: Context, phoneNumber: String?) {
        if (phoneNumber.isNullOrBlank()) return
        
        receiverScope.launch {
            try {
                // Store the incoming call information
                CallStateManager.setIncomingCall(phoneNumber)
                
                Timber.d("Incoming call detected: $phoneNumber")
                
            } catch (e: Exception) {
                Timber.e(e, "Error handling incoming call")
            }
        }
    }
    
    private fun handleCallStarted(context: Context, phoneNumber: String?) {
        receiverScope.launch {
            try {
                val callInfo = CallStateManager.getCurrentCallInfo()
                val actualPhoneNumber = phoneNumber ?: callInfo?.phoneNumber
                
                if (actualPhoneNumber.isNullOrBlank()) {
                    Timber.w("No phone number available for call recording")
                    return@launch
                }
                
                // Determine call type
                val callType = if (CallStateManager.isIncomingCall(actualPhoneNumber)) {
                    CallType.INCOMING
                } else {
                    CallType.OUTGOING
                }
                
                // Get contact name
                val contactName = ContactUtils.getContactName(context, actualPhoneNumber)
                
                // Check if recording is enabled for this call type
                if (shouldRecordCall(context, callType)) {
                    startCallRecording(context, actualPhoneNumber, contactName, callType)
                }
                
                // Update call state
                CallStateManager.setCallActive(actualPhoneNumber, callType)
                
            } catch (e: Exception) {
                Timber.e(e, "Error handling call started")
            }
        }
    }
    
    private fun handleCallEnded(context: Context) {
        receiverScope.launch {
            try {
                val callInfo = CallStateManager.getCurrentCallInfo()
                
                if (callInfo != null) {
                    Timber.d("Call ended: ${callInfo.phoneNumber}")
                    
                    // Stop recording if active
                    stopCallRecording(context)
                    
                    // Clear call state
                    CallStateManager.clearCallState()
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error handling call ended")
            }
        }
    }
    
    private suspend fun shouldRecordCall(context: Context, callType: CallType): Boolean {
        return try {
            // This would check user preferences from the repository
            // For now, return true to enable recording
            true
        } catch (e: Exception) {
            Timber.e(e, "Error checking recording preferences")
            false
        }
    }
    
    private fun startCallRecording(
        context: Context,
        phoneNumber: String,
        contactName: String?,
        callType: CallType
    ) {
        try {
            CallRecordingService.startRecording(
                context = context,
                phoneNumber = phoneNumber,
                contactName = contactName,
                callType = callType
            )
            
            Timber.d("Started call recording for $phoneNumber ($callType)")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start call recording")
        }
    }
    
    private fun stopCallRecording(context: Context) {
        try {
            CallRecordingService.stopRecording(context)
            Timber.d("Stopped call recording")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to stop call recording")
        }
    }
}
