{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-74:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87d78cd91b4d183bfa1262f9176c8808\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1440,1542,1641,1727,1832,1953,2032,2108,2200,2294,2389,2482,2577,2671,2767,2862,2954,3046,3135,3241,3348,3446,3555,3662,3776,3942,21418", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "1435,1537,1636,1722,1827,1948,2027,2103,2195,2289,2384,2477,2572,2666,2762,2857,2949,3041,3130,3236,3343,3441,3550,3657,3771,3937,4037,21495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2132,2243,2360,2437,2532,2604,2677,2765,2853,2922,2987,3040,3102,3150,3211,3278,3346,3412,3494,3552,3609,3675,3740,3806,3858,3919,4004,4089", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2127,2238,2355,2432,2527,2599,2672,2760,2848,2917,2982,3035,3097,3145,3206,3273,3341,3407,3489,3547,3604,3670,3735,3801,3853,3914,3999,4084,4147"}, "to": {"startLines": "2,11,17,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,375,714,6058,6141,6224,6307,6397,6497,6568,6641,6740,6841,6914,6986,7051,7129,7241,7352,7469,7546,7641,7713,7786,7874,7962,8031,8763,8816,8878,8926,8987,9054,9122,9188,9270,9328,9385,9451,9516,9582,9634,9695,9780,9865", "endLines": "10,16,22,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "370,709,1039,6136,6219,6302,6392,6492,6563,6636,6735,6836,6909,6981,7046,7124,7236,7347,7464,7541,7636,7708,7781,7869,7957,8026,8091,8811,8873,8921,8982,9049,9117,9183,9265,9323,9380,9446,9511,9577,9629,9690,9775,9860,9923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,999,1086,1158,1234,1312,1388,1472,1542", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,75,77,75,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,994,1081,1153,1229,1307,1383,1467,1537,1660"}, "to": {"startLines": "71,72,73,74,75,130,131,245,246,248,249,251,252,253,254,256,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5524,5617,5700,5798,5900,10104,10186,20997,21085,21247,21331,21500,21572,21648,21726,21903,21987,22057", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,75,77,75,83,69,122", "endOffsets": "5612,5695,5793,5895,5987,10181,10271,21080,21162,21326,21413,21567,21643,21721,21797,21982,22052,22175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8096,8168,8229,8294,8360,8438,8512,8600,8686", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "8163,8224,8289,8355,8433,8507,8595,8681,8758"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "61,62,63,64,65,66,67,255", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4471,4569,4671,4772,4873,4978,5081,21802", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4564,4666,4767,4868,4973,5076,5193,21898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,721,835,976,1093,1233,1317,1415,1508,1606,1721,1844,1947,2076,2204,2330,2510,2634,2757,2884,3004,3098,3198,3319,3452,3550,3664,3771,3903,4041,4151,4251,4336,4431,4527,4650,4744,4831,4939,5019,5103,5201,5302,5396,5491,5579,5686,5784,5883,6030,6110,6216", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "168,284,402,520,619,716,830,971,1088,1228,1312,1410,1503,1601,1716,1839,1942,2071,2199,2325,2505,2629,2752,2879,2999,3093,3193,3314,3447,3545,3659,3766,3898,4036,4146,4246,4331,4426,4522,4645,4739,4826,4934,5014,5098,5196,5297,5391,5486,5574,5681,5779,5878,6025,6105,6211,6308"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10339,10457,10573,10691,10809,10908,11005,11119,11260,11377,11517,11601,11699,11792,11890,12005,12128,12231,12360,12488,12614,12794,12918,13041,13168,13288,13382,13482,13603,13736,13834,13948,14055,14187,14325,14435,14535,14620,14715,14811,14934,15028,15115,15223,15303,15387,15485,15586,15680,15775,15863,15970,16068,16167,16314,16394,16500", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "10452,10568,10686,10804,10903,11000,11114,11255,11372,11512,11596,11694,11787,11885,12000,12123,12226,12355,12483,12609,12789,12913,13036,13163,13283,13377,13477,13598,13731,13829,13943,14050,14182,14320,14430,14530,14615,14710,14806,14929,15023,15110,15218,15298,15382,15480,15581,15675,15770,15858,15965,16063,16162,16309,16389,16495,16592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e314a50eb315442fe0afa641180e00d\\transformed\\material-1.11.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1202,1301,1378,1441,1559,1620,1685,1742,1812,1873,1927,2043,2100,2162,2216,2290,2418,2506,2592,2729,2813,2898,3032,3123,3199,3253,3304,3370,3442,3520,3616,3698,3778,3854,3931,4008,4115,4204,4277,4367,4462,4536,4617,4710,4765,4846,4912,4998,5083,5145,5209,5272,5344,5442,5541,5636,5728,5786,5841", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1197,1296,1373,1436,1554,1615,1680,1737,1807,1868,1922,2038,2095,2157,2211,2285,2413,2501,2587,2724,2808,2893,3027,3118,3194,3248,3299,3365,3437,3515,3611,3693,3773,3849,3926,4003,4110,4199,4272,4362,4457,4531,4612,4705,4760,4841,4907,4993,5078,5140,5204,5267,5339,5437,5536,5631,5723,5781,5836,5916"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,76,128,129,132,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1044,4042,4120,4198,4282,4380,5198,5295,5432,5992,9928,10027,10276,16597,16715,16776,16841,16898,16968,17029,17083,17199,17256,17318,17372,17446,17574,17662,17748,17885,17969,18054,18188,18279,18355,18409,18460,18526,18598,18676,18772,18854,18934,19010,19087,19164,19271,19360,19433,19523,19618,19692,19773,19866,19921,20002,20068,20154,20239,20301,20365,20428,20500,20598,20697,20792,20884,20942,21167", "endLines": "28,56,57,58,59,60,68,69,70,76,128,129,132,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,247", "endColumns": "12,77,77,83,97,90,96,136,91,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,85,136,83,84,133,90,75,53,50,65,71,77,95,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79", "endOffsets": "1320,4115,4193,4277,4375,4466,5290,5427,5519,6053,10022,10099,10334,16710,16771,16836,16893,16963,17024,17078,17194,17251,17313,17367,17441,17569,17657,17743,17880,17964,18049,18183,18274,18350,18404,18455,18521,18593,18671,18767,18849,18929,19005,19082,19159,19266,19355,19428,19518,19613,19687,19768,19861,19916,19997,20063,20149,20234,20296,20360,20423,20495,20593,20692,20787,20879,20937,20992,21242"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "259,260", "startColumns": "4,4", "startOffsets": "22180,22268", "endColumns": "87,90", "endOffsets": "22263,22354"}}]}]}