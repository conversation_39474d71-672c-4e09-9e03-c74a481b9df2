package com.mdmusfikurrahaman.callrecordingapp.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import com.mdmusfikurrahaman.callrecordingapp.service.recording.RecordingMethodManager
import com.mdmusfikurrahaman.callrecordingapp.util.FileManager
import com.mdmusfikurrahaman.callrecordingapp.audio.AudioPlayerManager

/**
 * Hilt module for providing service-related dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object ServiceModule {
    
    @Provides
    @Singleton
    fun provideRecordingMethodManager(
        @ApplicationContext context: Context
    ): RecordingMethodManager {
        return RecordingMethodManager(context)
    }
    
    @Provides
    @Singleton
    fun provideFileManager(
        @ApplicationContext context: Context
    ): FileManager {
        return FileManager(context)
    }

    @Provides
    @Singleton
    fun provideAudioPlayerManager(
        @ApplicationContext context: Context
    ): AudioPlayerManager {
        return AudioPlayerManager(context)
    }
}
