{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-74:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e314a50eb315442fe0afa641180e00d\\transformed\\material-1.11.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1047,1136,1205,1264,1359,1425,1490,1548,1613,1674,1734,1840,1901,1961,2019,2090,2209,2295,2377,2520,2595,2671,2802,2892,2970,3025,3080,3146,3215,3289,3368,3447,3520,3597,3666,3736,3833,3918,3993,4086,4179,4253,4322,4416,4468,4551,4618,4702,4786,4848,4912,4975,5045,5144,5242,5337,5431,5490,5549", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "264,338,409,490,576,659,774,893,976,1042,1131,1200,1259,1354,1420,1485,1543,1608,1669,1729,1835,1896,1956,2014,2085,2204,2290,2372,2515,2590,2666,2797,2887,2965,3020,3075,3141,3210,3284,3363,3442,3515,3592,3661,3731,3828,3913,3988,4081,4174,4248,4317,4411,4463,4546,4613,4697,4781,4843,4907,4970,5040,5139,5237,5332,5426,5485,5544,5623"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,3586,3660,3731,3812,3898,4687,4802,4921,5467,9213,9302,9550,15698,15793,15859,15924,15982,16047,16108,16168,16274,16335,16395,16453,16524,16643,16729,16811,16954,17029,17105,17236,17326,17404,17459,17514,17580,17649,17723,17802,17881,17954,18031,18100,18170,18267,18352,18427,18520,18613,18687,18756,18850,18902,18985,19052,19136,19220,19282,19346,19409,19479,19578,19676,19771,19865,19924,20152", "endLines": "22,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "914,3655,3726,3807,3893,3976,4797,4916,4999,5528,9297,9366,9604,15788,15854,15919,15977,16042,16103,16163,16269,16330,16390,16448,16519,16638,16724,16806,16949,17024,17100,17231,17321,17399,17454,17509,17575,17644,17718,17797,17876,17949,18026,18095,18165,18262,18347,18422,18515,18608,18682,18751,18845,18897,18980,19047,19131,19215,19277,19341,19404,19474,19573,19671,19766,19860,19919,19978,20226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "253,254", "startColumns": "4,4", "startOffsets": "21148,21234", "endColumns": "85,84", "endOffsets": "21229,21314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87d78cd91b4d183bfa1262f9176c8808\\transformed\\appcompat-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1022,1125,1238,1323,1427,1538,1616,1693,1784,1877,1969,2063,2163,2256,2351,2447,2538,2629,2710,2817,2921,3019,3122,3226,3330,3487,20395", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "1017,1120,1233,1318,1422,1533,1611,1688,1779,1872,1964,2058,2158,2251,2346,2442,2533,2624,2705,2812,2916,3014,3117,3221,3325,3482,3581,20472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,996,1080,1154,1235,1310,1385,1463,1529", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,80,74,74,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,991,1075,1149,1230,1305,1380,1458,1524,1645"}, "to": {"startLines": "65,66,67,68,69,124,125,239,240,242,243,245,246,247,248,250,251,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5004,5094,5171,5280,5378,9371,9460,19983,20069,20231,20311,20477,20551,20632,20707,20883,20961,21027", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,80,74,74,77,65,120", "endOffsets": "5089,5166,5275,5373,5462,9455,9545,20064,20147,20306,20390,20546,20627,20702,20777,20956,21022,21143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1766,1873,1983,2051,2128,2198,2274,2358,2440,2502,2565,2618,2676,2724,2785,2844,2912,2973,3039,3103,3162,3226,3293,3360,3414,3474,3548,3622", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1761,1868,1978,2046,2123,2193,2269,2353,2435,2497,2560,2613,2671,2719,2780,2839,2907,2968,3034,3098,3157,3221,3288,3355,3409,3469,3543,3617,3673"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,572,5533,5615,5695,5772,5860,5942,6018,6082,6175,6267,6337,6401,6464,6534,6644,6751,6861,6929,7006,7076,7152,7236,7318,7380,8100,8153,8211,8259,8320,8379,8447,8508,8574,8638,8697,8761,8828,8895,8949,9009,9083,9157", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "376,567,745,5610,5690,5767,5855,5937,6013,6077,6170,6262,6332,6396,6459,6529,6639,6746,6856,6924,7001,7071,7147,7231,7313,7375,7438,8148,8206,8254,8315,8374,8442,8503,8569,8633,8692,8756,8823,8890,8944,9004,9078,9152,9208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7443,7512,7573,7639,7704,7779,7849,7941,8028", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "7507,7568,7634,7699,7774,7844,7936,8023,8095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "55,56,57,58,59,60,61,249", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3981,4077,4180,4279,4377,4478,4576,20782", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "4072,4175,4274,4372,4473,4571,4682,20878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,391,502,599,695,808,937,1058,1189,1274,1374,1464,1564,1682,1802,1907,2034,2159,2289,2437,2558,2672,2791,2903,2994,3093,3206,3331,3425,3541,3647,3774,3908,4018,4115,4195,4293,4389,4496,4582,4668,4773,4859,4946,5049,5151,5246,5349,5435,5536,5634,5736,5863,5949,6049", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "164,276,386,497,594,690,803,932,1053,1184,1269,1369,1459,1559,1677,1797,1902,2029,2154,2284,2432,2553,2667,2786,2898,2989,3088,3201,3326,3420,3536,3642,3769,3903,4013,4110,4190,4288,4384,4491,4577,4663,4768,4854,4941,5044,5146,5241,5344,5430,5531,5629,5731,5858,5944,6044,6139"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9609,9723,9835,9945,10056,10153,10249,10362,10491,10612,10743,10828,10928,11018,11118,11236,11356,11461,11588,11713,11843,11991,12112,12226,12345,12457,12548,12647,12760,12885,12979,13095,13201,13328,13462,13572,13669,13749,13847,13943,14050,14136,14222,14327,14413,14500,14603,14705,14800,14903,14989,15090,15188,15290,15417,15503,15603", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "9718,9830,9940,10051,10148,10244,10357,10486,10607,10738,10823,10923,11013,11113,11231,11351,11456,11583,11708,11838,11986,12107,12221,12340,12452,12543,12642,12755,12880,12974,13090,13196,13323,13457,13567,13664,13744,13842,13938,14045,14131,14217,14322,14408,14495,14598,14700,14795,14898,14984,15085,15183,15285,15412,15498,15598,15693"}}]}]}