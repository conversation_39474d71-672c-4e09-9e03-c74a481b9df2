{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-74:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "55,56,57,58,59,60,61,249", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4146,4245,4347,4447,4545,4652,4758,21900", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "4240,4342,4442,4540,4647,4753,4869,21996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,499,682,768,856,935,1033,1128,1205,1272,1372,1472,1538,1607,1674,1745,1876,1995,2121,2192,2278,2354,2431,2534,2639,2703,2767,2820,2878,2926,2987,3052,3122,3188,3260,3330,3398,3464,3529,3595,3648,3710,3786,3862", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "286,494,677,763,851,930,1028,1123,1200,1267,1367,1467,1533,1602,1669,1740,1871,1990,2116,2187,2273,2349,2426,2529,2634,2698,2762,2815,2873,2921,2982,3047,3117,3183,3255,3325,3393,3459,3524,3590,3643,3705,3781,3857,3915"}, "to": {"startLines": "2,11,15,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,594,5748,5834,5922,6001,6099,6194,6271,6338,6438,6538,6604,6673,6740,6811,6942,7061,7187,7258,7344,7420,7497,7600,7705,7769,8531,8584,8642,8690,8751,8816,8886,8952,9024,9094,9162,9228,9293,9359,9412,9474,9550,9626", "endLines": "10,14,18,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,85,87,78,97,94,76,66,99,99,65,68,66,70,130,118,125,70,85,75,76,102,104,63,63,52,57,47,60,64,69,65,71,69,67,65,64,65,52,61,75,75,57", "endOffsets": "381,589,772,5829,5917,5996,6094,6189,6266,6333,6433,6533,6599,6668,6735,6806,6937,7056,7182,7253,7339,7415,7492,7595,7700,7764,7828,8579,8637,8685,8746,8811,8881,8947,9019,9089,9157,9223,9288,9354,9407,9469,9545,9621,9679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,294,417,537,638,734,847,985,1103,1256,1340,1443,1540,1641,1759,1877,1985,2124,2262,2414,2599,2734,2853,2975,3100,3198,3295,3416,3564,3665,3778,3892,4032,4177,4287,4393,4479,4575,4671,4793,4880,4966,5072,5154,5238,5339,5446,5537,5636,5724,5837,5938,6041,6165,6247,6365", "endColumns": "120,117,122,119,100,95,112,137,117,152,83,102,96,100,117,117,107,138,137,151,184,134,118,121,124,97,96,120,147,100,112,113,139,144,109,105,85,95,95,121,86,85,105,81,83,100,106,90,98,87,112,100,102,123,81,117,107", "endOffsets": "171,289,412,532,633,729,842,980,1098,1251,1335,1438,1535,1636,1754,1872,1980,2119,2257,2409,2594,2729,2848,2970,3095,3193,3290,3411,3559,3660,3773,3887,4027,4172,4282,4388,4474,4570,4666,4788,4875,4961,5067,5149,5233,5334,5441,5532,5631,5719,5832,5933,6036,6160,6242,6360,6468"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10094,10215,10333,10456,10576,10677,10773,10886,11024,11142,11295,11379,11482,11579,11680,11798,11916,12024,12163,12301,12453,12638,12773,12892,13014,13139,13237,13334,13455,13603,13704,13817,13931,14071,14216,14326,14432,14518,14614,14710,14832,14919,15005,15111,15193,15277,15378,15485,15576,15675,15763,15876,15977,16080,16204,16286,16404", "endColumns": "120,117,122,119,100,95,112,137,117,152,83,102,96,100,117,117,107,138,137,151,184,134,118,121,124,97,96,120,147,100,112,113,139,144,109,105,85,95,95,121,86,85,105,81,83,100,106,90,98,87,112,100,102,123,81,117,107", "endOffsets": "10210,10328,10451,10571,10672,10768,10881,11019,11137,11290,11374,11477,11574,11675,11793,11911,12019,12158,12296,12448,12633,12768,12887,13009,13134,13232,13329,13450,13598,13699,13812,13926,14066,14211,14321,14427,14513,14609,14705,14827,14914,15000,15106,15188,15272,15373,15480,15571,15670,15758,15871,15972,16075,16199,16281,16399,16507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,284,393,494,583,662,755,847,935,1020,1110,1187,1271,1351,1427,1509,1581", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,83,79,75,81,71,121", "endOffsets": "196,279,388,489,578,657,750,842,930,1015,1105,1182,1266,1346,1422,1504,1576,1698"}, "to": {"startLines": "65,66,67,68,69,124,125,239,240,242,243,245,246,247,248,250,251,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5192,5288,5371,5480,5581,9859,9938,21060,21152,21325,21410,21583,21660,21744,21824,22001,22083,22155", "endColumns": "95,82,108,100,88,78,92,91,87,84,89,76,83,79,75,81,71,121", "endOffsets": "5283,5366,5475,5576,5665,9933,10026,21147,21235,21405,21495,21655,21739,21819,21895,22078,22150,22272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,100", "endOffsets": "149,250"}, "to": {"startLines": "253,254", "startColumns": "4,4", "startOffsets": "22277,22376", "endColumns": "98,100", "endOffsets": "22371,22472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87d78cd91b4d183bfa1262f9176c8808\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,244", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "952,1056,1160,1268,1353,1454,1582,1668,1749,1841,1935,2032,2126,2226,2320,2416,2511,2603,2695,2776,2884,2991,3098,3207,3312,3426,3603,21500", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "1051,1155,1263,1348,1449,1577,1663,1744,1836,1930,2027,2121,2221,2315,2411,2506,2598,2690,2771,2879,2986,3093,3202,3307,3421,3598,3697,21578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,192,257,336,413,489,588,684", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "124,187,252,331,408,484,583,679,748"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7833,7907,7970,8035,8114,8191,8267,8366,8462", "endColumns": "73,62,64,78,76,75,98,95,68", "endOffsets": "7902,7965,8030,8109,8186,8262,8361,8457,8526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e314a50eb315442fe0afa641180e00d\\transformed\\material-1.11.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1115,1211,1290,1353,1448,1512,1581,1644,1718,1782,1838,1959,2017,2079,2135,2212,2351,2439,2519,2659,2739,2819,2968,3058,3139,3195,3251,3317,3396,3477,3565,3653,3732,3809,3891,3980,4081,4165,4257,4350,4451,4525,4617,4719,4771,4855,4921,5013,5101,5163,5227,5290,5360,5471,5576,5682,5781,5841,5901", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "270,351,431,516,618,714,819,952,1032,1110,1206,1285,1348,1443,1507,1576,1639,1713,1777,1833,1954,2012,2074,2130,2207,2346,2434,2514,2654,2734,2814,2963,3053,3134,3190,3246,3312,3391,3472,3560,3648,3727,3804,3886,3975,4076,4160,4252,4345,4446,4520,4612,4714,4766,4850,4916,5008,5096,5158,5222,5285,5355,5466,5571,5677,5776,5836,5896,5981"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "777,3702,3783,3863,3948,4050,4874,4979,5112,5670,9684,9780,10031,16512,16607,16671,16740,16803,16877,16941,16997,17118,17176,17238,17294,17371,17510,17598,17678,17818,17898,17978,18127,18217,18298,18354,18410,18476,18555,18636,18724,18812,18891,18968,19050,19139,19240,19324,19416,19509,19610,19684,19776,19878,19930,20014,20080,20172,20260,20322,20386,20449,20519,20630,20735,20841,20940,21000,21240", "endLines": "22,50,51,52,53,54,62,63,64,70,122,123,126,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,241", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,148,89,80,55,55,65,78,80,87,87,78,76,81,88,100,83,91,92,100,73,91,101,51,83,65,91,87,61,63,62,69,110,104,105,98,59,59,84", "endOffsets": "947,3778,3858,3943,4045,4141,4974,5107,5187,5743,9775,9854,10089,16602,16666,16735,16798,16872,16936,16992,17113,17171,17233,17289,17366,17505,17593,17673,17813,17893,17973,18122,18212,18293,18349,18405,18471,18550,18631,18719,18807,18886,18963,19045,19134,19235,19319,19411,19504,19605,19679,19771,19873,19925,20009,20075,20167,20255,20317,20381,20444,20514,20625,20730,20836,20935,20995,21055,21320"}}]}]}