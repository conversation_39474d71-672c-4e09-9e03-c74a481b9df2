package com.mdmusfikurrahaman.callrecordingapp.util

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import timber.log.Timber

/**
 * Utility class for handling permissions
 */
object PermissionUtils {
    
    /**
     * Check if all specified permissions are granted
     */
    fun hasPermissions(context: Context, permissions: List<String>): <PERSON><PERSON>an {
        return permissions.all { permission ->
            hasPermission(context, permission)
        }
    }
    
    /**
     * Check if a specific permission is granted
     */
    fun hasPermission(context: Context, permission: String): <PERSON><PERSON>an {
        return try {
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        } catch (e: Exception) {
            Timber.w(e, "Error checking permission: $permission")
            false
        }
    }
    
    /**
     * Get permissions that are not granted from the provided list
     */
    fun getMissingPermissions(context: Context, permissions: List<String>): List<String> {
        return permissions.filter { permission ->
            !hasPermission(context, permission)
        }
    }
    
    /**
     * Check if permission is available on current Android version
     */
    fun isPermissionAvailable(permission: String): <PERSON><PERSON><PERSON> {
        return when (permission) {
            android.Manifest.permission.POST_NOTIFICATIONS -> Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
            android.Manifest.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION -> Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
            android.Manifest.permission.FOREGROUND_SERVICE_MICROPHONE -> Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
            else -> true
        }
    }
}
