com.mdmusfikurrahaman.callrecordingapp-room-ktx-2.6.1-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01674ce1aea2017cb3b29df3cd41b3bd\transformed\room-ktx-2.6.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-fragment-2.7.6-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03959fa5a9aef612010f325a5f5415c1\transformed\navigation-fragment-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-activity-1.8.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05bfc679feb84f7960673e14a092f722\transformed\activity-1.8.0\res
com.mdmusfikurrahaman.callrecordingapp-drawerlayout-1.1.1-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fe02253847a043fcae5afbcc27c97f6\transformed\drawerlayout-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-navigation-ui-2.7.6-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125614bd4c72f93cfeffaa0b5947d611\transformed\navigation-ui-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-ktx-2.9.1-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\137fd311b6f2521592e86ef7d4099744\transformed\lifecycle-livedata-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-2.9.1-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c84042c82f293e7e69815da52c19631\transformed\lifecycle-livedata-core-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-viewpager2-1.1.0-beta02-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23f21d3c419ff137a435cdfe89d2284c\transformed\viewpager2-1.1.0-beta02\res
com.mdmusfikurrahaman.callrecordingapp-fragment-1.6.2-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\290852fed67b1f7ede9e4d6be3af45e0\transformed\fragment-1.6.2\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-views-helper-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29b25e9474ca7db2b6a6e7147143490b\transformed\emoji2-views-helper-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-startup-runtime-1.1.1-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39253521ce6f6b2816009a1502da1ed3\transformed\startup-runtime-1.1.1\res
com.mdmusfikurrahaman.callrecordingapp-constraintlayout-2.1.4-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43940a1b6e151f9713d4f2712d4ab6e8\transformed\constraintlayout-2.1.4\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-release-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\45c65098f87a82b2ced71dc910d12f54\transformed\savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-savedstate-ktx-1.3.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a34c0e5d021ef8bc7ea262a08830343\transformed\savedstate-ktx-1.3.0\res
com.mdmusfikurrahaman.callrecordingapp-room-runtime-2.6.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508bf7f7e673b134d3ddf3801053def3\transformed\room-runtime-2.6.1\res
com.mdmusfikurrahaman.callrecordingapp-activity-ktx-1.8.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d69932c91f0fef05914460b87ad306\transformed\activity-ktx-1.8.0\res
com.mdmusfikurrahaman.callrecordingapp-customview-poolingcontainer-1.0.0-16 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5828144479f57a357cc602468c3955bc\transformed\customview-poolingcontainer-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-1.6.1-17 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e1e4d034409f719670a3d1d52262c91\transformed\appcompat-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-runtime-release-18 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ee7e408228fe400cb0e8fd0c3f71164\transformed\lifecycle-runtime-release\res
com.mdmusfikurrahaman.callrecordingapp-core-1.16.0-19 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\663fb4dbb7e440a36499eafaa6fbda5d\transformed\core-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-material-1.11.0-20 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7473b31c75d2d339c0da8a1a3f9146d6\transformed\material-1.11.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-2.9.1-21 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7556c0ff2cefe1e8342821623d39f001\transformed\lifecycle-livedata-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-emoji2-1.2.0-22 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75840cc18dbfb209a457ec421d24c12f\transformed\emoji2-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-sqlite-framework-2.4.0-23 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\771cce08319481ccef920c4e5c93b6c1\transformed\sqlite-framework-2.4.0\res
com.mdmusfikurrahaman.callrecordingapp-cardview-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7972879ce704e341328075eb7c94ff66\transformed\cardview-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-ui-ktx-2.7.6-25 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b170b3dce601d0e2e77a421053829c3\transformed\navigation-ui-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-2.7.6-26 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bbd9ac083b2cb72ba4533977049ddf5\transformed\navigation-runtime-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-ktx-2.9.1-27 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d8b1567230f4f9d84206667cd766088\transformed\lifecycle-viewmodel-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-swiperefreshlayout-1.1.0-28 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f4e95de88c8795ab61f3756bf426f85\transformed\swiperefreshlayout-1.1.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-savedstate-release-29 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81961c3202906e68c6880cd14c4c1b00\transformed\lifecycle-viewmodel-savedstate-release\res
com.mdmusfikurrahaman.callrecordingapp-exoplayer-core-2.19.1-30 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e0b3af703173a357d5c452b0d31980\transformed\exoplayer-core-2.19.1\res
com.mdmusfikurrahaman.callrecordingapp-core-runtime-2.2.0-31 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85790714e108f535684af6d7ff89c8d7\transformed\core-runtime-2.2.0\res
com.mdmusfikurrahaman.callrecordingapp-databinding-adapters-8.9.2-32 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\864db2a38b1ada49a12ab4f904eeaf56\transformed\databinding-adapters-8.9.2\res
com.mdmusfikurrahaman.callrecordingapp-slidingpanelayout-1.2.0-33 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\892927a0cab796c0476c1ab09195e311\transformed\slidingpanelayout-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-2.7.6-34 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\89a534fb9477b24ba9e77b6102d9554c\transformed\navigation-common-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-appcompat-resources-1.6.1-35 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8e031c7c3109e62e465dfe831a17ba4f\transformed\appcompat-resources-1.6.1\res
com.mdmusfikurrahaman.callrecordingapp-annotation-experimental-1.4.1-36 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90e14be56c06b51a1131ce756dbd0276\transformed\annotation-experimental-1.4.1\res
com.mdmusfikurrahaman.callrecordingapp-window-1.0.0-37 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9506b02df8e2e558ee6641b395d2bc09\transformed\window-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-databinding-runtime-8.9.2-38 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a242d0e3fe05611829b9bf565c2d2230\transformed\databinding-runtime-8.9.2\res
com.mdmusfikurrahaman.callrecordingapp-core-viewtree-1.0.0-39 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a681391b1621fd08da49955106caed3d\transformed\core-viewtree-1.0.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-fragment-ktx-2.7.6-40 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a89022cf95ec4feb687ddd861b473905\transformed\navigation-fragment-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-livedata-core-ktx-2.9.1-41 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af92e979f9adeda4e43ee29d199099e8\transformed\lifecycle-livedata-core-ktx-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-tracing-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8a2b019ace5aca54dc238ff1947938c\transformed\tracing-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-media-1.6.0-43 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbbf53aeb58a5d429e474487ae97e95f\transformed\media-1.6.0\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-viewmodel-release-44 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5cb725166ce2049d007c478cd34ec67\transformed\lifecycle-viewmodel-release\res
com.mdmusfikurrahaman.callrecordingapp-fragment-ktx-1.6.2-45 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d7a50004433e3faf5d01891f7be7e6\transformed\fragment-ktx-1.6.2\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-service-2.9.1-46 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cead9067d77ad52a172ebd0b049dbc08\transformed\lifecycle-service-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-sqlite-2.4.0-47 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf975b981b84831bc57ef5c23ba962ba\transformed\sqlite-2.4.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-common-ktx-2.7.6-48 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1304ab331c40c828a08441e2836dbb2\transformed\navigation-common-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-profileinstaller-1.4.0-49 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3d4b97aaec003e310237b88b2564b22\transformed\profileinstaller-1.4.0\res
com.mdmusfikurrahaman.callrecordingapp-navigation-runtime-ktx-2.7.6-50 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da996110ab603543fbc3bfb15cf57a25\transformed\navigation-runtime-ktx-2.7.6\res
com.mdmusfikurrahaman.callrecordingapp-lifecycle-process-2.9.1-51 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e635fa146f82f49c4d31ffc09de88b29\transformed\lifecycle-process-2.9.1\res
com.mdmusfikurrahaman.callrecordingapp-recyclerview-1.3.2-52 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef1f7dd8d30bc2ef8e93356a5c134cb6\transformed\recyclerview-1.3.2\res
com.mdmusfikurrahaman.callrecordingapp-coordinatorlayout-1.2.0-53 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f56a1774045d70a99114b425fe27abf5\transformed\coordinatorlayout-1.2.0\res
com.mdmusfikurrahaman.callrecordingapp-core-ktx-1.16.0-54 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97b638c71545b7a061eb5555f7542f7\transformed\core-ktx-1.16.0\res
com.mdmusfikurrahaman.callrecordingapp-exoplayer-ui-2.19.1-55 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab9a8fce592b1bb05a0549bf6b2f96e\transformed\exoplayer-ui-2.19.1\res
com.mdmusfikurrahaman.callrecordingapp-transition-1.4.1-56 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb8fa1040e50668cfb5d01b2d1e6ab69\transformed\transition-1.4.1\res
com.mdmusfikurrahaman.callrecordingapp-pngs-57 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\pngs\release
com.mdmusfikurrahaman.callrecordingapp-resValues-58 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\generated\res\resValues\release
com.mdmusfikurrahaman.callrecordingapp-packageReleaseResources-59 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.mdmusfikurrahaman.callrecordingapp-packageReleaseResources-60 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.mdmusfikurrahaman.callrecordingapp-merged-not-compiled-resources-61 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\merged-not-compiled-resources\release
com.mdmusfikurrahaman.callrecordingapp-release-62 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\build\intermediates\merged_res\release\mergeReleaseResources
com.mdmusfikurrahaman.callrecordingapp-main-63 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\main\res
com.mdmusfikurrahaman.callrecordingapp-release-64 C:\Users\<USER>\AndroidStudioProjects\callrecordingapp\app\src\release\res
