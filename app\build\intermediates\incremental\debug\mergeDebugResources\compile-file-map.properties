#Sun Jul 20 16:55:11 BDT 2025
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_call_received_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_call_received_24.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_error_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_error_24.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_launcher_background.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_mic_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_mic_24.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_more_vert_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_more_vert_24.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_play_arrow_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_play_arrow_24.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_search_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search_24.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/drawable/ic_stop_24.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_stop_24.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/xml/accessibility_service_config.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_accessibility_service_config.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/xml/backup_rules.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/xml/data_extraction_rules.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.mdmusfikurrahaman.callrecordingapp-main-56\:/xml/file_paths.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-53\:/layout/activity_main.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-53\:/layout/item_recording.xml=C\:\\Users\\ASUS\\AndroidStudioProjects\\callrecordingapp\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_recording.xml.flat
