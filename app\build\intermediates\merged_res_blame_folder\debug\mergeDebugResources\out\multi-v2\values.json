{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-64:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "396,397,398,399,400,401,402,403,404", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "24108,24178,24240,24305,24369,24446,24511,24601,24685", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "24173,24235,24300,24364,24441,24506,24596,24680,24749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f73f3a5398eee140153949db3abbd04\\transformed\\media-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "95,96,100,299,329,723,725,726,731,733", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4424,4513,4725,17309,18938,44547,44723,44845,45107,45302", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "4508,4579,4793,17366,18993,44608,44840,44901,45168,45364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\811b1ce9077fecb6340e6d12cb4fa636\\transformed\\navigation-common-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "883,896,902,908,917", "startColumns": "4,4,4,4,4", "startOffsets": "50748,51387,51631,51878,52241", "endLines": "895,901,907,910,921", "endColumns": "24,24,24,24,24", "endOffsets": "51382,51626,51873,52006,52418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0caafe874001aa745e69129a07aec6ad\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "326", "startColumns": "4", "startOffsets": "18771", "endColumns": "49", "endOffsets": "18816"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\callrecordingapp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "82,97,98,99,101,102,103", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3632,4584,4631,4678,4798,4843,4888", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "3669,4626,4673,4720,4838,4883,4925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8b980f2eb9d27b8cfd48cb43a893c235\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "295,301", "startColumns": "4,4", "startOffsets": "17119,17424", "endColumns": "53,66", "endOffsets": "17168,17486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\62ec83f1185741b584d6d3f738481309\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "302,324", "startColumns": "4,4", "startOffsets": "17491,18657", "endColumns": "41,59", "endOffsets": "17528,18712"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\callrecordingapp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "93", "endOffsets": "145"}, "to": {"startLines": "734", "startColumns": "4", "startOffsets": "45369", "endColumns": "92", "endOffsets": "45457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5bd6aef088fbfce15a74175d5a5d4fa6\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "292", "startColumns": "4", "startOffsets": "16947", "endColumns": "65", "endOffsets": "17008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\02ea201e9bb3b55675877c0cb9193573\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "323", "startColumns": "4", "startOffsets": "18614", "endColumns": "42", "endOffsets": "18652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bc24f293396ce494ac1c8a020bc920ac\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "344", "startColumns": "4", "startOffsets": "20143", "endColumns": "82", "endOffsets": "20221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,35,40,47,48,49,50,51,52,57,58,59,60,61,62,63,64,65,66,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,213,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,266,270,274,278,282,286,290,294,295,301,312,316,320,324,328,332,336,340,344,348,352,356,367,372,377,382,393,401,411,415,419,423,426,442,468,503,532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1140,1190,1255,1312,1359,1414,1562,1800,1849,1910,1970,2026,2086,2256,2316,2369,2426,2481,2537,2594,2643,2694,2753,3040,3105,3163,3212,3260,3311,3368,3425,3487,3554,3625,3697,3741,3798,3854,3917,3990,4060,4119,4176,4223,4278,4323,4372,4427,4481,4531,4582,4636,4695,4745,4803,4859,4912,4975,5040,5103,5155,5215,5279,5345,5403,5475,5536,5606,5676,5741,5806,5877,5965,6063,6159,6233,6309,6383,6465,6551,6637,6723,6801,6889,6975,7045,7137,7215,7295,7373,7459,7541,7634,7712,7803,7884,7973,8076,8177,8261,8357,8454,8549,8642,8734,8827,8920,9013,9096,9183,9278,9371,9452,9547,9640,9717,9761,9802,9847,9895,9939,9982,10031,10078,10122,10178,10231,10273,10320,10368,10428,10466,10516,10560,10610,10662,10700,10747,10794,10835,10874,10912,10956,11004,11046,11084,11126,11180,11227,11264,11313,11355,11396,11437,11479,11522,11560,11596,11674,11752,12049,12319,12401,12483,12625,12703,12790,12875,12942,13005,13097,13189,13254,13317,13379,13450,13560,13671,13781,13848,13928,13999,14066,14151,14236,14299,14387,14451,14593,14693,14741,14884,14947,15009,15074,15145,15203,15261,15327,15391,15457,15509,15571,15647,15723,15777,16056,16280,16483,16689,16892,17107,17316,17513,17551,17905,18692,18933,19173,19430,19683,19936,20171,20418,20657,20901,21122,21317,21892,22183,22479,22782,23351,23885,24359,24570,24770,24946,25054,25630,26569,27745,28801", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,34,39,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,212,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,265,269,273,277,281,285,289,293,294,300,311,315,319,323,327,331,335,339,343,347,351,355,366,371,376,381,392,400,410,414,418,422,425,441,467,502,531,571", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1135,1185,1250,1307,1354,1409,1557,1795,1844,1905,1965,2021,2081,2251,2311,2364,2421,2476,2532,2589,2638,2689,2748,3035,3100,3158,3207,3255,3306,3363,3420,3482,3549,3620,3692,3736,3793,3849,3912,3985,4055,4114,4171,4218,4273,4318,4367,4422,4476,4526,4577,4631,4690,4740,4798,4854,4907,4970,5035,5098,5150,5210,5274,5340,5398,5470,5531,5601,5671,5736,5801,5872,5960,6058,6154,6228,6304,6378,6460,6546,6632,6718,6796,6884,6970,7040,7132,7210,7290,7368,7454,7536,7629,7707,7798,7879,7968,8071,8172,8256,8352,8449,8544,8637,8729,8822,8915,9008,9091,9178,9273,9366,9447,9542,9635,9712,9756,9797,9842,9890,9934,9977,10026,10073,10117,10173,10226,10268,10315,10363,10423,10461,10511,10555,10605,10657,10695,10742,10789,10830,10869,10907,10951,10999,11041,11079,11121,11175,11222,11259,11308,11350,11391,11432,11474,11517,11555,11591,11669,11747,12044,12314,12396,12478,12620,12698,12785,12870,12937,13000,13092,13184,13249,13312,13374,13445,13555,13666,13776,13843,13923,13994,14061,14146,14231,14294,14382,14446,14588,14688,14736,14879,14942,15004,15069,15140,15198,15256,15322,15386,15452,15504,15566,15642,15718,15772,16051,16275,16478,16684,16887,17102,17311,17508,17546,17900,18687,18928,19168,19425,19678,19931,20166,20413,20652,20896,21117,21312,21887,22178,22474,22777,23346,23880,24354,24565,24765,24941,25049,25625,26564,27740,28796,30160"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,33,34,35,37,42,49,50,51,52,53,54,59,60,61,62,63,64,65,66,67,68,75,76,77,78,79,85,86,87,88,89,90,91,92,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,330,331,334,338,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,549,554,558,562,566,570,574,578,582,583,589,600,604,608,612,616,620,624,628,632,636,640,644,655,660,665,670,681,689,699,703,707,744,763,922,948,1004,1033", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1185,1235,1360,1417,1464,1575,1723,1961,2010,2071,2131,2187,2247,2417,2477,2530,2587,2642,2698,2755,2804,2855,2914,3201,3266,3324,3373,3421,3805,3862,3919,3981,4048,4119,4191,4235,5418,5474,5537,5610,5680,5739,5796,5843,5898,5943,5992,6047,6101,6151,6202,6256,6315,6365,6423,6479,6532,6595,6660,6723,6775,6835,6899,6965,7023,7095,7156,7226,7296,7361,7426,8861,8949,9047,9143,9217,9293,9367,9449,9535,9621,9707,9785,9873,9959,10029,10121,10199,10279,10357,10443,10525,10618,10696,10787,10868,10957,11060,11161,11245,11341,11438,11533,11626,11718,11811,11904,11997,12080,12167,12262,12355,12436,12531,12624,15011,15055,15096,15141,15189,15233,15276,15325,15372,15416,15472,15525,15567,15614,15662,15722,15760,15810,15854,15904,15956,15994,16041,16088,16129,16168,16206,16250,16298,16340,16378,16420,16474,16521,16558,16607,16649,16690,16731,16773,16816,16854,18998,19076,19297,19594,21976,22058,22140,22282,22360,22447,22532,22599,22662,22754,22846,22911,22974,23036,23107,23217,23328,23438,23505,23585,23656,23723,23808,23893,23956,24044,24754,24896,24996,25044,25187,25250,25312,25377,25448,25506,25564,25630,25694,25760,25812,25874,25950,26026,34754,35033,35257,35460,35666,35869,36084,36293,36490,36528,36882,37669,37910,38150,38407,38660,38913,39148,39395,39634,39878,40099,40294,40869,41160,41456,41759,42328,42862,43336,43547,43747,45905,46517,52423,53362,55378,56434", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,33,34,35,41,48,49,50,51,52,53,58,59,60,61,62,63,64,65,66,67,74,75,76,77,78,79,85,86,87,88,89,90,91,92,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,330,331,337,341,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,553,557,561,565,569,573,577,581,582,588,599,603,607,611,615,619,623,627,631,635,639,643,654,659,664,669,680,688,698,702,706,710,746,778,947,982,1032,1072", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,87,97,95,73,75,73,81,85,85,85,77,87,85,69,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1180,1230,1295,1412,1459,1514,1718,1956,2005,2066,2126,2182,2242,2412,2472,2525,2582,2637,2693,2750,2799,2850,2909,3196,3261,3319,3368,3416,3467,3857,3914,3976,4043,4114,4186,4230,4287,5469,5532,5605,5675,5734,5791,5838,5893,5938,5987,6042,6096,6146,6197,6251,6310,6360,6418,6474,6527,6590,6655,6718,6770,6830,6894,6960,7018,7090,7151,7221,7291,7356,7421,7492,8944,9042,9138,9212,9288,9362,9444,9530,9616,9702,9780,9868,9954,10024,10116,10194,10274,10352,10438,10520,10613,10691,10782,10863,10952,11055,11156,11240,11336,11433,11528,11621,11713,11806,11899,11992,12075,12162,12257,12350,12431,12526,12619,12696,15050,15091,15136,15184,15228,15271,15320,15367,15411,15467,15520,15562,15609,15657,15717,15755,15805,15849,15899,15951,15989,16036,16083,16124,16163,16201,16245,16293,16335,16373,16415,16469,16516,16553,16602,16644,16685,16726,16768,16811,16849,16885,19071,19149,19589,19859,22053,22135,22277,22355,22442,22527,22594,22657,22749,22841,22906,22969,23031,23102,23212,23323,23433,23500,23580,23651,23718,23803,23888,23951,24039,24103,24891,24991,25039,25182,25245,25307,25372,25443,25501,25559,25625,25689,25755,25807,25869,25945,26021,26075,35028,35252,35455,35661,35864,36079,36288,36485,36523,36877,37664,37905,38145,38402,38655,38908,39143,39390,39629,39873,40094,40289,40864,41155,41451,41754,42323,42857,43331,43542,43742,43918,46008,47088,53357,54533,56429,57793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "246,247,248,293,294,328,355,356,359,360,364,424,426,490,493,509,510,518,521,522,523,526,527,528,533,711,714", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14824,14898,14956,17013,17064,18885,20882,20947,21211,21277,21578,26134,26237,31039,31197,32396,32446,33034,33166,33212,33254,33403,33450,33486,33774,43923,44034", "endLines": "246,247,248,293,294,328,355,356,359,360,364,424,426,490,493,509,510,518,521,522,523,526,527,528,535,713,717", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "14893,14951,15006,17059,17114,18933,20942,20996,21272,21373,21631,26181,26292,31096,31246,32441,32495,33075,33207,33249,33289,33445,33481,33571,33881,44029,44224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\43451c4cf576c4ca58d679413c1217ee\\transformed\\navigation-runtime-2.8.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "300,737,911,914", "startColumns": "4,4,4,4", "startOffsets": "17371,45601,52011,52126", "endLines": "300,743,913,916", "endColumns": "52,24,24,24", "endOffsets": "17419,45900,52121,52236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "332,429,430,431,432,433,434,435,436,437,438,441,442,443,444,445,446,447,448,449,450,451,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,536,546", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19154,26598,26686,26772,26853,26937,27006,27071,27154,27260,27346,27466,27520,27589,27650,27719,27808,27903,27977,28074,28167,28265,28414,28505,28593,28689,28787,28851,28919,29006,29100,29167,29239,29311,29412,29521,29597,29666,29714,29780,29844,29918,29975,30032,30104,30154,30208,30279,30350,30420,30489,30547,30623,30694,30768,30854,30904,30974,33886,34601", "endLines": "332,429,430,431,432,433,434,435,436,437,440,441,442,443,444,445,446,447,448,449,450,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,545,548", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19222,26681,26767,26848,26932,27001,27066,27149,27255,27341,27461,27515,27584,27645,27714,27803,27898,27972,28069,28162,28260,28409,28500,28588,28684,28782,28846,28914,29001,29095,29162,29234,29306,29407,29516,29592,29661,29709,29775,29839,29913,29970,30027,30099,30149,30203,30274,30345,30415,30484,30542,30618,30689,30763,30849,30899,30969,31034,34596,34749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e72416f097544af7c7b4a70ec06ad5f5\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "291,303,327,835,840", "startColumns": "4,4,4,4,4", "startOffsets": "16890,17533,18821,49580,49750", "endLines": "291,303,327,839,843", "endColumns": "56,64,63,24,24", "endOffsets": "16942,17593,18880,49745,49894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\64daf04d4922015e37dc200ad2ff335c\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "36,146,147,148,149,150,151,296,983", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1519,7497,7556,7604,7660,7735,7811,17173,54538", "endLines": "36,146,147,148,149,150,151,296,1003", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1570,7551,7599,7655,7730,7806,7878,17234,55373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\749be79bf2ac10b23bb300822828318a\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "322", "startColumns": "4", "startOffsets": "18571", "endColumns": "42", "endOffsets": "18609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "529,530", "startColumns": "4,4", "startOffsets": "33576,33632", "endColumns": "55,54", "endOffsets": "33627,33682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "32,80,81,83,84,93,94,104,105,106,107,108,109,110,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,297,298,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,333,347,348,349,350,351,352,353,524,718,719,724,727,732,735,736,747,753,779,814,844,877", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1300,3472,3544,3674,3739,4292,4361,4930,5000,5068,5140,5210,5271,5345,7883,7944,8005,8067,8131,8193,8254,8322,8422,8482,8548,8621,8690,8747,8799,12701,12773,12849,12914,12973,13032,13092,13152,13212,13272,13332,13392,13452,13512,13572,13632,13691,13751,13811,13871,13931,13991,14051,14111,14171,14231,14291,14350,14410,14470,14529,14588,14647,14706,14765,17239,17274,17598,17653,17716,17771,17829,17885,17943,18004,18067,18124,18175,18233,18283,18344,18401,18467,18501,18536,19227,20329,20396,20468,20537,20606,20680,20752,33294,44229,44346,44613,44906,45173,45462,45534,46013,46216,47093,48899,49899,50581", "endLines": "32,80,81,83,84,93,94,104,105,106,107,108,109,110,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,297,298,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,333,347,348,349,350,351,352,353,524,718,722,724,730,732,735,736,752,762,813,834,876,882", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1355,3539,3627,3734,3800,4356,4419,4995,5063,5135,5205,5266,5340,5413,7939,8000,8062,8126,8188,8249,8317,8417,8477,8543,8616,8685,8742,8794,8856,12768,12844,12909,12968,13027,13087,13147,13207,13267,13327,13387,13447,13507,13567,13627,13686,13746,13806,13866,13926,13986,14046,14106,14166,14226,14286,14345,14405,14465,14524,14583,14642,14701,14760,14819,17269,17304,17648,17711,17766,17824,17880,17938,17999,18062,18119,18170,18228,18278,18339,18396,18462,18496,18531,18566,19292,20391,20463,20532,20601,20675,20747,20835,33360,44341,44542,44718,45102,45297,45529,45596,46211,46512,48894,49575,50576,50743"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1182d5d1bbc868453e478891bcc4e878\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "325", "startColumns": "4", "startOffsets": "18717", "endColumns": "53", "endOffsets": "18766"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\callrecordingapp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "52,8,1,49,45,58,57,40,42,41,65,63,66,62,64,59,31,56,55,44,29,32,37,16,15,20,19,18,17,14,13,12,11,51,36,50,5,26,25,24,23,48,30,4,39,38,33,43", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2832,243,16,2656,2527,3260,3193,2257,2361,2299,3676,3546,3752,3478,3616,3403,1926,2960,2892,2493,1799,1977,2139,946,872,1307,1233,1128,1046,762,682,588,508,2766,2101,2708,143,1661,1582,1504,1432,2592,1861,97,2217,2179,2028,2457", "endColumns": "39,238,50,51,41,142,66,41,95,61,75,69,65,67,59,53,50,232,67,33,61,50,39,99,73,92,73,104,81,109,79,93,79,65,37,57,63,111,78,77,71,63,64,45,39,37,50,35", "endOffsets": "2867,477,62,2703,2564,3398,3255,2294,2452,2356,3747,3611,3813,3541,3671,3452,1972,3188,2955,2522,1856,2023,2174,1041,941,1395,1302,1228,1123,867,757,677,583,2827,2134,2761,202,1768,1656,1577,1499,2651,1921,138,2252,2212,2074,2488"}, "to": {"startLines": "342,343,345,346,354,357,358,361,362,363,365,366,367,368,369,423,425,427,428,491,492,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,511,512,513,514,515,516,517,519,520,525,531,532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19864,19904,20226,20277,20840,21001,21144,21378,21420,21516,21636,21712,21782,21848,21916,26080,26186,26297,26530,31101,31135,31251,31302,31342,31442,31516,31609,31683,31788,31870,31980,32060,32154,32234,32300,32338,32500,32564,32676,32755,32833,32905,32969,33080,33126,33365,33687,33738", "endColumns": "39,238,50,51,41,142,66,41,95,61,75,69,65,67,59,53,50,232,67,33,61,50,39,99,73,92,73,104,81,109,79,93,79,65,37,57,63,111,78,77,71,63,64,45,39,37,50,35", "endOffsets": "19899,20138,20272,20324,20877,21139,21206,21415,21511,21573,21707,21777,21843,21911,21971,26129,26232,26525,26593,31130,31192,31297,31337,31437,31511,31604,31678,31783,31865,31975,32055,32149,32229,32295,32333,32391,32559,32671,32750,32828,32900,32964,33029,33121,33161,33398,33733,33769"}}]}]}