{"logs": [{"outputFile": "com.mdmusfikurrahaman.callrecordingapp-mergeDebugResources-64:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f0d934159a745b77ce5e2c937655ae7\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4848,4939,5034,5141,5221,5306,5407,5516,5611,5714,5801,5912,6011,6116,6239,6319,6425", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4843,4934,5029,5136,5216,5301,5402,5511,5606,5709,5796,5907,6006,6111,6234,6314,6420,6514"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6036,6165,6284,6400,6528,6627,6722,6834,6986,7107,7260,7344,7452,7550,7649,7761,7885,7998,8144,8287,8421,8586,8716,8868,9025,9154,9253,9348,9464,9588,9692,9811,9921,10067,10215,10325,10433,10508,10613,10718,10829,10920,11015,11122,11202,11287,11388,11497,11592,11695,11782,11893,11992,12097,12220,12300,12406", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "6160,6279,6395,6523,6622,6717,6829,6981,7102,7255,7339,7447,7545,7644,7756,7880,7993,8139,8282,8416,8581,8711,8863,9020,9149,9248,9343,9459,9583,9687,9806,9916,10062,10210,10320,10428,10503,10608,10713,10824,10915,11010,11117,11197,11282,11383,11492,11587,11690,11777,11888,11987,12092,12215,12295,12401,12495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\060f7bae30cd6eaca84919e959bbcf8e\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,90", "endOffsets": "135,226"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "13523,13608", "endColumns": "84,90", "endOffsets": "13603,13694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\494bfdab15dca442fa2e05e43f86b4ff\\transformed\\exoplayer-ui-2.19.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1456,1551,1623,1696,1756,1826,1943,2057,2177,2256,2348,2416,2502,2588,2673,2742,2805,2858,2916,2964,3025,3087,3158,3220,3282,3341,3408,3474,3537,3604,3658,3720,3796,3872", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1451,1546,1618,1691,1751,1821,1938,2052,2172,2251,2343,2411,2497,2583,2668,2737,2800,2853,2911,2959,3020,3082,3153,3215,3277,3336,3403,3469,3532,3599,3653,3715,3791,3867,3920"}, "to": {"startLines": "2,11,15,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,360,579,1967,2060,2153,2243,2344,2447,2533,2597,2692,2787,2859,2932,2992,3062,3179,3293,3413,3492,3584,3652,3738,3824,3909,3978,4738,4791,4849,4897,4958,5020,5091,5153,5215,5274,5341,5407,5470,5537,5591,5653,5729,5805", "endLines": "10,14,18,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "355,574,776,2055,2148,2238,2339,2442,2528,2592,2687,2782,2854,2927,2987,3057,3174,3288,3408,3487,3579,3647,3733,3819,3904,3973,4036,4786,4844,4892,4953,5015,5086,5148,5210,5269,5336,5402,5465,5532,5586,5648,5724,5800,5853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f6b081858863bf6ac83d3e56dce9679\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,294,388,485,571,653,749,836,922,1012,1105,1182,1257,1330,1402,1483,1551", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,74,72,71,80,67,119", "endOffsets": "199,289,383,480,566,648,744,831,917,1007,1100,1177,1252,1325,1397,1478,1546,1666"}, "to": {"startLines": "26,27,28,29,30,82,83,141,142,143,144,145,146,147,148,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1501,1600,1690,1784,1881,5858,5940,12500,12587,12673,12763,12856,12933,13008,13081,13254,13335,13403", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,74,72,71,80,67,119", "endOffsets": "1595,1685,1779,1876,1962,5935,6031,12582,12668,12758,12851,12928,13003,13076,13148,13330,13398,13518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3bbdb89120b629f45d0a6e4e4788a826\\transformed\\core-1.16.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "19,20,21,22,23,24,25,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "781,884,987,1089,1195,1293,1393,13153", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "879,982,1084,1190,1288,1388,1496,13249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37884121572ee391713c499f6dfa99b6\\transformed\\exoplayer-core-2.19.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4041,4112,4183,4253,4320,4398,4475,4575,4669", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "4107,4178,4248,4315,4393,4470,4570,4664,4733"}}]}]}