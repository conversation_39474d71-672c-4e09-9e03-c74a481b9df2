<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Call Type Icon -->
        <ImageView
            android:id="@+id/callTypeIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_call_received_24"
            app:tint="?attr/colorPrimary"
            tools:src="@drawable/ic_call_received_24" />

        <!-- Recording Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Contact Name / Phone Number -->
            <TextView
                android:id="@+id/contactNameText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceBodyLarge"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold"
                tools:text="John Doe" />

            <!-- Phone Number (if contact name exists) -->
            <TextView
                android:id="@+id/phoneNumberText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="?attr/textAppearanceBodyMedium"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:visibility="gone"
                tools:text="+880 1234 567890"
                tools:visibility="visible" />

            <!-- Date and Duration -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/dateText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="আজ, ১২:৩০ PM" />

                <TextView
                    android:id="@+id/durationText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceBodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="২:৩৫" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:orientation="horizontal">

            <!-- Play Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/playButton"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                app:icon="@drawable/ic_play_arrow_24"
                app:iconTint="?attr/colorPrimary" />

            <!-- More Options Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/moreButton"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                app:icon="@drawable/ic_more_vert_24"
                app:iconTint="?attr/colorOnSurfaceVariant" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
